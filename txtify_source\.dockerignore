# Ignore Python cache files
__pycache__/
*.pyc
*.pyo
*.pyd
.pytest_cache/

# Ignore virtual environments
env/
venv/
.env
*.env

# Ignore Docker-related files
# Dockerfile

# Ignore log files
logs/
*.log

# Ignore other unnecessary files
.DS_Store
.idea/
.vscode/
*.swp

# Ignore git-related files and directories
.git/
.gitignore
.github/

# Ignore compiled files
*.so
*.o
*.a
*.out
*.exe

# Logs and databases
*.db

# VS Code specific
.vscode/

# Random test
test.txt

# Current Tests directory
tests/

# MacOS-specific files
.DS_Store

# Poetry and dependency management
# poetry.lock
.mypy_cache/
.ruff_cache/
# pyproject.toml



# extra
__pycache__/
*.py[cod]
*$py.class
.git/
.github/
.pytest_cache/
test_data/
tests/
docs/
*.md