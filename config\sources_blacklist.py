# قائمة المصادر المعطلة أو المحظورة
# يتم تحديث هذه القائمة تلقائياً بناءً على أخطاء HTTP

# مصادر معطلة بشكل دائم (404, 410)
PERMANENTLY_DISABLED_SOURCES = {
    "https://www.arageek.com/tech/gaming",  # 404 Not Found
    "https://www.tech-wd.com/wd/category/games",  # 404 Not Found
    "https://www.resetera.com/forums/gaming-forum.4/",  # 404 Not Found
}

# مصادر محظورة مؤقتاً (403, 429)
TEMPORARILY_BLOCKED_SOURCES = {
    "https://www.gamestop.com/collection/new-releases",  # 403 Forbidden
    "https://www.neogaf.com/forums/gaming.2/",  # 403 Forbidden
}

# مصادر بطيئة تحتاج timeout أطول
SLOW_SOURCES = {
    "https://www.reddit.com/r/gamingnews/",
    "https://www.reddit.com/r/Games/",
    "https://www.reddit.com/r/gaming/",
    "https://en.wikipedia.org/wiki/2025_in_video_games",
}

# مصادر سريعة يمكن استخدام timeout أقصر
FAST_SOURCES = {
    "https://ign.com",
    "https://www.gamespot.com",
    "https://www.polygon.com",
    "https://pcgamesn.com",
}

# مصادر تتطلب User-Agent خاص
SPECIAL_USER_AGENT_SOURCES = {
    "https://www.metacritic.com": "Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)",
    "https://opencritic.com": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:91.0) Gecko/20100101 Firefox/91.0",
}

def is_source_disabled(url: str) -> bool:
    """تحقق من كون المصدر معطل"""
    return url in PERMANENTLY_DISABLED_SOURCES or url in TEMPORARILY_BLOCKED_SOURCES

def get_timeout_for_source(url: str) -> int:
    """الحصول على timeout مناسب للمصدر"""
    if url in SLOW_SOURCES:
        return 60
    elif url in FAST_SOURCES:
        return 15
    else:
        return 30

def get_user_agent_for_source(url: str) -> str:
    """الحصول على User-Agent مناسب للمصدر"""
    for source_url, user_agent in SPECIAL_USER_AGENT_SOURCES.items():
        if source_url in url:
            return user_agent
    return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
