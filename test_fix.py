#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للتأكد من إصلاح المشاكل
"""

import asyncio
import sys
import os
from datetime import datetime

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger

class QuickFixTester:
    """فئة اختبار سريع للإصلاحات"""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = datetime.now()
    
    async def run_quick_tests(self):
        """تشغيل اختبار سريع"""
        print("🚀 بدء اختبار سريع للإصلاحات...\n")
        
        tests = [
            ("Import Test", self.test_imports),
            ("Class Initialization", self.test_class_init),
            ("Method Existence", self.test_methods_exist),
            ("Basic Functionality", self.test_basic_functionality)
        ]
        
        for test_name, test_func in tests:
            try:
                print(f"🔍 اختبار {test_name}...")
                result = await test_func()
                self.test_results[test_name] = result
                
                if result['success']:
                    print(f"✅ {test_name}: نجح")
                    if 'details' in result:
                        for key, value in result['details'].items():
                            print(f"   • {key}: {value}")
                else:
                    print(f"❌ {test_name}: فشل - {result.get('error', 'خطأ غير محدد')}")
                
                print()
                
            except Exception as e:
                print(f"❌ {test_name}: خطأ في الاختبار - {e}\n")
                self.test_results[test_name] = {'success': False, 'error': str(e)}
        
        await self.generate_test_report()
    
    async def test_imports(self):
        """اختبار استيراد الوحدات"""
        try:
            # اختبار استيراد الوحدات الأساسية
            from modules.content_generator import ContentGenerator
            from modules.web_scraper import WebScraper
            from modules.youtube_analyzer import YouTubeAnalyzer
            
            return {
                'success': True,
                'message': 'جميع الوحدات تم استيرادها بنجاح',
                'details': {
                    'ContentGenerator': 'متوفر',
                    'WebScraper': 'متوفر',
                    'YouTubeAnalyzer': 'متوفر'
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_class_init(self):
        """اختبار تهيئة الكلاسات"""
        try:
            from modules.content_generator import ContentGenerator
            from modules.web_scraper import WebScraper
            
            # اختبار تهيئة الكلاسات
            content_gen = ContentGenerator()
            scraper = WebScraper()
            
            return {
                'success': True,
                'message': 'تم تهيئة الكلاسات بنجاح',
                'details': {
                    'ContentGenerator': type(content_gen).__name__,
                    'WebScraper': type(scraper).__name__,
                    'تهيئة ناجحة': True
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_methods_exist(self):
        """اختبار وجود الدوال المطلوبة"""
        try:
            from modules.content_generator import ContentGenerator
            from modules.web_scraper import WebScraper
            
            content_gen = ContentGenerator()
            scraper = WebScraper()
            
            # فحص وجود الدوال
            methods_check = {
                'ContentGenerator.generate_article': hasattr(content_gen, 'generate_article'),
                'WebScraper.extract_articles': hasattr(scraper, 'extract_articles'),
                'WebScraper.extract_articles_from_site': hasattr(scraper, 'extract_articles_from_site')
            }
            
            all_exist = all(methods_check.values())
            
            return {
                'success': all_exist,
                'message': 'فحص وجود الدوال المطلوبة',
                'details': methods_check
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_basic_functionality(self):
        """اختبار الوظائف الأساسية"""
        try:
            from modules.content_generator import ContentGenerator
            from modules.web_scraper import WebScraper
            
            content_gen = ContentGenerator()
            scraper = WebScraper()
            
            # اختبار توليد محتوى بسيط
            test_content = {
                'title': 'اختبار توليد المحتوى',
                'content': 'هذا محتوى اختبار للتأكد من عمل النظام',
                'summary': 'اختبار بسيط',
                'url': 'test://example',
                'source': 'Test Source'
            }
            
            generated = content_gen.generate_article(test_content, 'news')
            
            # اختبار استخراج مقالات (محاكاة)
            test_query = "gaming news"
            articles = scraper.extract_articles(test_query, "google_search")
            
            return {
                'success': True,
                'message': 'الوظائف الأساسية تعمل',
                'details': {
                    'توليد المحتوى': 'نجح' if generated else 'فشل',
                    'استخراج المقالات': 'نجح' if articles else 'فشل',
                    'عدد المقالات المستخرجة': len(articles) if articles else 0
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def generate_test_report(self):
        """إنشاء تقرير الاختبار"""
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results.values() if result.get('success', False))
        success_rate = (successful_tests / total_tests) * 100 if total_tests > 0 else 0
        
        duration = (datetime.now() - self.start_time).total_seconds()
        
        print("\n" + "="*60)
        print("📊 تقرير اختبار الإصلاحات السريع")
        print("="*60)
        
        print(f"🎯 معدل النجاح: {success_rate:.1f}% ({successful_tests}/{total_tests})")
        print(f"⏱️ مدة الاختبار: {duration:.1f} ثانية")
        print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        print("\n📋 النتائج:")
        for test_name, result in self.test_results.items():
            status = "✅ نجح" if result.get('success', False) else "❌ فشل"
            print(f"  • {test_name}: {status}")
            
            if result.get('success') and 'details' in result:
                for key, value in result['details'].items():
                    print(f"    - {key}: {value}")
        
        # تقييم عام
        if success_rate == 100:
            grade = "A+ ممتاز"
            message = "🎉 جميع الإصلاحات تعمل بنجاح!"
        elif success_rate >= 75:
            grade = "A جيد جداً"
            message = "👍 معظم الإصلاحات تعمل بشكل جيد"
        elif success_rate >= 50:
            grade = "B مقبول"
            message = "⚠️ بعض المشاكل تحتاج إصلاح"
        else:
            grade = "C يحتاج عمل"
            message = "🔧 يحتاج إلى مراجعة شاملة"
        
        print(f"\n🏆 التقييم: {grade}")
        print(f"💬 الحالة: {message}")
        
        if success_rate == 100:
            print("\n🚀 النظام جاهز للتشغيل!")
            print("يمكنك الآن تشغيل: python main.py")
        else:
            print("\n🔧 يحتاج إلى مراجعة الأخطاء المذكورة أعلاه")

async def main():
    """الدالة الرئيسية"""
    tester = QuickFixTester()
    await tester.run_quick_tests()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⌨️ تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
