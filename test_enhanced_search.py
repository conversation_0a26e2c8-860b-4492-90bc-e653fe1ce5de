#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التحسينات الجديدة لنظام البحث واكتشاف الأخبار
"""

import sys
import os
import asyncio
from datetime import datetime

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.content_scraper import ContentScraper
from modules.logger import logger

async def test_enhanced_search():
    """اختبار نظام البحث المحسن"""
    
    print("🔍 بدء اختبار نظام البحث المحسن...")
    
    try:
        # إنشاء مستخرج المحتوى
        scraper = ContentScraper()
        
        # اختبار استعلامات بحث مختلفة
        test_queries = [
            "gaming news today",
            "video game updates January 2025",
            "new game releases this week",
            "latest gaming announcements",
            "indie games news recent",
            "AAA games updates 2025"
        ]
        
        total_articles = 0
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n{'='*60}")
            print(f"🎮 اختبار {i}: البحث عن '{query}'")
            print(f"{'='*60}")
            
            # البحث المحسن
            articles = scraper.search_and_extract_articles(query, num_results=8)
            
            if articles:
                print(f"✅ تم العثور على {len(articles)} مقال")
                total_articles += len(articles)
                
                # عرض تفاصيل أفضل 3 مقالات
                for j, article in enumerate(articles[:3], 1):
                    print(f"\n📰 المقال {j}:")
                    print(f"   العنوان: {article.get('title', 'غير محدد')[:80]}...")
                    print(f"   المصدر: {article.get('source_url', 'غير محدد')}")
                    print(f"   جودة المحتوى: {article.get('content_quality', 0)}/10")
                    print(f"   تاريخ النشر: {article.get('published_date', 'غير محدد')}")
                    print(f"   طول المحتوى: {len(article.get('content', ''))} حرف")
                    
                    # فحص الكلمات المفتاحية
                    keywords = article.get('keywords', [])
                    if keywords:
                        print(f"   الكلمات المفتاحية: {', '.join(keywords[:5])}")
            else:
                print(f"❌ لم يتم العثور على مقالات للاستعلام: {query}")
            
            # تأخير بين الاختبارات
            await asyncio.sleep(2)
        
        print(f"\n{'='*60}")
        print(f"📊 ملخص النتائج:")
        print(f"   إجمالي الاستعلامات: {len(test_queries)}")
        print(f"   إجمالي المقالات: {total_articles}")
        print(f"   متوسط المقالات لكل استعلام: {total_articles/len(test_queries):.1f}")
        print(f"{'='*60}")
        
        # اختبار RSS feeds
        print(f"\n🔍 اختبار RSS feeds...")
        rss_articles = scraper.extract_from_rss_feeds()
        
        if rss_articles:
            print(f"✅ تم جمع {len(rss_articles)} مقال من RSS feeds")
            
            # عرض أفضل 3 مقالات من RSS
            for i, article in enumerate(rss_articles[:3], 1):
                print(f"\n📡 RSS المقال {i}:")
                print(f"   العنوان: {article.get('title', 'غير محدد')[:80]}...")
                print(f"   المصدر: {article.get('source_url', 'غير محدد')}")
                print(f"   تاريخ النشر: {article.get('published_date', 'غير محدد')}")
        else:
            print(f"❌ لم يتم العثور على مقالات في RSS feeds")
        
        print(f"\n🎉 انتهى الاختبار بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

def test_content_quality():
    """اختبار نظام تقييم جودة المحتوى"""
    print(f"\n🔍 اختبار نظام تقييم جودة المحتوى...")
    
    scraper = ContentScraper()
    
    test_contents = [
        {
            "name": "محتوى عالي الجودة",
            "content": """
            This is a comprehensive gaming article about the latest updates in the gaming industry.
            The article covers new game releases, including titles from major companies like Nintendo,
            Sony, and Microsoft. It discusses gameplay mechanics, graphics improvements, and 
            multiplayer features. The content is detailed and provides valuable insights for gamers.
            """
        },
        {
            "name": "محتوى متوسط الجودة",
            "content": """
            Gaming news update. New game coming soon. Graphics are good.
            Players will enjoy the gameplay. More details later.
            """
        },
        {
            "name": "محتوى منخفض الجودة",
            "content": "Game good. Play now."
        }
    ]
    
    for test in test_contents:
        quality_score = scraper._assess_content_quality(test["content"])
        print(f"   {test['name']}: {quality_score}/10")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار التحسينات الجديدة لنظام البحث...")
    
    # اختبار جودة المحتوى
    test_content_quality()
    
    # اختبار البحث المحسن
    asyncio.run(test_enhanced_search())

if __name__ == "__main__":
    main()
