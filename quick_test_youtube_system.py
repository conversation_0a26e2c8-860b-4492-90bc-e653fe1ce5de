#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار سريع لنظام YouTube المتقدم
"""

import asyncio
import sys
import os

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.advanced_youtube_analyzer import AdvancedYouTubeAnalyzer
from modules.video_approval_system import VideoApprovalSystem
from modules.database import db

async def quick_test():
    """اختبار سريع للنظام"""
    
    print("🚀 اختبار سريع لنظام YouTube المتقدم")
    print("=" * 50)
    
    try:
        # 1. اختبار تهيئة المكونات
        print("\n1️⃣ اختبار تهيئة المكونات...")
        
        # محلل YouTube المتقدم
        youtube_analyzer = AdvancedYouTubeAnalyzer()
        print(f"   ✅ محلل YouTube: {len(youtube_analyzer.priority_channels)} قناة محددة")
        
        # نظام الموافقة
        approval_system = VideoApprovalSystem()
        print("   ✅ نظام الموافقة: جاهز")
        
        # قاعدة البيانات
        stats = db.get_stats_summary(1)
        print("   ✅ قاعدة البيانات: متصلة")
        
        # 2. اختبار القنوات
        print("\n2️⃣ اختبار القنوات المحددة...")
        
        for i, channel in enumerate(youtube_analyzer.priority_channels[:3], 1):  # أول 3 قنوات
            print(f"   {i}. {channel['name']} ({channel['language']})")
            print(f"      URL: {channel['url']}")
            print(f"      ID: {channel.get('id', 'سيتم استخراجه')}")
        
        # 3. اختبار تحليل النص (محاكاة)
        print("\n3️⃣ اختبار تحليل النص...")
        
        sample_text = """
        مرحباً بكم في قناتنا، اليوم سنتحدث عن أحدث أخبار الألعاب.
        أعلنت شركة سوني عن إصدار جديد من PlayStation 5 Pro.
        كما تم الكشف عن لعبة جديدة من استوديو Naughty Dog.
        هناك تحديث جديد للعبة Fortnite سيصدر الأسبوع القادم.
        نصيحة للاعبين: تأكدوا من تحديث أجهزتكم.
        """
        
        analysis = youtube_analyzer.analyze_transcript_for_gaming_news(sample_text, 'ar')
        
        print(f"   📊 إجمالي الجمل: {analysis['total_sentences']}")
        print(f"   📰 عدد الأخبار: {analysis['news_count']}")
        print(f"   ℹ️ معلومات إضافية: {analysis['info_count']}")
        
        if analysis['main_news']:
            print("   🔥 أهم الأخبار:")
            for i, news in enumerate(analysis['main_news'][:2], 1):
                print(f"      {i}. {news['text'][:60]}...")
                print(f"         أهمية: {news['importance']}%")
        
        # 4. اختبار قاعدة البيانات الجديدة
        print("\n4️⃣ اختبار قاعدة البيانات الجديدة...")
        
        # اختبار فحص الفيديو
        test_video_id = "test_video_123"
        is_processed = db.is_video_processed(test_video_id)
        print(f"   📹 فحص معالجة الفيديو: {'معالج' if is_processed else 'غير معالج'}")
        
        # اختبار إحصائيات الفيديوهات
        try:
            video_stats = db.get_video_processing_stats(7)
            if video_stats:
                print(f"   📊 فيديوهات معالجة (7 أيام): {video_stats.get('total_videos_processed', 0)}")
                print(f"   📰 أخبار مستخرجة: {video_stats.get('total_news_extracted', 0)}")
            else:
                print("   📊 لا توجد إحصائيات فيديوهات حتى الآن")
        except Exception as e:
            print(f"   ⚠️ خطأ في إحصائيات الفيديوهات: {e}")
        
        # 5. اختبار نظام الموافقة (محاكاة)
        print("\n5️⃣ اختبار نظام الموافقة...")
        
        # بيانات فيديو وهمية للاختبار
        mock_video = {
            'id': 'test_video_456',
            'title': 'أحدث أخبار الألعاب - اختبار',
            'description': 'فيديو اختبار لنظام الموافقة',
            'published_at': '2025-01-20T10:00:00Z',
            'duration': 900,  # 15 دقيقة
            'channel_info': {
                'name': 'Test Gaming Channel',
                'language': 'ar',
                'id': 'test_channel_123'
            }
        }
        
        # محاكاة طلب الموافقة
        approval_result = {'approved': False, 'reason': 'اختبار'}
        
        async def test_callback(approved: bool, reason: str):
            approval_result['approved'] = approved
            approval_result['reason'] = reason
        
        # في الاختبار، سنحاكي الموافقة التلقائية
        approval_result = {'approved': True, 'reason': 'موافقة تلقائية للاختبار'}
        print(f"   ✅ نتيجة الموافقة: {'موافق' if approval_result['approved'] else 'مرفوض'}")
        print(f"   📝 السبب: {approval_result['reason']}")
        
        # 6. اختبار حفظ البيانات
        print("\n6️⃣ اختبار حفظ البيانات...")
        
        if not db.is_video_processed(mock_video['id']):
            # حفظ بيانات الفيديو
            video_record = {
                'video_id': mock_video['id'],
                'title': mock_video['title'],
                'channel_id': mock_video['channel_info']['id'],
                'channel_name': mock_video['channel_info']['name'],
                'duration': mock_video['duration'],
                'published_date': mock_video['published_at'],
                'transcript_length': len(sample_text),
                'news_extracted': analysis['news_count'],
                'article_id': None
            }
            
            video_id = db.save_processed_video(video_record)
            if video_id:
                print(f"   ✅ تم حفظ بيانات الفيديو برقم: {video_id}")
                
                # حفظ النص المستخرج
                transcript_data = {
                    'transcript_text': sample_text,
                    'language': 'ar',
                    'main_news_count': analysis['news_count'],
                    'additional_info_count': analysis['info_count']
                }
                
                if db.save_video_transcript(mock_video['id'], transcript_data):
                    print("   ✅ تم حفظ النص المستخرج")
            else:
                print("   ❌ فشل في حفظ بيانات الفيديو")
        else:
            print("   ⚠️ الفيديو موجود مسبقاً في قاعدة البيانات")
        
        # 7. عرض الإحصائيات النهائية
        print("\n7️⃣ الإحصائيات النهائية...")

        try:
            final_stats = db.get_video_processing_stats(7)
            if final_stats:
                print(f"   📊 إجمالي الفيديوهات: {final_stats.get('total_videos_processed', 0)}")
                print(f"   📰 إجمالي الأخبار: {final_stats.get('total_news_extracted', 0)}")
                print(f"   📺 القنوات الفريدة: {final_stats.get('unique_channels', 0)}")
                print(f"   ✅ معدل الموافقة: {final_stats.get('approval_rate', 0):.1f}%")
            else:
                print("   📊 لا توجد إحصائيات متاحة حتى الآن")
        except Exception as e:
            print(f"   ⚠️ خطأ في الإحصائيات النهائية: {e}")
        
        print("\n" + "=" * 50)
        print("✅ اكتمل الاختبار السريع بنجاح!")
        print("🚀 النظام جاهز للاستخدام")
        
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def display_next_steps():
    """عرض الخطوات التالية"""
    print("\n📋 الخطوات التالية:")
    print("-" * 20)
    
    steps = [
        "1. تشغيل الإعداد الكامل: python setup_youtube_priority.py",
        "2. اختبار شامل: python test_youtube_priority_system.py",
        "3. تشغيل البوت الرئيسي: python main.py",
        "4. مراقبة رسائل الموافقة على Telegram",
        "5. مراجعة الإحصائيات في قاعدة البيانات"
    ]
    
    for step in steps:
        print(f"   {step}")

async def main():
    """الدالة الرئيسية"""
    success = await quick_test()
    
    if success:
        display_next_steps()
    else:
        print("\n⚠️ هناك مشاكل تحتاج لحل قبل الاستخدام")
        print("💡 تحقق من:")
        print("   - مفاتيح API في ملف الإعدادات")
        print("   - اتصال الإنترنت")
        print("   - صحة قاعدة البيانات")

if __name__ == "__main__":
    asyncio.run(main())
