# إعداد سريع لمفاتيح APIs - ImageGuard Pro 🚀

## 📋 قائمة المفاتيح المطلوبة

### 🟢 Pexels API (الأولوية العالية - مجاني)
- **الموقع**: https://www.pexels.com/api/
- **الحدود**: 200 طلب/ساعة
- **الجودة**: ممتازة
- **الرخصة**: Pexels License (استخدام تجاري مجاني)

### 🟡 Pixabay API (الأولوية المتوسطة - مجاني)
- **الموقع**: https://pixabay.com/api/docs/
- **الحدود**: 5000 طلب/شهر
- **الجودة**: جيدة جداً
- **الرخصة**: Pixabay License (استخدام تجاري مجاني)

### 🟠 Unsplash API (احتياطي - مجاني)
- **الموقع**: https://unsplash.com/developers
- **الحدود**: 50 طلب/ساعة
- **الجودة**: ممتازة
- **الرخصة**: Unsplash License (استخدام تجاري مجاني)

---

## 🔧 خطوات الإعداد السريع

### الخطوة 1: إنشاء حساب Pexels
```bash
1. اذهب إلى: https://www.pexels.com/api/
2. انقر على "Get Started"
3. أنشئ حساب جديد أو سجل دخول
4. اذهب إلى "Your API Key"
5. انسخ المفتاح
```

### الخطوة 2: إنشاء حساب Pixabay
```bash
1. اذهب إلى: https://pixabay.com/accounts/register/
2. أنشئ حساب جديد
3. اذهب إلى: https://pixabay.com/api/docs/
4. انقر على "Get your API key"
5. انسخ المفتاح
```

### الخطوة 3: إنشاء تطبيق Unsplash
```bash
1. اذهب إلى: https://unsplash.com/developers
2. انقر على "Register as a developer"
3. أنشئ حساب جديد
4. انقر على "New Application"
5. املأ البيانات:
   - Application name: "Gaming News Bot"
   - Description: "AI bot for gaming news with safe images"
6. انسخ "Access Key"
```

### الخطوة 4: إضافة المفاتيح لملف .env
```env
# أضف هذه الأسطر لملف .env
PEXELS_API_KEY=your_pexels_key_here
PIXABAY_API_KEY=your_pixabay_key_here
UNSPLASH_ACCESS_KEY=your_unsplash_access_key_here
```

---

## ✅ التحقق من الإعداد

### اختبار سريع
```bash
# شغل الاختبار للتأكد من عمل النظام
python test_image_guard.py
```

### النتيجة المتوقعة
```
🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.
```

---

## 🚨 ملاحظات مهمة

### ⚠️ إذا لم تحصل على مفاتيح APIs
- **لا تقلق!** النظام سيعمل بالصور الاحتياطية الآمنة
- الصور الاحتياطية مجانية 100% ومتوافقة مع AdSense
- يمكنك إضافة المفاتيح لاحقاً لتحسين التنوع

### 🔒 أمان المفاتيح
- لا تشارك مفاتيح APIs مع أحد
- احتفظ بنسخة احتياطية آمنة
- يمكنك إعادة توليد المفاتيح إذا لزم الأمر

### 📊 مراقبة الاستخدام
- راقب حدود APIs لتجنب التجاوز
- Pexels: 200 طلب/ساعة
- Pixabay: 5000 طلب/شهر  
- Unsplash: 50 طلب/ساعة

---

## 🎯 نصائح للحصول على أفضل النتائج

### 1. ترتيب الأولوية
```
Pexels (أفضل جودة) → Pixabay (تنوع كبير) → Unsplash (احتياطي)
```

### 2. كلمات البحث المثلى
```
✅ جيد: "gaming controller", "esports setup", "gaming keyboard"
❌ تجنب: "violent game", "bloody battle", "gambling"
```

### 3. مراقبة الأداء
```python
# تحقق من الإحصائيات دورياً
from modules.image_guard import image_guard
stats = image_guard.get_usage_stats()
print(f"استدعاءات Pexels: {stats['pexels_calls']}")
```

---

## 🆘 حل المشاكل الشائعة

### مشكلة: "API Key غير صالح"
```bash
# الحل:
1. تحقق من صحة المفتاح في ملف .env
2. تأكد من عدم وجود مسافات إضافية
3. جرب إعادة توليد المفتاح
```

### مشكلة: "تجاوز حدود API"
```bash
# الحل:
1. انتظر حتى إعادة تعيين الحدود
2. استخدم APIs أخرى
3. النظام سيستخدم الصور الاحتياطية تلقائياً
```

### مشكلة: "لا توجد صور مناسبة"
```bash
# الحل:
1. النظام سيستخدم الصور الاحتياطية الآمنة
2. جرب كلمات بحث مختلفة
3. تحقق من اتصال الإنترنت
```

---

## 🎉 مبروك! النظام جاهز

بعد إكمال هذه الخطوات، ستحصل على:
- ✅ صور آمنة 100% قانونياً
- ✅ متوافقة مع سياسات AdSense
- ✅ جودة عالية ومتنوعة
- ✅ نسب تلقائية للمصورين
- ✅ نظام احتياطي موثوق

**🚀 ابدأ الآن واستمتع بصور آمنة لمقالاتك!**
