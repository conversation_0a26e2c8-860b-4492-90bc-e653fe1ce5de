#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نماذج الذكاء الاصطناعي المجانية عبر الإنترنت
"""

import asyncio
import sys
import os

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger

async def test_individual_models():
    """اختبار النماذج الفردية"""
    print("🤖 اختبار نماذج AI المجانية الفردية...")
    print("-" * 60)
    
    try:
        from modules.free_ai_models import free_ai_models
        
        # محتوى تجريبي للاختبار
        test_content = """
        New gaming console PlayStation 6 announced by Sony with revolutionary features.
        The console includes advanced AI processing, 8K gaming support, and backward compatibility.
        Expected release date is late 2025 with a price point around $599.
        """
        
        # اختبار النماذج المختلفة
        models_to_test = ['deepseek', 'qwen', 'gemini_free', 'huggingface_free']
        
        successful_models = []
        
        for model_name in models_to_test:
            print(f"\n🔍 اختبار {model_name}...")
            
            try:
                # اختبار تحليل المحتوى
                analysis = await free_ai_models.analyze_gaming_content(test_content, 'quality')
                
                if analysis and analysis.get('ai_model_used') == model_name:
                    print(f"✅ {model_name}: يعمل بنجاح")
                    print(f"   جودة: {analysis.get('ai_quality_score', 0)}/10")
                    print(f"   صلة: {analysis.get('ai_relevance_score', 0)}/10")
                    print(f"   ملخص: {analysis.get('ai_summary', 'لا يوجد')[:50]}...")
                    successful_models.append(model_name)
                else:
                    print(f"⚠️ {model_name}: لم يستجب أو استخدم نموذج بديل")
                    
            except Exception as e:
                print(f"❌ {model_name}: خطأ - {e}")
        
        print(f"\n📊 النماذج الناجحة: {len(successful_models)}/{len(models_to_test)}")
        
        if successful_models:
            print(f"✅ النماذج العاملة: {', '.join(successful_models)}")
            return True
        else:
            print("❌ لا توجد نماذج تعمل حالياً")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار النماذج: {e}")
        return False

async def test_query_enhancement():
    """اختبار تحسين الاستعلامات"""
    print("\n🔍 اختبار تحسين الاستعلامات...")
    print("-" * 60)
    
    try:
        from modules.free_ai_models import free_ai_models
        
        # استعلامات تجريبية
        test_queries = [
            "gaming news",
            "new games 2025",
            "PlayStation updates"
        ]
        
        successful_enhancements = 0
        
        for query in test_queries:
            print(f"\n🔍 تحسين: '{query}'")
            
            try:
                enhanced_queries = await free_ai_models.enhance_search_query(query)
                
                if enhanced_queries and len(enhanced_queries) > 1:
                    print(f"✅ تم تحسين الاستعلام ({len(enhanced_queries)} استعلام)")
                    for i, enhanced in enumerate(enhanced_queries[:3], 1):
                        print(f"   {i}. {enhanced}")
                    successful_enhancements += 1
                else:
                    print(f"⚠️ تحسين محدود أو فشل")
                    
            except Exception as e:
                print(f"❌ خطأ في تحسين '{query}': {e}")
        
        success_rate = (successful_enhancements / len(test_queries)) * 100
        print(f"\n📊 معدل نجاح تحسين الاستعلامات: {success_rate:.1f}%")
        
        return success_rate > 50
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تحسين الاستعلامات: {e}")
        return False

async def test_models_status():
    """اختبار حالة النماذج"""
    print("\n📊 اختبار حالة النماذج...")
    print("-" * 60)
    
    try:
        from modules.free_ai_models import free_ai_models
        
        status = await free_ai_models.get_models_status()
        
        print(f"📈 إجمالي النماذج المتاحة: {status['available_models']}")
        print(f"📊 إجمالي الطلبات: {status['usage_stats']['total_requests']}")
        print(f"✅ الطلبات الناجحة: {status['usage_stats']['successful_requests']}")
        print(f"❌ الطلبات الفاشلة: {status['usage_stats']['failed_requests']}")
        print(f"⏱️ متوسط وقت الاستجابة: {status['average_response_time']:.2f} ثانية")
        print(f"📈 معدل النجاح: {status['success_rate']:.1f}%")
        
        print("\n🤖 تفاصيل النماذج:")
        for model_name, model_info in status['models'].items():
            print(f"   • {model_info['name']}: {model_info['status']}")
            print(f"     القوة: {model_info['strength']}")
            print(f"     الحد المجاني: {model_info['free_limit']}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص حالة النماذج: {e}")
        return False

async def test_integration_with_search():
    """اختبار التكامل مع البحث"""
    print("\n🔗 اختبار التكامل مع نظام البحث...")
    print("-" * 60)
    
    try:
        from modules.free_mcp_search import free_mcp_search
        
        # اختبار البحث مع التحليل المحسن
        results = await free_mcp_search.free_deep_search("gaming news today", max_results=3)
        
        if results:
            print(f"✅ البحث المحسن: {len(results)} نتيجة")
            
            # فحص النتائج للتأكد من وجود تحليل AI
            ai_analyzed_results = 0
            
            for result in results:
                if result.get('ai_model_used') and result.get('ai_quality_score'):
                    ai_analyzed_results += 1
                    model_used = result.get('ai_model_used', 'غير محدد')
                    quality = result.get('ai_quality_score', 0)
                    print(f"   📊 تحليل AI: {model_used}, جودة: {quality}/10")
            
            if ai_analyzed_results > 0:
                print(f"🎉 تم تحليل {ai_analyzed_results}/{len(results)} نتيجة بـ AI!")
                return True
            else:
                print("⚠️ لم يتم تحليل النتائج بـ AI")
                return False
        else:
            print("❌ لم يتم العثور على نتائج")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل: {e}")
        return False

async def test_fallback_mechanisms():
    """اختبار آليات الاحتياط"""
    print("\n🛡️ اختبار آليات الاحتياط...")
    print("-" * 60)
    
    try:
        from modules.free_ai_models import free_ai_models
        
        # اختبار مع محتوى قصير جداً
        short_content = "Game"
        analysis = await free_ai_models.analyze_gaming_content(short_content, 'quality')
        
        if analysis:
            print("✅ آلية الاحتياط تعمل للمحتوى القصير")
            print(f"   النموذج المستخدم: {analysis.get('ai_model_used', 'غير محدد')}")
        else:
            print("❌ فشل في آلية الاحتياط")
            return False
        
        # اختبار تحسين استعلام بسيط
        simple_query = "games"
        enhanced = await free_ai_models.enhance_search_query(simple_query)
        
        if enhanced and len(enhanced) > 0:
            print("✅ آلية الاحتياط تعمل لتحسين الاستعلامات")
            print(f"   استعلامات محسنة: {len(enhanced)}")
        else:
            print("❌ فشل في آلية احتياط تحسين الاستعلامات")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار آليات الاحتياط: {e}")
        return False

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 اختبار نماذج الذكاء الاصطناعي المجانية")
    print("=" * 70)
    
    tests = [
        ("النماذج الفردية", test_individual_models()),
        ("تحسين الاستعلامات", test_query_enhancement()),
        ("حالة النماذج", test_models_status()),
        ("التكامل مع البحث", test_integration_with_search()),
        ("آليات الاحتياط", test_fallback_mechanisms())
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}...")
        try:
            result = await test_func
            if result:
                passed_tests += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    print("\n" + "=" * 70)
    print("📊 ملخص نتائج الاختبار:")
    print(f"   • الاختبارات الناجحة: {passed_tests}/{total_tests}")
    print(f"   • معدل النجاح: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests >= 3:  # على الأقل 3 اختبارات ناجحة
        print("\n🎉 نماذج AI المجانية جاهزة للاستخدام!")
        print("\n💡 النماذج المتاحة:")
        print("   🤖 DeepSeek - برمجة وتحليل متقدم")
        print("   🇨🇳 Qwen - فهم اللغة العربية والصينية")
        print("   🔍 Gemini - بحث ومعلومات حديثة")
        print("   🤗 Hugging Face - نماذج متنوعة ومفتوحة")
        
        print("\n🚀 الاستخدام:")
        print("   1. النماذج تعمل تلقائياً مع البحث")
        print("   2. تحليل ذكي للمحتوى")
        print("   3. تحسين الاستعلامات")
        print("   4. مجاني 100% بدون مفاتيح API!")
        
        return True
    else:
        print("\n⚠️ النماذج تحتاج تحسين")
        print("\n🔧 خطوات مقترحة:")
        print("   • تحقق من اتصال الإنترنت")
        print("   • بعض النماذج قد تكون مؤقتاً غير متاحة")
        print("   • النظام سيستخدم آليات الاحتياط")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
