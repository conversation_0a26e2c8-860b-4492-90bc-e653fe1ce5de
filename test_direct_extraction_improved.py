#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الاستخراج المباشر المحسن
"""

import asyncio
import sys
import os

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.advanced_web_scraper import AdvancedWebScraper

async def test_improved_direct_extraction():
    """اختبار الاستخراج المباشر المحسن"""
    print("🧪 اختبار الاستخراج المباشر المحسن...")
    print("=" * 60)
    
    # إنشاء مثيل من AdvancedWebScraper
    scraper = AdvancedWebScraper()
    
    print(f"🎮 مواقع الألعاب المتاحة: {len(scraper.gaming_sites)}")
    
    # اختبار البحث الشامل مع الاستخراج المباشر
    try:
        print("\n🔍 اختبار البحث الشامل مع الاستخراج المباشر...")
        
        # البحث باستخدام الدالة المحسنة
        articles = await scraper.comprehensive_gaming_search(
            query='gaming news today',
            max_results=20,
            include_direct_scraping=True
        )
        
        print(f"📊 النتائج:")
        print(f"   إجمالي المقالات: {len(articles)}")
        
        if articles:
            # تحليل المصادر
            source_types = {}
            direct_extraction_articles = []
            
            for article in articles:
                extraction_method = article.get('extraction_method', 'unknown')
                source_types[extraction_method] = source_types.get(extraction_method, 0) + 1
                
                if extraction_method == 'web_scraping':
                    direct_extraction_articles.append(article)
            
            print(f"\n📡 توزيع طرق الاستخراج:")
            for method, count in source_types.items():
                print(f"   {method}: {count} مقال")
            
            print(f"\n🎮 مقالات الاستخراج المباشر: {len(direct_extraction_articles)}")
            
            if direct_extraction_articles:
                print(f"\n📰 عينة من مقالات الاستخراج المباشر:")
                
                for i, article in enumerate(direct_extraction_articles[:5], 1):
                    print(f"\n   {i}. العنوان: {article['title'][:60]}...")
                    print(f"      المصدر: {article['source']}")
                    print(f"      نقاط الجودة: {article.get('quality_score', 'غير محدد')}/10")
                    print(f"      طول المحتوى: {len(article.get('content', ''))} حرف")
                    print(f"      الرابط: {article['url']}")
                
                # تحليل الجودة
                quality_scores = [article.get('quality_score', 0) for article in direct_extraction_articles]
                avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0
                
                print(f"\n📊 تحليل جودة الاستخراج المباشر:")
                print(f"   متوسط نقاط الجودة: {avg_quality:.1f}/10")
                
                # فحص المصادر
                sources = {}
                for article in direct_extraction_articles:
                    source = article['source']
                    sources[source] = sources.get(source, 0) + 1
                
                print(f"\n📡 مصادر الاستخراج المباشر:")
                for source, count in sorted(sources.items(), key=lambda x: x[1], reverse=True):
                    print(f"   {source}: {count} مقال")
                
                return True
            else:
                print("⚠️ لم يتم العثور على مقالات من الاستخراج المباشر")
                return await diagnose_direct_extraction_issues(scraper)
        else:
            print("⚠️ لم يتم العثور على أي مقالات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

async def diagnose_direct_extraction_issues(scraper):
    """تشخيص مشاكل الاستخراج المباشر"""
    print("\n🔍 تشخيص مشاكل الاستخراج المباشر...")
    
    try:
        # اختبار الاستخراج المباشر بشكل منفصل
        direct_results = await scraper._direct_scraping_from_gaming_sites('gaming', 10)
        
        print(f"📊 نتائج الاستخراج المباشر المنفصل: {len(direct_results)} مقال")
        
        if direct_results:
            print(f"✅ الاستخراج المباشر يعمل!")
            
            for i, article in enumerate(direct_results[:3], 1):
                print(f"   {i}. {article['title'][:50]}... (من {article['source']})")
            
            return True
        else:
            print("❌ الاستخراج المباشر لا يعمل")
            
            # اختبار كل موقع على حدة
            print("\n🔍 اختبار المواقع الفردية...")
            
            for domain, config in scraper.gaming_sites.items():
                try:
                    print(f"\n   🌐 اختبار {domain}...")
                    
                    # بناء URL البحث
                    search_url = f"https://{domain}/search?q=gaming"
                    
                    # اختبار استخراج الموقع
                    site_results = await scraper._scrape_gaming_site(search_url, config, 3)
                    
                    print(f"      📊 النتائج: {len(site_results)} مقال")
                    
                    if site_results:
                        for article in site_results:
                            print(f"         • {article['title'][:40]}...")
                    else:
                        print(f"      ⚠️ لا توجد نتائج من {domain}")
                        
                except Exception as e:
                    print(f"      ❌ خطأ في {domain}: {e}")
            
            return False
            
    except Exception as e:
        print(f"❌ خطأ في التشخيص: {e}")
        return False

async def test_individual_site_extraction():
    """اختبار استخراج موقع فردي"""
    print("\n🔍 اختبار استخراج موقع فردي...")
    
    scraper = AdvancedWebScraper()
    
    # اختبار موقع واحد بالتفصيل
    test_site = 'polygon.com'
    if test_site in scraper.gaming_sites:
        config = scraper.gaming_sites[test_site]
        
        print(f"🌐 اختبار {test_site} بالتفصيل...")
        print(f"   Selectors الحالية:")
        for element_type, selector in config['selectors'].items():
            print(f"      {element_type}: {selector}")
        
        try:
            # اختبار URL مباشر
            test_url = f"https://{test_site}/news"
            
            site_results = await scraper._scrape_gaming_site(test_url, config, 5)
            
            print(f"\n   📊 النتائج: {len(site_results)} مقال")
            
            if site_results:
                for i, article in enumerate(site_results, 1):
                    print(f"\n   {i}. العنوان: {article['title'][:50]}...")
                    print(f"      المحتوى: {len(article.get('content', ''))} حرف")
                    print(f"      التاريخ: {article.get('published_date', 'غير محدد')}")
                    print(f"      المؤلف: {article.get('author', 'غير محدد')}")
                    print(f"      الجودة: {article.get('quality_score', 0)}/10")
                
                return True
            else:
                print(f"   ⚠️ لا توجد نتائج من {test_site}")
                return False
                
        except Exception as e:
            print(f"   ❌ خطأ في اختبار {test_site}: {e}")
            return False
    else:
        print(f"❌ الموقع {test_site} غير موجود في التكوين")
        return False

async def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار الاستخراج المباشر المحسن...")
    
    # اختبار الاستخراج المباشر المحسن
    comprehensive_success = await test_improved_direct_extraction()
    
    # اختبار موقع فردي
    individual_success = await test_individual_site_extraction()
    
    print("\n" + "=" * 60)
    print("📋 ملخص النتائج:")
    print("-" * 30)
    
    print(f"الاستخراج المباشر الشامل: {'✅ نجح' if comprehensive_success else '❌ فشل'}")
    print(f"اختبار الموقع الفردي: {'✅ نجح' if individual_success else '❌ فشل'}")
    
    if comprehensive_success or individual_success:
        print("\n🎉 تم تحسين الاستخراج المباشر بنجاح!")
        print("💡 التحسينات المطبقة:")
        print("   • selectors محدثة ومحسنة")
        print("   • آلية تشخيص تلقائي للأخطاء")
        print("   • دعم مواقع إضافية")
        print("   • معالجة أخطاء محسنة")
        
        return True
    else:
        print("\n⚠️ الاستخراج المباشر يحتاج مزيد من التحسين")
        print("🔧 خطوات إضافية مقترحة:")
        print("   • مراجعة selectors للمواقع")
        print("   • تحسين آلية البحث عن المقالات")
        print("   • إضافة مواقع أخرى")
        
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
