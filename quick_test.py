#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للميزات الجديدة
"""

import asyncio
import sys
import os

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.user_engagement import engagement_engine
from modules.visitor_analytics import visitor_analytics
from modules.intelligent_cms import intelligent_cms
from modules.advanced_seo import advanced_seo
from modules.ai_personality import ai_personality

async def test_engagement_system():
    """اختبار نظام الجذب"""
    print("🎯 اختبار نظام جذب المستخدمين...")
    
    # اختبار العنوان الفيروسي
    viral_title = engagement_engine.generate_viral_title(
        "Minecraft Gets New Update",
        "Minecraft",
        "news"
    )
    print(f"📰 عنوان فيروسي: {viral_title}")
    
    # اختبار المحتوى التفاعلي
    content = "هذا محتوى اختبار بسيط."
    engaging_content = engagement_engine.generate_engaging_content(content, "gamers")
    print(f"✨ محتوى محسن: {engaging_content[:100]}...")
    
    # اختبار إمكانية الانتشار
    viral_potential = engagement_engine.calculate_viral_potential(viral_title, engaging_content)
    print(f"🔥 إمكانية الانتشار: {viral_potential['viral_score']}/100")
    
    return True

async def test_visitor_analytics():
    """اختبار تحليلات الزوار"""
    print("\n📊 اختبار تحليلات الزوار...")
    
    # محاكاة زائر
    visitor_data = {
        'visitor_id': 'test_visitor_001',
        'session_id': 'test_session_001',
        'page_url': 'https://example.com/test-article',
        'device_type': 'mobile',
        'browser': 'chrome'
    }
    
    result = visitor_analytics.track_visitor(visitor_data)
    print(f"👤 تتبع الزائر: {'✅ نجح' if result else '❌ فشل'}")
    
    # الحصول على رؤى
    insights = visitor_analytics.get_visitor_insights(7)
    print(f"🔍 رؤى الزوار: {len(insights)} نقطة بيانات")
    
    return True

async def test_intelligent_cms():
    """اختبار نظام إدارة المحتوى الذكي"""
    print("\n🧠 اختبار نظام إدارة المحتوى الذكي...")
    
    # اتخاذ قرارات استراتيجية
    decisions = await intelligent_cms.make_content_decisions()
    print(f"💡 قرارات استراتيجية: {len(decisions)} فئة")
    
    # تقرير الشخصية
    personality_report = intelligent_cms.get_ai_personality_report()
    print(f"🤖 تقرير الشخصية: {'✅ متوفر' if personality_report else '❌ غير متوفر'}")
    
    return True

async def test_advanced_seo():
    """اختبار SEO المتقدم"""
    print("\n🚀 اختبار SEO المتقدم...")
    
    # تحليل الكلمات المفتاحية
    keywords = ['gaming', 'minecraft', 'video games']
    opportunities = await advanced_seo.analyze_keyword_opportunities(keywords)
    print(f"🔍 فرص الكلمات المفتاحية: {len(opportunities)} فئة")
    
    # مقال اختبار
    test_article = {
        'title': 'أفضل ألعاب 2025 - دليل شامل',
        'content': 'هذا محتوى اختبار لمقال عن أفضل الألعاب في عام 2025.',
        'meta_description': 'اكتشف أفضل ألعاب الفيديو لعام 2025',
        'keywords': keywords
    }
    
    # حساب نقاط SEO
    seo_score = advanced_seo.calculate_seo_score(test_article, keywords)
    print(f"📈 نقاط SEO: {seo_score['total_score']}/100 ({seo_score['grade']})")
    
    # توليد Schema Markup
    schema = advanced_seo.generate_schema_markup(test_article, 'article')
    print(f"🏗️ Schema Markup: {'✅ تم التوليد' if schema else '❌ فشل'}")
    
    return True

async def test_ai_personality():
    """اختبار الشخصية الاصطناعية"""
    print("\n🤖 اختبار الشخصية الاصطناعية - أليكس...")
    
    # اتخاذ قرار
    context = {
        'situation': 'content_quality_vs_quantity',
        'urgency': 'medium',
        'impact': 'high'
    }
    
    options = [
        {
            'name': 'focus_on_quality',
            'data_support': 9,
            'long_term_impact': 8,
            'risk_level': 2,
            'user_benefit': 9
        },
        {
            'name': 'increase_quantity',
            'data_support': 6,
            'long_term_impact': 5,
            'risk_level': 4,
            'user_benefit': 6
        }
    ]
    
    decision = ai_personality.make_personality_driven_decision(context, options)
    chosen_option = decision.get('chosen_option', {}).get('option', {}).get('name', 'غير محدد')
    confidence = decision.get('confidence_level', 0)
    
    print(f"🎯 قرار أليكس: {chosen_option}")
    print(f"🎚️ مستوى الثقة: {confidence}/100")
    
    # رد شخصي
    response = ai_personality.generate_personality_response(
        "نجاح في تطوير ميزات جديدة للموقع",
        'enthusiastic'
    )
    print(f"💬 رد أليكس: {response}")
    
    # تقرير الشخصية
    personality_report = ai_personality.get_personality_report()
    print(f"📋 تقرير الشخصية: {'✅ متوفر' if personality_report else '❌ غير متوفر'}")
    
    return True

async def run_comprehensive_test():
    """تشغيل اختبار شامل"""
    print("🚀 بدء الاختبار الشامل للميزات الجديدة...\n")
    
    tests = [
        ("نظام جذب المستخدمين", test_engagement_system),
        ("تحليلات الزوار", test_visitor_analytics),
        ("نظام إدارة المحتوى الذكي", test_intelligent_cms),
        ("SEO المتقدم", test_advanced_seo),
        ("الشخصية الاصطناعية", test_ai_personality)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result, None))
            print(f"✅ {test_name}: نجح")
        except Exception as e:
            results.append((test_name, False, str(e)))
            print(f"❌ {test_name}: فشل - {e}")
    
    # تقرير النتائج
    print("\n" + "="*60)
    print("📊 تقرير نتائج الاختبار")
    print("="*60)
    
    successful_tests = sum(1 for _, success, _ in results if success)
    total_tests = len(results)
    success_rate = (successful_tests / total_tests) * 100
    
    print(f"🎯 معدل النجاح: {success_rate:.1f}% ({successful_tests}/{total_tests})")
    
    for test_name, success, error in results:
        status = "✅ نجح" if success else f"❌ فشل: {error}"
        print(f"  • {test_name}: {status}")
    
    if success_rate >= 80:
        print("\n🎉 ممتاز! جميع الأنظمة تعمل بشكل صحيح")
        print("🚀 البوت جاهز للعمل بالميزات المتقدمة!")
    elif success_rate >= 60:
        print("\n⚠️ جيد! معظم الأنظمة تعمل مع بعض المشاكل البسيطة")
    else:
        print("\n❌ يحتاج إلى مراجعة! عدة أنظمة تحتاج إصلاح")
    
    print("\n🤖 أليكس يقول: شكراً لاختبار قدراتي الجديدة!")

def main():
    """الدالة الرئيسية"""
    try:
        asyncio.run(run_comprehensive_test())
    except KeyboardInterrupt:
        print("\n⌨️ تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")

if __name__ == "__main__":
    main()
