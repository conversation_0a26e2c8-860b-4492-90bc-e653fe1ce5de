#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحديث سريع لمفاتيح API
"""

import os
import sys

def update_env_file():
    """تحديث ملف .env بالمفاتيح الجديدة"""
    print("🔧 تحديث مفاتيح API...")
    
    # المفاتيح الجديدة المحدثة
    new_keys = {
        'GOOGLE_SEARCH_KEY': 'AIzaSyC3ihvVEvFSgaw_MAjcJLBZQno_8L1F-Tk',
        'FREEPIK_API_KEY': 'FPSX1ee910637a8ec349e6d8c7f17a57740b',
        'FLUXAI_API_KEY': 'b6863038ac459a1f8cd9e30d82cdd989'
    }
    
    env_file_path = '.env'
    
    # قراءة الملف الحالي
    lines = []
    if os.path.exists(env_file_path):
        with open(env_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    
    # تحديث المفاتيح
    updated_lines = []
    keys_updated = set()
    
    for line in lines:
        line = line.strip()
        if '=' in line and not line.startswith('#'):
            key, value = line.split('=', 1)
            if key in new_keys:
                updated_lines.append(f"{key}={new_keys[key]}\n")
                keys_updated.add(key)
                print(f"✅ تم تحديث {key}")
            else:
                updated_lines.append(line + '\n')
        else:
            updated_lines.append(line + '\n')
    
    # إضافة المفاتيح الجديدة التي لم تكن موجودة
    for key, value in new_keys.items():
        if key not in keys_updated:
            updated_lines.append(f"{key}={value}\n")
            print(f"✅ تم إضافة {key}")
    
    # كتابة الملف المحدث
    with open(env_file_path, 'w', encoding='utf-8') as f:
        f.writelines(updated_lines)
    
    print("✅ تم تحديث ملف .env بنجاح")

def show_current_status():
    """عرض حالة المفاتيح الحالية"""
    print("\n📊 حالة مفاتيح API الحالية:")
    print("-" * 50)
    
    status = {
        'Google Search API': {
            'key': 'AIzaSyC3ihvVEvFSgaw_MAjcJLBZQno_8L1F-Tk',
            'status': '✅ يعمل',
            'description': 'مفتاح جديد ومحدث'
        },
        'Freepik API': {
            'key': 'FPSX1ee910637a8ec349e6d8c7f17a57740b',
            'status': '🔄 يعمل جزئياً',
            'description': 'ينشئ المهام بنجاح، يحتاج تحسين polling'
        },
        'FluxAI API': {
            'key': 'b6863038ac459a1f8cd9e30d82cdd989',
            'status': '❌ لا يعمل',
            'description': 'مشاكل في الخدمة الخارجية'
        },
        'IGN RSS Feed': {
            'url': 'https://feeds.ign.com/ign/news',
            'status': '✅ يعمل',
            'description': 'رابط محدث وصحيح'
        }
    }
    
    for api_name, info in status.items():
        print(f"🔧 {api_name}:")
        print(f"   الحالة: {info['status']}")
        print(f"   الوصف: {info['description']}")
        if 'key' in info:
            print(f"   المفتاح: {info['key'][:10]}...")
        if 'url' in info:
            print(f"   الرابط: {info['url']}")
        print()

def main():
    """الدالة الرئيسية"""
    print("🚀 تحديث مفاتيح API لوكيل أخبار الألعاب")
    print("=" * 60)
    
    try:
        # تحديث المفاتيح
        update_env_file()
        
        # عرض الحالة
        show_current_status()
        
        print("=" * 60)
        print("🎉 تم تحديث جميع المفاتيح بنجاح!")
        print("\n📋 الخطوات التالية:")
        print("1. تشغيل اختبار المفاتيح: python test_api_keys.py")
        print("2. تشغيل البوت: python main.py")
        print("3. مراجعة التقرير النهائي: FINAL_API_FIXES_REPORT.md")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ أثناء التحديث: {e}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التحديث بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
