#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نظام YouTube المتقدم مع الأولوية والموافقة
"""

import asyncio
import sys
import os

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.advanced_youtube_analyzer import AdvancedYouTubeAnalyzer
from modules.video_approval_system import VideoApprovalSystem
from modules.database import db

async def test_youtube_priority_system():
    """اختبار النظام الجديد لـ YouTube"""
    
    print("🎥 بدء اختبار نظام YouTube المتقدم مع الأولوية والموافقة")
    print("=" * 60)
    
    try:
        # 1. اختبار محلل YouTube المتقدم
        print("\n1️⃣ اختبار محلل YouTube المتقدم...")
        youtube_analyzer = AdvancedYouTubeAnalyzer()
        
        # عرض القنوات المحددة
        print(f"📺 القنوات المحددة: {len(youtube_analyzer.priority_channels)}")
        for i, channel in enumerate(youtube_analyzer.priority_channels, 1):
            print(f"   {i}. {channel['name']} ({channel['language']}) - أولوية {channel['priority']}")
            print(f"      ID: {channel.get('id', 'غير محدد')}")
        
        # 2. البحث عن فيديو مناسب
        print("\n2️⃣ البحث عن أحدث فيديو مناسب...")
        video_data = await youtube_analyzer.find_latest_gaming_video()
        
        if video_data:
            print("✅ تم العثور على فيديو مناسب:")
            print(f"   📺 العنوان: {video_data['title']}")
            print(f"   🏷️ القناة: {video_data.get('channel_info', {}).get('name', 'غير محدد')}")
            print(f"   🔗 الرابط: https://youtube.com/watch?v={video_data['id']}")
            print(f"   📅 تاريخ النشر: {video_data.get('published_at', 'غير محدد')}")
            
            # 3. اختبار نظام الموافقة
            print("\n3️⃣ اختبار نظام الموافقة...")
            approval_system = VideoApprovalSystem()
            
            # محاكاة طلب الموافقة
            approval_result = {'approved': False, 'reason': 'في انتظار الاختبار'}
            
            async def test_approval_callback(approved: bool, reason: str):
                approval_result['approved'] = approved
                approval_result['reason'] = reason
                print(f"   📝 نتيجة الموافقة: {'موافق' if approved else 'مرفوض'} - {reason}")
            
            # إرسال طلب الموافقة (سيتم الموافقة تلقائياً في الاختبار)
            approval_id = await approval_system.request_video_approval(video_data, test_approval_callback)
            print(f"   🆔 معرف الموافقة: {approval_id}")
            
            # انتظار قصير للمحاكاة
            await asyncio.sleep(2)
            
            # 4. اختبار استخراج النص (محاكاة)
            print("\n4️⃣ اختبار استخراج النص بـ Whisper...")
            print("   ⚠️ ملاحظة: هذا اختبار محاكاة - الاستخراج الفعلي يتطلب تطوير إضافي")
            
            # محاكاة نص مستخرج
            mock_transcript = """
            مرحباً بكم في قناتنا، اليوم سنتحدث عن أحدث أخبار الألعاب.
            أعلنت شركة سوني عن إصدار جديد من PlayStation.
            كما تم الكشف عن لعبة جديدة من استوديو Naughty Dog.
            هناك تحديث جديد للعبة Fortnite سيصدر الأسبوع القادم.
            """
            
            # 5. اختبار تحليل النص
            print("\n5️⃣ اختبار تحليل النص للبحث عن أخبار...")
            channel_info = video_data.get('channel_info', {})
            language = channel_info.get('language', 'ar')
            
            analysis_result = youtube_analyzer.analyze_transcript_for_gaming_news(mock_transcript, language)
            
            print(f"   📊 إجمالي الجمل: {analysis_result['total_sentences']}")
            print(f"   📰 عدد الأخبار: {analysis_result['news_count']}")
            print(f"   ℹ️ معلومات إضافية: {analysis_result['info_count']}")
            
            if analysis_result['main_news']:
                print("   🔥 الأخبار الرئيسية:")
                for i, news in enumerate(analysis_result['main_news'][:3], 1):
                    print(f"      {i}. {news['text'][:100]}...")
                    print(f"         أهمية: {news['importance']}%, مواضيع: {news['topics']}")
            
            # 6. اختبار حفظ البيانات
            print("\n6️⃣ اختبار حفظ البيانات في قاعدة البيانات...")
            
            # حفظ بيانات الفيديو المعالج
            video_record = {
                'video_id': video_data['id'],
                'title': video_data['title'],
                'channel_id': channel_info.get('id', ''),
                'channel_name': channel_info.get('name', ''),
                'duration': video_data.get('duration', 0),
                'published_date': video_data.get('published_at'),
                'transcript_length': len(mock_transcript),
                'news_extracted': len(analysis_result['main_news']),
                'article_id': None
            }
            
            if not db.is_video_processed(video_data['id']):
                video_record_id = db.save_processed_video(video_record)
                if video_record_id:
                    print(f"   ✅ تم حفظ بيانات الفيديو برقم: {video_record_id}")
                    
                    # حفظ النص المستخرج
                    transcript_data = {
                        'transcript_text': mock_transcript,
                        'language': language,
                        'main_news_count': len(analysis_result['main_news']),
                        'additional_info_count': len(analysis_result['additional_info'])
                    }
                    
                    if db.save_video_transcript(video_data['id'], transcript_data):
                        print("   ✅ تم حفظ النص المستخرج")
                else:
                    print("   ❌ فشل في حفظ بيانات الفيديو")
            else:
                print("   ⚠️ الفيديو تم معالجته من قبل")
            
            # 7. عرض الإحصائيات
            print("\n7️⃣ إحصائيات معالجة الفيديوهات...")
            stats = db.get_video_processing_stats(7)  # آخر 7 أيام
            
            if stats:
                print(f"   📊 إجمالي الفيديوهات المعالجة: {stats.get('total_videos_processed', 0)}")
                print(f"   📰 إجمالي الأخبار المستخرجة: {stats.get('total_news_extracted', 0)}")
                print(f"   📺 عدد القنوات الفريدة: {stats.get('unique_channels', 0)}")
                print(f"   ✅ معدل الموافقة: {stats.get('approval_rate', 0):.1f}%")
            
        else:
            print("❌ لم يتم العثور على فيديو مناسب في القنوات المحددة")
            print("   💡 تأكد من:")
            print("   - صحة مفاتيح YouTube API")
            print("   - وجود فيديوهات حديثة في القنوات")
            print("   - صحة معرفات القنوات")
        
        print("\n" + "=" * 60)
        print("✅ اكتمل اختبار نظام YouTube المتقدم")
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

async def test_channel_id_extraction():
    """اختبار استخراج معرفات القنوات"""
    print("\n🔍 اختبار استخراج معرفات القنوات...")
    
    youtube_analyzer = AdvancedYouTubeAnalyzer()
    
    test_channels = [
        "https://youtube.com/@abureviews",
        "https://youtube.com/@faisella",
        "https://youtube.com/@nassergamerzone",
        "https://youtube.com/@jorraptor"
    ]
    
    for channel_url in test_channels:
        try:
            channel_id = youtube_analyzer._extract_channel_id_from_handle(channel_url)
            if channel_id:
                print(f"   ✅ {channel_url} -> {channel_id}")
            else:
                print(f"   ❌ فشل في استخراج معرف: {channel_url}")
        except Exception as e:
            print(f"   ❌ خطأ في {channel_url}: {e}")

def test_database_setup():
    """اختبار إعداد قاعدة البيانات"""
    print("\n💾 اختبار إعداد قاعدة البيانات...")
    
    try:
        # اختبار الاتصال
        stats = db.get_stats_summary(1)
        print("   ✅ الاتصال بقاعدة البيانات يعمل")
        
        # اختبار الجداول الجديدة
        test_video_id = "test_video_123"
        
        # فحص ما إذا كان الفيديو معالج
        is_processed = db.is_video_processed(test_video_id)
        print(f"   📹 فحص معالجة الفيديو: {'معالج' if is_processed else 'غير معالج'}")
        
        print("   ✅ جداول قاعدة البيانات الجديدة تعمل بشكل صحيح")
        
    except Exception as e:
        print(f"   ❌ خطأ في قاعدة البيانات: {e}")

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار شامل لنظام YouTube المتقدم")
    print("=" * 80)
    
    # اختبار قاعدة البيانات
    test_database_setup()
    
    # اختبار استخراج معرفات القنوات
    await test_channel_id_extraction()
    
    # اختبار النظام الكامل
    await test_youtube_priority_system()
    
    print("\n🎉 اكتمل جميع الاختبارات!")

if __name__ == "__main__":
    asyncio.run(main())
