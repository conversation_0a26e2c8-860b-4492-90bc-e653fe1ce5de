#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل للتحسينات المطبقة على البوت
"""

import asyncio
import sys
import os
from datetime import datetime

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.content_generator import ContentGenerator
from modules.analytics import analytics
from modules.database import db
from modules.logger import logger
from modules.user_engagement import engagement_engine
from modules.visitor_analytics import visitor_analytics
from modules.intelligent_cms import intelligent_cms
from modules.advanced_seo import advanced_seo
from modules.ai_personality import ai_personality
from config.settings import BotConfig, ContentConfig, SEOConfig

class BotImprovementTester:
    """فئة اختبار التحسينات"""
    
    def __init__(self):
        self.content_generator = ContentGenerator()
        self.test_results = {}
        
    async def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        logger.info("🧪 بدء اختبار التحسينات الشامل...")
        
        tests = [
            ("اختبار مولد المحتوى المحسن", self.test_content_generator),
            ("اختبار تنظيف التنسيق", self.test_content_formatting),
            ("اختبار نظام مراجعة الجودة", self.test_quality_review),
            ("اختبار تحسين SEO", self.test_seo_optimization),
            ("اختبار نظام التحليلات", self.test_analytics_system),
            ("اختبار قاعدة البيانات", self.test_database_operations),
            ("اختبار الكلمات المفتاحية المحسنة", self.test_enhanced_keywords),
            ("اختبار نظام جذب المستخدمين", self.test_user_engagement),
            ("اختبار تحليلات الزوار", self.test_visitor_analytics),
            ("اختبار نظام إدارة المحتوى الذكي", self.test_intelligent_cms),
            ("اختبار SEO المتقدم", self.test_advanced_seo),
            ("اختبار الشخصية الاصطناعية", self.test_ai_personality)
        ]
        
        for test_name, test_func in tests:
            try:
                logger.info(f"🔍 تشغيل: {test_name}")
                result = await test_func()
                self.test_results[test_name] = result
                status = "✅ نجح" if result['success'] else "❌ فشل"
                logger.info(f"{status}: {test_name}")
                
            except Exception as e:
                logger.error(f"❌ خطأ في {test_name}: {e}")
                self.test_results[test_name] = {'success': False, 'error': str(e)}
        
        # إنشاء تقرير شامل
        await self.generate_test_report()
    
    async def test_content_generator(self):
        """اختبار مولد المحتوى المحسن"""
        try:
            # بيانات اختبار
            test_content = {
                'title': 'New Gaming Update Released',
                'content': 'A major gaming update has been released with new features.',
                'summary': 'Gaming update with new features and improvements.',
                'keywords': ['gaming', 'update', 'new features'],
                'source_url': 'https://example.com/test'
            }
            
            # توليد مقال
            article = self.content_generator.generate_article(
                test_content, 
                'أخبار_الألعاب', 
                'standard'
            )
            
            if not article or 'error' in article:
                return {'success': False, 'message': 'فشل في توليد المقال'}
            
            # فحص وجود الحقول المطلوبة
            required_fields = ['title', 'content', 'meta_description', 'keywords']
            missing_fields = [field for field in required_fields if field not in article]
            
            if missing_fields:
                return {'success': False, 'message': f'حقول مفقودة: {missing_fields}'}
            
            # فحص جودة المحتوى
            quality_review = article.get('quality_review', {})
            
            return {
                'success': True,
                'message': 'تم توليد المقال بنجاح',
                'details': {
                    'title_length': len(article['title']),
                    'content_length': len(article['content']),
                    'keywords_count': len(article['keywords']),
                    'quality_score': quality_review.get('quality_score', 0),
                    'has_quality_review': 'quality_review' in article
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_content_formatting(self):
        """اختبار تنظيف التنسيق"""
        try:
            # محتوى اختبار يحتوي على مشاكل التنسيق
            test_content = """
            **هذا عنوان مع مشاكل**
            
            هذه فقرة عادية مع **نص عريض** و *نص مائل*.
            
            ### عنوان فرعي ###
            
            - نقطة أولى
            - نقطة ثانية
            - نقطة ثالثة
            
            **نقاط مهمة:**
            
            1. النقطة الأولى
            2. النقطة الثانية
            """
            
            # تنظيف المحتوى
            cleaned_content = self.content_generator._clean_and_format_content(test_content)
            
            # فحص إزالة رموز Markdown الخاطئة
            has_markdown_issues = '**' in cleaned_content or '###' in cleaned_content
            
            # فحص وجود HTML صحيح
            has_proper_html = '<strong>' in cleaned_content and '<h3>' in cleaned_content
            
            return {
                'success': not has_markdown_issues and has_proper_html,
                'message': 'تم تنظيف التنسيق بنجاح' if not has_markdown_issues else 'لا تزال هناك مشاكل في التنسيق',
                'details': {
                    'has_markdown_issues': has_markdown_issues,
                    'has_proper_html': has_proper_html,
                    'cleaned_length': len(cleaned_content)
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_quality_review(self):
        """اختبار نظام مراجعة الجودة"""
        try:
            # مقال اختبار
            test_article = {
                'title': 'اختبار جودة المقال',
                'content': 'هذا محتوى اختبار قصير جداً.',
                'keywords': ['اختبار']
            }
            
            # مراجعة الجودة
            quality_review = self.content_generator._review_article_quality(test_article)
            
            # فحص وجود النتائج المطلوبة
            required_keys = ['quality_score', 'issues', 'suggestions', 'approved']
            missing_keys = [key for key in required_keys if key not in quality_review]
            
            if missing_keys:
                return {'success': False, 'message': f'مفاتيح مفقودة في مراجعة الجودة: {missing_keys}'}
            
            return {
                'success': True,
                'message': 'نظام مراجعة الجودة يعمل بشكل صحيح',
                'details': quality_review
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_seo_optimization(self):
        """اختبار تحسين SEO"""
        try:
            # مقال اختبار
            test_article = {
                'title': 'عنوان اختبار',
                'content': 'محتوى اختبار للتحقق من تحسين SEO.',
                'keywords': ['اختبار', 'SEO', 'تحسين']
            }
            
            # تحسين SEO
            optimized_article = self.content_generator.optimize_for_seo(test_article)
            
            # فحص التحسينات
            has_seo_score = 'seo_score' in optimized_article
            has_meta_description = 'meta_description' in optimized_article
            has_enhanced_keywords = len(optimized_article.get('keywords', [])) > len(test_article['keywords'])
            
            return {
                'success': has_seo_score and has_meta_description,
                'message': 'تحسين SEO يعمل بشكل صحيح',
                'details': {
                    'has_seo_score': has_seo_score,
                    'seo_score': optimized_article.get('seo_score', 0),
                    'has_meta_description': has_meta_description,
                    'has_enhanced_keywords': has_enhanced_keywords,
                    'keywords_count': len(optimized_article.get('keywords', []))
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_analytics_system(self):
        """اختبار نظام التحليلات"""
        try:
            # اختبار تحليل أداء مقال
            test_article = {
                'id': 999,  # معرف اختبار
                'title': 'مقال اختبار التحليلات',
                'content': 'هذا محتوى اختبار لنظام التحليلات. يحتوي على كلمات كافية لاختبار النظام بشكل صحيح.',
                'keywords': ['اختبار', 'تحليلات', 'أداء'],
                'content_type': 'أخبار_الألعاب',
                'quality_review': {'quality_score': 85},
                'seo_score': 75
            }
            
            # تحليل الأداء
            performance_data = analytics.analyze_article_performance(test_article)
            
            # فحص النتائج
            required_metrics = ['word_count', 'readability_score', 'engagement_potential', 'overall_score']
            missing_metrics = [metric for metric in required_metrics if metric not in performance_data]
            
            if missing_metrics:
                return {'success': False, 'message': f'مقاييس مفقودة: {missing_metrics}'}
            
            return {
                'success': True,
                'message': 'نظام التحليلات يعمل بشكل صحيح',
                'details': performance_data
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_database_operations(self):
        """اختبار عمليات قاعدة البيانات"""
        try:
            # اختبار الحصول على إحصائيات
            stats = db.get_stats_summary(1)
            
            # اختبار فحص التكرار
            is_duplicate, reason = db.is_duplicate_content(
                "محتوى اختبار",
                "عنوان اختبار", 
                ["اختبار"]
            )
            
            return {
                'success': True,
                'message': 'عمليات قاعدة البيانات تعمل بشكل صحيح',
                'details': {
                    'stats_available': bool(stats),
                    'duplicate_check_works': isinstance(is_duplicate, bool)
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_enhanced_keywords(self):
        """اختبار الكلمات المفتاحية المحسنة"""
        try:
            # فحص الكلمات المفتاحية الجديدة
            base_keywords_count = len(ContentConfig.BASE_KEYWORDS)
            high_perf_keywords_count = len(SEOConfig.HIGH_PERFORMANCE_KEYWORDS)
            
            # فحص وجود كلمات مفتاحية متنوعة
            has_arabic_keywords = any('ألعاب' in keyword for keyword in ContentConfig.BASE_KEYWORDS)
            has_english_keywords = any('games' in keyword.lower() for keyword in ContentConfig.BASE_KEYWORDS)
            
            return {
                'success': base_keywords_count > 10 and high_perf_keywords_count > 5,
                'message': 'الكلمات المفتاحية المحسنة متوفرة',
                'details': {
                    'base_keywords_count': base_keywords_count,
                    'high_perf_keywords_count': high_perf_keywords_count,
                    'has_arabic_keywords': has_arabic_keywords,
                    'has_english_keywords': has_english_keywords
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def generate_test_report(self):
        """إنشاء تقرير شامل للاختبارات"""
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results.values() if result.get('success', False))
        
        logger.info("=" * 60)
        logger.info("📋 تقرير اختبار التحسينات الشامل")
        logger.info("=" * 60)
        logger.info(f"📊 إجمالي الاختبارات: {total_tests}")
        logger.info(f"✅ الاختبارات الناجحة: {successful_tests}")
        logger.info(f"❌ الاختبارات الفاشلة: {total_tests - successful_tests}")
        logger.info(f"📈 معدل النجاح: {(successful_tests/total_tests)*100:.1f}%")
        logger.info("-" * 60)
        
        for test_name, result in self.test_results.items():
            status = "✅" if result.get('success', False) else "❌"
            logger.info(f"{status} {test_name}: {result.get('message', 'لا توجد رسالة')}")
            
            if 'details' in result:
                logger.info(f"   التفاصيل: {result['details']}")
        
        logger.info("=" * 60)
        
        # حفظ التقرير في ملف
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'total_tests': total_tests,
            'successful_tests': successful_tests,
            'success_rate': (successful_tests/total_tests)*100,
            'results': self.test_results
        }
        
        try:
            import json
            with open('test_report.json', 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)
            logger.info("💾 تم حفظ التقرير في test_report.json")
        except Exception as e:
            logger.error(f"❌ فشل في حفظ التقرير: {e}")

    async def test_user_engagement(self):
        """اختبار نظام جذب المستخدمين"""
        try:
            # اختبار توليد عنوان فيروسي
            viral_title = engagement_engine.generate_viral_title(
                "New Game Update Released",
                "Minecraft",
                "news"
            )

            # اختبار تحسين المحتوى للتفاعل
            test_content = "هذا محتوى اختبار للتحقق من تحسينات التفاعل."
            engaging_content = engagement_engine.generate_engaging_content(test_content, "gamers")

            # اختبار حساب إمكانية الانتشار
            viral_potential = engagement_engine.calculate_viral_potential(viral_title, engaging_content)

            return {
                'success': True,
                'message': 'نظام جذب المستخدمين يعمل بشكل صحيح',
                'details': {
                    'viral_title_generated': bool(viral_title),
                    'content_enhanced': len(engaging_content) > len(test_content),
                    'viral_score': viral_potential.get('viral_score', 0),
                    'potential_level': viral_potential.get('potential_level', 'غير محدد')
                }
            }

        except Exception as e:
            return {'success': False, 'error': str(e)}

    async def test_visitor_analytics(self):
        """اختبار تحليلات الزوار"""
        try:
            # اختبار تتبع زائر
            visitor_data = {
                'visitor_id': 'test_visitor_123',
                'session_id': 'test_session_123',
                'page_url': 'https://example.com/test',
                'device_type': 'mobile',
                'browser': 'chrome'
            }

            track_result = visitor_analytics.track_visitor(visitor_data)

            # اختبار تتبع النقرات
            click_data = {
                'visitor_id': 'test_visitor_123',
                'article_id': 1,
                'element_type': 'link',
                'page_url': 'https://example.com/test'
            }

            click_result = visitor_analytics.track_click(click_data)

            # اختبار الحصول على رؤى الزوار
            insights = visitor_analytics.get_visitor_insights(7)

            return {
                'success': track_result and click_result,
                'message': 'تحليلات الزوار تعمل بشكل صحيح',
                'details': {
                    'visitor_tracking': track_result,
                    'click_tracking': click_result,
                    'insights_available': bool(insights)
                }
            }

        except Exception as e:
            return {'success': False, 'error': str(e)}

    async def test_intelligent_cms(self):
        """اختبار نظام إدارة المحتوى الذكي"""
        try:
            # اختبار اتخاذ القرارات
            decisions = await intelligent_cms.make_content_decisions()

            # اختبار تقرير الشخصية
            personality_report = intelligent_cms.get_ai_personality_report()

            return {
                'success': bool(decisions),
                'message': 'نظام إدارة المحتوى الذكي يعمل بشكل صحيح',
                'details': {
                    'decisions_made': bool(decisions),
                    'personality_report_available': bool(personality_report),
                    'decision_categories': len(decisions.keys()) if decisions else 0
                }
            }

        except Exception as e:
            return {'success': False, 'error': str(e)}

    async def test_advanced_seo(self):
        """اختبار SEO المتقدم"""
        try:
            # اختبار تحليل الكلمات المفتاحية
            keywords = ['gaming', 'video games', 'reviews']
            keyword_opportunities = await advanced_seo.analyze_keyword_opportunities(keywords)

            # اختبار توليد Schema Markup
            test_article = {
                'title': 'Test Gaming Article',
                'meta_description': 'Test description',
                'content': 'Test content about gaming',
                'image_urls': ['https://example.com/image.jpg']
            }

            schema_markup = advanced_seo.generate_schema_markup(test_article, 'article')

            # اختبار حساب نقاط SEO
            seo_score = advanced_seo.calculate_seo_score(test_article, keywords)

            return {
                'success': bool(keyword_opportunities and schema_markup and seo_score),
                'message': 'SEO المتقدم يعمل بشكل صحيح',
                'details': {
                    'keyword_analysis': bool(keyword_opportunities),
                    'schema_generated': bool(schema_markup),
                    'seo_score_calculated': bool(seo_score),
                    'total_seo_score': seo_score.get('total_score', 0)
                }
            }

        except Exception as e:
            return {'success': False, 'error': str(e)}

    async def test_ai_personality(self):
        """اختبار الشخصية الاصطناعية"""
        try:
            # اختبار اتخاذ قرار مبني على الشخصية
            context = {
                'situation': 'content_strategy_decision',
                'urgency': 'medium',
                'impact': 'high'
            }

            options = [
                {'name': 'focus_on_quality', 'data_support': 8, 'long_term_impact': 9, 'risk_level': 2},
                {'name': 'increase_quantity', 'data_support': 6, 'long_term_impact': 5, 'risk_level': 4}
            ]

            decision = ai_personality.make_personality_driven_decision(context, options)

            # اختبار توليد رد شخصي
            response = ai_personality.generate_personality_response(
                "نجاح في تحقيق الأهداف",
                'enthusiastic'
            )

            # اختبار التعلم من التجربة
            ai_personality.learn_from_experience({
                'type': 'test_experience',
                'outcome': 'success',
                'success_factors': ['good_planning', 'quality_focus']
            })

            # اختبار تقرير الشخصية
            personality_report = ai_personality.get_personality_report()

            return {
                'success': bool(decision and response and personality_report),
                'message': 'الشخصية الاصطناعية تعمل بشكل صحيح',
                'details': {
                    'decision_made': bool(decision),
                    'response_generated': bool(response),
                    'learning_functional': True,
                    'personality_report_available': bool(personality_report),
                    'chosen_option': decision.get('chosen_option', {}).get('option', {}).get('name', 'غير محدد')
                }
            }

        except Exception as e:
            return {'success': False, 'error': str(e)}

async def main():
    """الدالة الرئيسية للاختبار"""
    tester = BotImprovementTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
