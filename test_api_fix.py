#!/usr/bin/env python3
"""
اختبار سريع لإصلاح ApiKeyManager
"""

import sys
import os

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_api_key_manager():
    """اختبار ApiKeyManager مع الدوال الجديدة"""
    print("🧪 اختبار ApiKeyManager مع الإصلاحات...")
    
    try:
        from modules.api_key_manager import ApiKeyManager
        
        # إنشاء مدير اختبار
        test_keys = ["key1", "key2", "key3"]
        manager = ApiKeyManager(test_keys, "Test Service")
        
        print(f"✅ تم إنشاء مدير المفاتيح مع {len(test_keys)} مفتاح")
        
        # اختبار get_key
        key = manager.get_key()
        print(f"✅ الحصول على مفتاح: {key}")
        
        # اختبار mark_key_failed
        manager.mark_key_failed(key)
        print(f"✅ تم وضع المفتاح في القائمة السوداء")
        
        # اختبار get_available_keys_count
        available_count = manager.get_available_keys_count()
        print(f"✅ عدد المفاتيح المتاحة: {available_count}")
        
        # اختبار rotate_key
        new_key = manager.rotate_key()
        print(f"✅ تم التبديل إلى مفتاح جديد: {new_key}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار ApiKeyManager: {e}")
        return False

def test_advanced_youtube_analyzer():
    """اختبار AdvancedYouTubeAnalyzer مع الإصلاحات"""
    print("\n🎬 اختبار AdvancedYouTubeAnalyzer...")
    
    try:
        from modules.advanced_youtube_analyzer import AdvancedYouTubeAnalyzer
        
        # إنشاء محلل
        analyzer = AdvancedYouTubeAnalyzer()
        print(f"✅ تم إنشاء محلل YouTube")
        print(f"⏱️ الحد الأقصى للمدة: {analyzer.max_video_duration // 60} دقيقة")
        
        # اختبار تحليل المدة
        test_durations = [
            ("PT15M", 15, "15 دقيقة"),
            ("PT45M", 45, "45 دقيقة"),
            ("PT1H30M", 90, "ساعة ونصف")
        ]
        
        for duration_str, expected_minutes, description in test_durations:
            seconds = analyzer._parse_duration(duration_str)
            minutes = seconds // 60
            
            if minutes <= 30:
                status = "✅ مقبول"
            else:
                status = "❌ مرفوض"
            
            print(f"   {status} {description}: {minutes} دقيقة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار AdvancedYouTubeAnalyzer: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار الإصلاحات المطبقة")
    print("=" * 50)
    
    # اختبار ApiKeyManager
    api_test = test_api_key_manager()
    
    # اختبار AdvancedYouTubeAnalyzer
    youtube_test = test_advanced_youtube_analyzer()
    
    # النتيجة النهائية
    print("\n" + "=" * 50)
    print("📊 نتائج الاختبار:")
    print(f"   ApiKeyManager: {'✅ نجح' if api_test else '❌ فشل'}")
    print(f"   AdvancedYouTubeAnalyzer: {'✅ نجح' if youtube_test else '❌ فشل'}")
    
    if api_test and youtube_test:
        print("\n🎉 جميع الإصلاحات تعمل بنجاح!")
        print("💡 يمكنك الآن تشغيل البوت بدون أخطاء ApiKeyManager")
    else:
        print("\n⚠️ بعض الاختبارات فشلت - يرجى مراجعة الأخطاء")

if __name__ == "__main__":
    main()
