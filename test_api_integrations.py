#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تكامل APIs المتقدمة
"""

import asyncio
import sys
import os
from datetime import datetime

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.api_integrations import APIIntegrationManager, CoreWebVitalsAnalyzer, KeywordResearchAPI
from modules.performance_monitor import PerformanceMonitor

class APIIntegrationTester:
    """فئة اختبار تكامل APIs"""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = datetime.now()
    
    async def run_comprehensive_api_tests(self):
        """تشغيل اختبار شامل لتكامل APIs"""
        print("🚀 بدء اختبار تكامل APIs المتقدمة...\n")
        
        tests = [
            ("Core Web Vitals Analyzer", self.test_core_web_vitals),
            ("Keyword Research API", self.test_keyword_research),
            ("Performance Monitor", self.test_performance_monitor),
            ("API Rate Limiting", self.test_rate_limiting),
            ("Error Handling", self.test_error_handling),
            ("Data Caching", self.test_data_caching),
            ("Report Generation", self.test_report_generation)
        ]
        
        for test_name, test_func in tests:
            try:
                print(f"🔍 اختبار {test_name}...")
                result = await test_func()
                self.test_results[test_name] = result
                
                if result['success']:
                    print(f"✅ {test_name}: نجح")
                    if 'details' in result:
                        for key, value in result['details'].items():
                            print(f"   • {key}: {value}")
                else:
                    print(f"❌ {test_name}: فشل - {result.get('error', 'خطأ غير محدد')}")
                
                print()
                
            except Exception as e:
                print(f"❌ {test_name}: خطأ في الاختبار - {e}\n")
                self.test_results[test_name] = {'success': False, 'error': str(e)}
        
        await self.generate_api_test_report()
    
    async def test_core_web_vitals(self):
        """اختبار محلل Core Web Vitals"""
        try:
            async with APIIntegrationManager() as api_manager:
                analyzer = CoreWebVitalsAnalyzer(api_manager)
                
                # اختبار تحليل صفحة
                test_url = "https://example.com"
                analysis = await analyzer.analyze_page_performance(test_url)
                
                # التحقق من النتائج
                has_vitals = 'core_web_vitals' in analysis
                has_recommendations = 'recommendations' in analysis
                has_score = 'overall_score' in analysis
                
                # تحليل Core Web Vitals
                vitals_quality = 'جيد'
                if analysis.get('core_web_vitals'):
                    lcp_status = analysis['core_web_vitals'].get('lcp', {}).get('status', 'غير محدد')
                    fid_status = analysis['core_web_vitals'].get('fid', {}).get('status', 'غير محدد')
                    cls_status = analysis['core_web_vitals'].get('cls', {}).get('status', 'غير محدد')
                    
                    good_metrics = sum(1 for status in [lcp_status, fid_status, cls_status] if status == 'جيد')
                    if good_metrics >= 2:
                        vitals_quality = 'ممتاز'
                    elif good_metrics == 1:
                        vitals_quality = 'متوسط'
                    else:
                        vitals_quality = 'يحتاج تحسين'
                
                return {
                    'success': True,
                    'message': 'محلل Core Web Vitals يعمل بكفاءة',
                    'details': {
                        'تحليل متوفر': has_vitals,
                        'توصيات متوفرة': has_recommendations,
                        'نقاط إجمالية': has_score,
                        'النقاط': analysis.get('overall_score', 0),
                        'جودة المقاييس': vitals_quality,
                        'عدد التوصيات': len(analysis.get('recommendations', []))
                    }
                }
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_keyword_research(self):
        """اختبار API بحث الكلمات المفتاحية"""
        try:
            async with APIIntegrationManager() as api_manager:
                keyword_api = KeywordResearchAPI(api_manager)
                
                # اختبار بحث شامل
                test_keywords = ['gaming', 'minecraft', 'video games']
                research_results = await keyword_api.comprehensive_keyword_research(test_keywords)
                
                # التحقق من النتائج
                has_opportunities = len(research_results.get('keyword_opportunities', [])) > 0
                has_competitor_data = len(research_results.get('competitor_keywords', [])) > 0
                has_trending = len(research_results.get('trending_keywords', [])) > 0
                has_long_tail = len(research_results.get('long_tail_suggestions', [])) > 0
                
                # حساب إحصائيات
                total_suggestions = (
                    len(research_results.get('keyword_opportunities', [])) +
                    len(research_results.get('competitor_keywords', [])) +
                    len(research_results.get('long_tail_suggestions', []))
                )
                
                return {
                    'success': True,
                    'message': 'API بحث الكلمات المفتاحية فعال',
                    'details': {
                        'كلمات مستهدفة': len(test_keywords),
                        'فرص اكتُشفت': has_opportunities,
                        'بيانات منافسين': has_competitor_data,
                        'كلمات رائجة': has_trending,
                        'اقتراحات طويلة': has_long_tail,
                        'إجمالي الاقتراحات': total_suggestions,
                        'معدل النجاح': f"{(sum([has_opportunities, has_competitor_data, has_trending, has_long_tail]) / 4) * 100:.1f}%"
                    }
                }
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_performance_monitor(self):
        """اختبار مراقب الأداء"""
        try:
            monitor = PerformanceMonitor()
            
            # اختبار مراقبة Core Web Vitals
            test_url = "https://example.com"
            vitals_report = await monitor._monitor_core_web_vitals(test_url)
            
            # اختبار مراقبة SEO
            seo_report = await monitor._monitor_seo_performance(test_url)
            
            # اختبار مراقبة الكلمات المفتاحية
            keyword_report = await monitor._monitor_keyword_rankings()
            
            # التحقق من التقارير
            reports_generated = sum(1 for report in [vitals_report, seo_report, keyword_report] if report)
            
            return {
                'success': reports_generated > 0,
                'message': 'مراقب الأداء يعمل بكفاءة',
                'details': {
                    'تقارير مُولدة': reports_generated,
                    'تقرير Core Web Vitals': bool(vitals_report),
                    'تقرير SEO': bool(seo_report),
                    'تقرير الكلمات المفتاحية': bool(keyword_report),
                    'حالة المراقبة': 'نشط' if reports_generated == 3 else 'جزئي'
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_rate_limiting(self):
        """اختبار حدود معدل الطلبات"""
        try:
            api_manager = APIIntegrationManager()
            
            # اختبار حدود المعدل
            rate_limits = api_manager.rate_limits
            
            # محاكاة طلبات متعددة
            test_requests = 5
            successful_requests = 0
            
            for i in range(test_requests):
                try:
                    # محاكاة طلب API
                    await asyncio.sleep(0.1)  # تأخير قصير
                    successful_requests += 1
                except Exception:
                    pass
            
            success_rate = (successful_requests / test_requests) * 100
            
            return {
                'success': True,
                'message': 'نظام حدود المعدل يعمل بشكل صحيح',
                'details': {
                    'طلبات اختبار': test_requests,
                    'طلبات ناجحة': successful_requests,
                    'معدل النجاح': f"{success_rate:.1f}%",
                    'حدود مُعرفة': len(rate_limits),
                    'أنواع APIs': list(rate_limits.keys())
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_error_handling(self):
        """اختبار معالجة الأخطاء"""
        try:
            async with APIIntegrationManager() as api_manager:
                analyzer = CoreWebVitalsAnalyzer(api_manager)
                
                # اختبار URL غير صحيح
                invalid_url = "invalid-url"
                result = await analyzer.analyze_page_performance(invalid_url)
                
                # اختبار API غير متوفر
                keyword_api = KeywordResearchAPI(api_manager)
                empty_keywords = []
                keyword_result = await keyword_api.comprehensive_keyword_research(empty_keywords)
                
                # التحقق من معالجة الأخطاء
                handles_invalid_url = isinstance(result, dict)
                handles_empty_input = isinstance(keyword_result, dict)
                
                return {
                    'success': True,
                    'message': 'معالجة الأخطاء تعمل بشكل صحيح',
                    'details': {
                        'معالجة URL غير صحيح': handles_invalid_url,
                        'معالجة مدخلات فارغة': handles_empty_input,
                        'استجابة آمنة': handles_invalid_url and handles_empty_input,
                        'نوع الاستجابة': 'dict' if handles_invalid_url else 'other'
                    }
                }
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_data_caching(self):
        """اختبار تخزين البيانات المؤقت"""
        try:
            api_manager = APIIntegrationManager()
            
            # اختبار التخزين المؤقت
            cache_key = "test_key"
            test_data = {"test": "data", "timestamp": datetime.now().isoformat()}
            
            # تخزين البيانات
            api_manager.cache[cache_key] = test_data
            
            # استرجاع البيانات
            cached_data = api_manager.cache.get(cache_key)
            
            # التحقق من التخزين المؤقت
            cache_works = cached_data == test_data
            cache_size = len(api_manager.cache)
            
            return {
                'success': cache_works,
                'message': 'نظام التخزين المؤقت يعمل بكفاءة',
                'details': {
                    'تخزين ناجح': cache_works,
                    'حجم التخزين المؤقت': cache_size,
                    'نوع البيانات': type(cached_data).__name__,
                    'مطابقة البيانات': cached_data == test_data
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_report_generation(self):
        """اختبار توليد التقارير"""
        try:
            monitor = PerformanceMonitor()
            
            # توليد تقرير شامل
            vitals_report = {'overall_score': 85, 'status': 'جيد'}
            seo_report = {'overall_seo_score': 78}
            keyword_report = {'average_position': 15}
            competitor_report = {'market_position': 'متقدم'}
            
            comprehensive_report = await monitor._generate_comprehensive_report(
                vitals_report, seo_report, keyword_report, competitor_report
            )
            
            # التحقق من التقرير
            has_timestamp = 'timestamp' in comprehensive_report
            has_overall_score = 'overall_performance_score' in comprehensive_report
            has_recommendations = 'recommendations' in comprehensive_report
            has_priorities = 'improvement_priorities' in comprehensive_report
            
            report_quality = sum([has_timestamp, has_overall_score, has_recommendations, has_priorities])
            
            return {
                'success': report_quality >= 3,
                'message': 'توليد التقارير يعمل بكفاءة عالية',
                'details': {
                    'تقرير مُولد': bool(comprehensive_report),
                    'طابع زمني': has_timestamp,
                    'نقاط إجمالية': has_overall_score,
                    'توصيات': has_recommendations,
                    'أولويات': has_priorities,
                    'جودة التقرير': f"{(report_quality / 4) * 100:.1f}%",
                    'النقاط الإجمالية': comprehensive_report.get('overall_performance_score', 0)
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def generate_api_test_report(self):
        """إنشاء تقرير اختبار APIs"""
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results.values() if result.get('success', False))
        success_rate = (successful_tests / total_tests) * 100 if total_tests > 0 else 0
        
        duration = (datetime.now() - self.start_time).total_seconds()
        
        print("\n" + "="*80)
        print("📊 تقرير اختبار تكامل APIs المتقدمة")
        print("="*80)
        
        print(f"🎯 معدل النجاح الإجمالي: {success_rate:.1f}% ({successful_tests}/{total_tests})")
        print(f"⏱️ مدة الاختبار: {duration:.1f} ثانية")
        print(f"📅 تاريخ الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        print("\n📋 تفاصيل النتائج:")
        for test_name, result in self.test_results.items():
            status = "✅ نجح" if result.get('success', False) else "❌ فشل"
            print(f"  • {test_name}: {status}")
            
            if result.get('success') and 'details' in result:
                for key, value in result['details'].items():
                    print(f"    - {key}: {value}")
        
        # تقييم عام
        if success_rate >= 90:
            grade = "A+ ممتاز"
            message = "🎉 جميع APIs تعمل بكفاءة استثنائية!"
        elif success_rate >= 80:
            grade = "A جيد جداً"
            message = "👍 معظم APIs تعمل بشكل ممتاز"
        elif success_rate >= 70:
            grade = "B جيد"
            message = "⚠️ APIs تعمل بشكل جيد مع تحسينات بسيطة"
        else:
            grade = "C يحتاج تطوير"
            message = "🔧 يحتاج إلى مراجعة وتحسين عدة APIs"
        
        print(f"\n🏆 التقييم العام: {grade}")
        print(f"💬 التوصية: {message}")
        
        # توصيات التحسين
        print(f"\n🔧 توصيات التحسين:")
        if success_rate < 100:
            print("  • إضافة مفاتيح APIs الحقيقية للحصول على بيانات دقيقة")
            print("  • تحسين معالجة الأخطاء للحالات الاستثنائية")
            print("  • تطوير نظام تخزين مؤقت أكثر تقدماً")
        
        print("  • دمج المزيد من مصادر البيانات")
        print("  • تطوير لوحة تحكم لمراقبة APIs")
        print("  • إضافة تنبيهات تلقائية للمشاكل")

async def main():
    """الدالة الرئيسية"""
    tester = APIIntegrationTester()
    await tester.run_comprehensive_api_tests()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⌨️ تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
