#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام البحث العميق المجاني باستخدام MCP
"""

import asyncio
import sys
import os

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger

async def test_free_search_engines():
    """اختبار محركات البحث المجانية"""
    print("🔍 اختبار محركات البحث المجانية...")
    print("-" * 50)
    
    try:
        from modules.free_mcp_search import free_mcp_search
        
        # اختبار DuckDuckGo
        print("🦆 اختبار DuckDuckGo...")
        duckduckgo_results = await free_mcp_search._search_duckduckgo("gaming news")
        print(f"   النتائج: {len(duckduckgo_results)}")
        
        # اختبار SearX
        print("🔍 اختبار SearX...")
        searx_results = await free_mcp_search._search_searx("gaming news")
        print(f"   النتائج: {len(searx_results)}")
        
        # اختبار Bing Scraping
        print("🌐 اختبار Bing Scraping...")
        bing_results = await free_mcp_search._scrape_bing("gaming news")
        print(f"   النتائج: {len(bing_results)}")
        
        total_results = len(duckduckgo_results) + len(searx_results) + len(bing_results)
        
        if total_results > 0:
            print(f"✅ إجمالي النتائج: {total_results}")
            return True
        else:
            print("❌ لم يتم العثور على نتائج")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار محركات البحث: {e}")
        return False

async def test_ai_analysis():
    """اختبار تحليل الذكاء الاصطناعي المجاني"""
    print("\n🤖 اختبار تحليل الذكاء الاصطناعي المجاني...")
    print("-" * 50)
    
    try:
        from modules.free_mcp_search import free_mcp_search
        
        # محتوى تجريبي للتحليل
        test_results = [
            {
                'title': 'New Gaming Console Released',
                'content': 'A new gaming console has been announced with advanced features for gamers.',
                'url': 'https://example.com/gaming-news',
                'source': 'Test'
            }
        ]
        
        print("🧠 تحليل المحتوى باستخدام Hugging Face...")
        analyzed_results = await free_mcp_search._analyze_with_huggingface(test_results, "gaming news")
        
        if analyzed_results and len(analyzed_results) > 0:
            result = analyzed_results[0]
            ai_quality = result.get('ai_quality_score', 0)
            ai_relevance = result.get('ai_relevance_score', 0)
            
            print(f"   جودة AI: {ai_quality}/10")
            print(f"   صلة AI: {ai_relevance}/10")
            print("✅ تحليل الذكاء الاصطناعي يعمل")
            return True
        else:
            print("❌ فشل في تحليل الذكاء الاصطناعي")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار AI: {e}")
        return False

async def test_full_free_search():
    """اختبار البحث المجاني الكامل"""
    print("\n🚀 اختبار البحث المجاني الكامل...")
    print("-" * 50)
    
    try:
        from modules.free_mcp_search import free_mcp_search
        
        # تشغيل البحث الكامل
        results = await free_mcp_search.free_deep_search("gaming news today", max_results=10)
        
        if results:
            print(f"✅ تم العثور على {len(results)} نتيجة")
            
            # عرض أفضل 3 نتائج
            print("\n📋 أفضل النتائج:")
            for i, result in enumerate(results[:3], 1):
                title = result.get('title', 'بدون عنوان')[:50]
                source = result.get('source', 'مصدر غير محدد')
                quality = result.get('quality_score', 0)
                total_score = result.get('total_score', 0)
                
                print(f"   {i}. {title}...")
                print(f"      المصدر: {source}")
                print(f"      الجودة: {quality}/10, النقاط الإجمالية: {total_score:.1f}")
            
            return True
        else:
            print("❌ لم يتم العثور على نتائج")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في البحث الكامل: {e}")
        return False

async def test_mcp_server_connection():
    """اختبار الاتصال بـ MCP Server المحلي"""
    print("\n🖥️ اختبار MCP Server المحلي...")
    print("-" * 50)
    
    try:
        from modules.free_mcp_search import free_mcp_search
        
        # محاولة إعداد MCP Server
        server_available = await free_mcp_search.setup_local_mcp_server()
        
        if server_available:
            print("✅ MCP Server محلي متاح")
            
            # اختبار البحث عبر MCP
            mcp_results = await free_mcp_search.search_with_local_mcp("gaming news")
            print(f"   نتائج MCP: {len(mcp_results)}")
            
            return True
        else:
            print("💡 MCP Server محلي غير متاح")
            print("   يمكنك تشغيله باستخدام: python mcp_server_setup.py")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار MCP Server: {e}")
        return False

async def test_integration_with_agent():
    """اختبار التكامل مع الوكيل"""
    print("\n🔗 اختبار التكامل مع الوكيل...")
    print("-" * 50)
    
    try:
        from modules.content_scraper import ContentScraper
        
        scraper = ContentScraper()
        
        # اختبار البحث المتقدم مع النظام المجاني
        results = await scraper.advanced_search_and_extract(
            query="gaming news",
            max_results=5
        )
        
        if results:
            print(f"✅ التكامل ناجح: {len(results)} نتيجة")
            
            # تحليل مصادر النتائج
            free_search_results = 0
            traditional_results = 0
            
            for result in results:
                search_method = result.get('search_method', '')
                if any(method in search_method for method in ['duckduckgo_free', 'searx_free', 'bing_scrape']):
                    free_search_results += 1
                else:
                    traditional_results += 1
            
            print(f"   • نتائج البحث المجاني: {free_search_results}")
            print(f"   • نتائج البحث التقليدي: {traditional_results}")
            
            if free_search_results > 0:
                print("🎉 البحث المجاني يعمل مع الوكيل!")
                return True
            else:
                print("⚠️ لم يتم استخدام البحث المجاني")
                return False
        else:
            print("❌ لم يتم العثور على نتائج")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل: {e}")
        return False

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 اختبار نظام البحث العميق المجاني")
    print("=" * 60)
    
    tests = [
        ("محركات البحث المجانية", test_free_search_engines()),
        ("تحليل الذكاء الاصطناعي", test_ai_analysis()),
        ("البحث المجاني الكامل", test_full_free_search()),
        ("MCP Server المحلي", test_mcp_server_connection()),
        ("التكامل مع الوكيل", test_integration_with_agent())
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}...")
        try:
            result = await test_func
            if result:
                passed_tests += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار:")
    print(f"   • الاختبارات الناجحة: {passed_tests}/{total_tests}")
    print(f"   • معدل النجاح: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests >= 3:  # على الأقل 3 اختبارات ناجحة
        print("\n🎉 نظام البحث المجاني جاهز للاستخدام!")
        print("\n💡 المزايا:")
        print("   • بحث مجاني 100% بدون مفاتيح API")
        print("   • تحليل ذكي بالذكاء الاصطناعي")
        print("   • مصادر متنوعة وموثوقة")
        print("   • تكامل سلس مع الوكيل")
        
        print("\n🚀 للاستفادة الكاملة:")
        print("   1. شغل MCP Server: python mcp_server_setup.py")
        print("   2. شغل الوكيل: python main.py")
        print("   3. استمتع بالبحث المحسن!")
        
        return True
    else:
        print("\n⚠️ النظام يحتاج تحسين")
        print("\n🔧 خطوات مقترحة:")
        print("   • تحقق من اتصال الإنترنت")
        print("   • تأكد من تثبيت المكتبات المطلوبة")
        print("   • راجع ملفات السجلات للأخطاء")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
