# وحدة النشر على بلوجر وتيليجرام
import asyncio
import aiohttp
import json
import random
import re
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import telegram
from telegram import Bot
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
import os
from .logger import logger
from .database import db
from config.settings import BotConfig, ContentConfig

class BloggerPublisher:
    """ناشر المحتوى على مدونة بلوجر"""
    
    def __init__(self, client_secret_file: str, blog_id: str):
        self.client_secret_file = client_secret_file
        self.blog_id = blog_id
        self.credentials = None
        self.service = None
        self.setup_blogger_api()
    
    def setup_blogger_api(self):
        """إعداد API بلوجر"""
        try:
            # إعداد OAuth2 للمصادقة من ملف الأسرار باستخدام تدفق التطبيقات المثبتة
            self.flow = InstalledAppFlow.from_client_secrets_file(
                self.client_secret_file,
                scopes=['https://www.googleapis.com/auth/blogger']
            )
            
            # محاولة تحميل الرموز المحفوظة
            self._load_saved_credentials()
            
            logger.info("✅ تم إعداد Blogger API بنجاح")
            
        except Exception as e:
            logger.error("❌ فشل في إعداد Blogger API", e)
            raise
    
    def _load_saved_credentials(self):
        """تحميل الرموز المحفوظة"""
        token_file = "config/blogger_token.json"
        
        if os.path.exists(token_file):
            try:
                self.credentials = Credentials.from_authorized_user_file(token_file)
                
                if self.credentials and self.credentials.valid:
                    self.service = build('blogger', 'v3', credentials=self.credentials)
                    logger.info("✅ تم تحميل رموز Blogger المحفوظة")
                elif self.credentials and self.credentials.expired and self.credentials.refresh_token:
                    from google.auth.transport.requests import Request
                    self.credentials.refresh(Request())
                    self.service = build('blogger', 'v3', credentials=self.credentials)
                    self._save_credentials()
                    logger.info("✅ تم تجديد رموز Blogger")
                else:
                    logger.warning("⚠️ رموز Blogger غير صالحة، يتطلب مصادقة جديدة")
                    
            except Exception as e:
                logger.warning("⚠️ فشل في تحميل رموز Blogger المحفوظة", e)
    
    def authenticate(self) -> str:
        """بدء عملية المصادقة وإرجاع رابط التفويض"""
        try:
            auth_url, _ = self.flow.authorization_url(prompt='consent')
            logger.info("🔐 تم إنشاء رابط التفويض لـ Blogger")
            return auth_url
            
        except Exception as e:
            logger.error("❌ فشل في إنشاء رابط التفويض", e)
            raise
    
    def complete_authentication(self, authorization_response: str):
        """إكمال عملية المصادقة باستخدام الاستجابة"""
        try:
            self.flow.fetch_token(authorization_response=authorization_response)
            self.credentials = self.flow.credentials
            self.service = build('blogger', 'v3', credentials=self.credentials)
            
            # حفظ الرموز
            self._save_credentials()
            
            logger.info("✅ تم إكمال مصادقة Blogger بنجاح")
            
        except Exception as e:
            logger.error("❌ فشل في إكمال مصادقة Blogger", e)
            raise

    def run_local_server_authentication(self):
        """تشغيل خادم محلي مؤقت لإكمال المصادقة تلقائياً"""
        try:
            self.credentials = self.flow.run_local_server(port=8080)
            self.service = build('blogger', 'v3', credentials=self.credentials)
            self._save_credentials()
            logger.info("✅ تمت مصادقة Blogger بنجاح باستخدام الخادم المحلي.")
        except Exception as e:
            logger.error("❌ فشل في المصادقة التلقائية لـ Blogger", e)
            raise
    
    def _save_credentials(self):
        """حفظ رموز المصادقة"""
        try:
            os.makedirs("config", exist_ok=True)
            
            with open("config/blogger_token.json", 'w') as token_file:
                token_file.write(self.credentials.to_json())
                
            logger.info("💾 تم حفظ رموز Blogger")
            
        except Exception as e:
            logger.error("❌ فشل في حفظ رموز Blogger", e)
    
    def test_connection(self) -> bool:
        """اختبار الاتصال بمدونة بلوجر"""
        try:
            if not self.service:
                logger.error("❌ خدمة Blogger غير جاهزة")
                return False
            
            # محاولة جلب معلومات المدونة
            blog = self.service.blogs().get(blogId=self.blog_id).execute()
            
            logger.info(f"✅ تم الاتصال بالمدونة: {blog.get('name', 'غير محدد')}")
            return True
            
        except HttpError as e:
            logger.error(f"❌ خطأ HTTP في الاتصال بالمدونة: {e.resp.status}", e)
            return False
        except telegram.error.TimedOut:
            logger.warning("⚠️ انتهت مهلة الاتصال بـ Telegram، قد تكون هناك مشكلة في الشبكة")
            return False
        except Exception as e:
            logger.error(f"❌ فشل في اختبار اتصال تيليجرام", e)
            return False
    
    def publish_article(self, article: Dict) -> Optional[str]:
        """نشر مقال على بلوجر"""
        try:
            if not self.service:
                logger.error("❌ خدمة Blogger غير جاهزة للنشر")
                return None
            
            # تحضير محتوى المنشور
            post_body = self._prepare_post_body(article)
            
            # نشر المنشور
            post = self.service.posts().insert(
                blogId=self.blog_id,
                body=post_body
            ).execute()
            
            post_url = post.get('url')
            post_id = post.get('id')
            
            logger.log_publishing(
                "Blogger",
                article['title'],
                "نجح",
                post_url
            )
            
            # تسجيل في قاعدة البيانات
            db.update_performance_stats(
                articles_published=1,
                api_calls_blogger=1
            )
            
            logger.info(f"✅ تم نشر المقال على بلوجر: {post_url}")
            
            return post_url
            
        except HttpError as e:
            logger.error(f"❌ خطأ HTTP في نشر المقال: {e.resp.status}", e)
            db.log_error("blogger_publish_error", str(e), article.get('title'))
            return None
        except Exception as e:
            logger.error("❌ فشل في نشر المقال على بلوجر", e)
            db.log_error("blogger_general_error", str(e), article.get('title'))
            return None
    
    def _prepare_post_body(self, article: Dict) -> Dict:
        """تحضير محتوى المنشور لبلوجر"""
        
        # تنسيق المحتوى بـ HTML
        content_html = self._format_content_as_html(article['content'])
        
        # إضافة صورة إذا كانت متوفرة
        # إضافة الصور
        image_urls = article.get('image_urls', [])
        if image_urls:
            # إضافة الصورة الرئيسية في البداية
            main_image_url = image_urls[0]
            content_html = f'<div class="separator" style="clear: both; text-align: center;"><a href="{main_image_url}" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="600" data-original-width="800" height="480" src="{main_image_url}" width="640" /></a></div><br><br>' + content_html
            
            # إدراج الصور المتبقية بعد الفقرة الأولى
            if len(image_urls) > 1:
                remaining_images_html = ""
                for img_url in image_urls[1:]:
                    remaining_images_html += f'<div class="separator" style="clear: both; text-align: center;"><a href="{img_url}" imageanchor="1" style="margin-left: 1em; margin-right: 1em;"><img border="0" data-original-height="600" data-original-width="800" height="480" src="{img_url}" width="640" /></a></div><br>'
                
                # البحث عن نهاية الفقرة الأولى لإدراج الصور
                first_paragraph_end = content_html.find('<br><br>')
                if first_paragraph_end != -1:
                    content_html = content_html[:first_paragraph_end + 8] + remaining_images_html + content_html[first_paragraph_end + 8:]
                else:
                    # إذا لم يتم العثور على فاصل فقرات، أضف الصور في النهاية
                    content_html += remaining_images_html
        
        # إضافة الكلمات المفتاحية في النهاية
        if article.get('keywords'):
            keywords_html = '<br><br><strong>الكلمات المفتاحية:</strong> ' + ', '.join(article['keywords'])
            content_html += keywords_html
        
        post_body = {
            'title': article['title'],
            'content': content_html,
            'labels': article.get('keywords', [])[:5],  # بلوجر يدعم حتى 5 تصنيفات
        }
        
        # إضافة الوصف التعريفي إذا كان متوفراً
        if article.get('meta_description'):
            post_body['customMetaData'] = article['meta_description']
        
        return post_body
    
    def _format_content_as_html(self, content: str) -> str:
        """تنسيق المحتوى كـ HTML"""
        
        # استبدال فواصل الأسطر
        content = content.replace('\n', '<br>')
        
        # تحويل العناوين الفرعية
        content = content.replace('## ', '<h2>').replace('<br>##', '</h2><br><h2>')
        content = content.replace('### ', '<h3>').replace('<br>###', '</h3><br><h3>')
        
        # إغلاق العناوين المفتوحة
        if '<h2>' in content and '</h2>' not in content:
            content += '</h2>'
        if '<h3>' in content and '</h3>' not in content:
            content += '</h3>'
            
        # إضافة مسافات بين الفقرات
        content = content.replace('<br><br>', '<br><p>&nbsp;</p>')
        
        return f'<div>{content}</div>'

class TelegramPublisher:
    """ناشر المحتوى على تيليجرام"""
    
    def __init__(self, bot_token: str, channel_id: str):
        self.bot_token = bot_token
        self.channel_id = channel_id
        self.bot = None
        self.setup_telegram_bot()
        
        # تنسيقات النشر المختلفة
        self.format_templates = {
            'ملخص_قصير': self._format_short_summary,
            'نقاط_رئيسية': self._format_key_points,
            'سؤال_وجواب': self._format_qa,
            'اقتباس': self._format_quote,
            'صورة_مع_نص': self._format_image_text
        }
    
    def setup_telegram_bot(self):
        """إعداد بوت تيليجرام"""
        try:
            self.bot = Bot(token=self.bot_token)
            logger.info("✅ تم إعداد بوت تيليجرام بنجاح")
            
        except Exception as e:
            logger.error("❌ فشل في إعداد بوت تيليجرام", e)
            raise
    
    async def test_connection(self) -> bool:
        """اختبار الاتصال ببوت تيليجرام"""
        try:
            bot_info = await self.bot.get_me()
            logger.info(f"✅ تم الاتصال ببوت تيليجرام: @{bot_info.username}")
            return True
            
        except Exception as e:
            logger.error("❌ فشل في اختبار اتصال تيليجرام", e)
            return False
    
    async def publish_article(self, article: Dict, blogger_url: str = None, format_type: str = None) -> Optional[int]:
        """نشر مقال على تيليجرام"""
        try:
            # اختيار تنسيق النشر
            if not format_type:
                format_type = random.choice(list(self.format_templates.keys()))
            
            # تحضير المحتوى حسب التنسيق المختار
            message_content = self.format_templates[format_type](article, blogger_url)
            
            # نشر الرسالة
            message = await self.bot.send_message(
                chat_id=self.channel_id,
                text=message_content,
                parse_mode='Markdown',
                disable_web_page_preview=False
            )
            
            message_id = message.message_id
            
            logger.log_publishing(
                "Telegram",
                article['title'],
                "نجح",
                f"https://t.me/{self.channel_id.replace('@', '')}/{message_id}"
            )
            
            # تسجيل في قاعدة البيانات
            db.update_performance_stats(api_calls_telegram=1)
            
            logger.info(f"✅ تم نشر المقال على تيليجرام بتنسيق: {format_type}")
            
            return message_id
            
        except Exception as e:
            logger.error(f"❌ فشل في نشر المقال على تيليجرام بتنسيق {format_type}", e)
            db.log_error("telegram_publish_error", str(e), article.get('title'))
            return None
    
    def _format_short_summary(self, article: Dict, blogger_url: str = None) -> str:
        """تنسيق ملخص قصير وجذاب"""
        title = article['title']
        summary = article.get('meta_description', article['content'][:150] + '...')
        dialect_greeting, emoji = self._get_dialect_elements(article.get('dialect', 'egyptian'))

        message = f"""
{emoji} *خبر ماين كرافتي جديد وصللل* {emoji}

*{title}*

{summary}

{dialect_greeting} تبغى تعرف السالفة كاملة؟ 😉

"""
        if blogger_url:
            message += f"👇 *اضغط هنا وكمل قراءة* 👇\n[اقرأ المقال الكامل]({blogger_url})\n"

        message += f"\n#ماينكرافت #أخبار_ماينكرافت #{article.get('category', 'العاب').replace(' ', '_')}"
        return message
    
    def _format_key_points(self, article: Dict, blogger_url: str = None) -> str:
        """تنسيق نقاط رئيسية بأسلوب شبابي"""
        title = article['title']
        dialect_greeting, emoji = self._get_dialect_elements(article.get('dialect', 'egyptian'))

        # استخراج نقاط رئيسية (H2, H3, أو قوائم)
        key_points = re.findall(r'(?:##|###|\*|-|•)\s*(.*?)\n', article['content'])
        if not key_points:
            key_points = [s.strip() for s in article['content'].split('.')[:3] if len(s.strip()) > 20]

        message = f"""
💥 *الزبدة من آخر خبر في ماين كرافت* 💥

*{title}*

{dialect_greeting} وش السالفة؟ شوف أهم النقاط:

"""
        point_emojis = ['1️⃣', '2️⃣', '3️⃣', '4️⃣']
        for i, point in enumerate(key_points[:4]):
            message += f"{point_emojis[i]} {point.strip()}\n"

        if blogger_url:
            message += f"\n\n🧠 *ودك تفهم أكثر؟* \n[التفاصيل الكاملة هنا]({blogger_url})\n"
        
        message += f"\n#ماين_كرافت #تحديثات #{article.get('category', 'جديد').replace(' ', '_')}"
        return message
    
    def _format_qa(self, article: Dict, blogger_url: str = None) -> str:
        """تنسيق سؤال وجواب"""
        
        title = article['title']
        content_preview = article['content'][:300] + '...' if len(article['content']) > 300 else article['content']
        
        # إنشاء سؤال من العنوان
        if 'تحديث' in title:
            question = f"ما الجديد في {title}؟"
        elif 'سر' in title or 'مخفي' in title:
            question = f"ما هو {title}؟"
        elif 'إضافة' in title or 'مود' in title:
            question = f"كيف يمكن الاستفادة من {title}؟"
        else:
            question = f"ما تفاصيل {title}؟"
        
        dialect_greeting, _ = self._get_dialect_elements(article.get('dialect', 'egyptian'))
        
        message = f"""
❓ *سؤال وجواب - ماين كرافت*

**سؤال:** {question}

**الجواب:** {content_preview}

{dialect_greeting} للحصول على الإجابة الشاملة والتفاصيل الكاملة...

"""
        
        if blogger_url:
            message += f"📋 [اقرأ الإجابة الكاملة]({blogger_url})\n"
        
        message += f"\n📅 {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        
        return message
    
    def _format_quote(self, article: Dict, blogger_url: str = None) -> str:
        """تنسيق اقتباس"""
        
        # استخراج جملة مثيرة من المحتوى
        sentences = article['content'].split('.')
        quote = ""
        
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 50 and len(sentence) < 150:
                quote = sentence
                break
        
        if not quote:
            quote = article['content'][:120] + '...' if len(article['content']) > 120 else article['content']
        
        dialect_greeting, _ = self._get_dialect_elements(article.get('dialect', 'egyptian'))
        
        message = f"""
💬 *اقتباس من أحدث أخبار ماين كرافت*

*"{quote}"*

من مقال: **{article['title']}**

{dialect_greeting} اكتشف المزيد من التفاصيل المثيرة!

"""
        
        if blogger_url:
            message += f"📰 [اقرأ المقال كاملاً]({blogger_url})\n"
        
        message += f"\n📅 {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        
        return message
    
    def _format_image_text(self, article: Dict, blogger_url: str = None) -> str:
        """تنسيق صورة مع نص"""
        
        content_preview = article['content'][:250] + '...' if len(article['content']) > 250 else article['content']
        dialect_greeting, _ = self._get_dialect_elements(article.get('dialect', 'egyptian'))
        
        message = f"""
🖼️ *صورة اليوم من ماين كرافت*

*{article['title']}*

{content_preview}

{dialect_greeting} شاهدوا المزيد من الصور والتفاصيل!

"""
        
        if blogger_url:
            message += f"🔍 [شاهد المقال مع الصور]({blogger_url})\n"
        
        message += f"\n📅 {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        
        return message
    
    def _get_dialect_elements(self, dialect: str) -> Tuple[str, str]:
        """الحصول على تحية وإيموجي حسب اللهجة"""
        if dialect == "egyptian":
            greetings = ["يا جدعان الحقوا!", "شوفوا الحلاوة دي!", "كلام كبار أوي!"]
            emojis = ["🇪🇬", "🤠", "😎"]
        elif dialect == "saudi":
            greetings = ["يا عيال لا يفوتكم!", "شيء من الآخر!", "وش تنتظرون؟"]
            emojis = ["🇸🇦", "🔥", "🚀"]
        else:
            greetings = ["يا أصدقاء، انظروا!", "خبر رائع!", "ما رأيكم بهذا؟"]
            emojis = ["🎮", "🌟", "💡"]
        
        return random.choice(greetings), random.choice(emojis)
    
    async def publish_with_multiple_dialects(self, article: Dict, blogger_url: str = None) -> List[int]:
        """نشر المقال بلهجتين مختلفتين"""
        message_ids = []
        
        # نشر باللهجة المصرية
        egyptian_article = article.copy()
        egyptian_article['dialect'] = 'egyptian'
        
        msg_id_1 = await self.publish_article(egyptian_article, blogger_url)
        if msg_id_1:
            message_ids.append(msg_id_1)
        
        # انتظار قصير بين المنشورات
        await asyncio.sleep(2)
        
        # نشر باللهجة السعودية
        saudi_article = article.copy()
        saudi_article['dialect'] = 'saudi'
        
        msg_id_2 = await self.publish_article(saudi_article, blogger_url)
        if msg_id_2:
            message_ids.append(msg_id_2)
        
        return message_ids

class PublisherManager:
    """مدير النشر الموحد"""
    
    def __init__(self, blogger_config: Dict, telegram_config: Dict):
        self.blogger = BloggerPublisher(
            client_secret_file=BotConfig.BLOGGER_CLIENT_SECRET_FILE,
            blog_id=blogger_config['blog_id']
        )
        self.telegram = TelegramPublisher(**telegram_config)
    
    async def publish_complete_article(self, article: Dict) -> Dict:
        """نشر مقال كامل على جميع المنصات"""
        results = {
            'blogger_url': None,
            'telegram_message_ids': [],
            'success': False,
            'errors': []
        }
        
        try:
            # 1. النشر على بلوجر أولاً
            logger.info("📝 بدء النشر على بلوجر...")
            blogger_url = self.blogger.publish_article(article)
            
            if blogger_url:
                results['blogger_url'] = blogger_url
                logger.info(f"✅ تم النشر على بلوجر: {blogger_url}")
                
                # 2. النشر على تيليجرام مع رابط بلوجر
                logger.info("📱 بدء النشر على تيليجرام...")
                telegram_ids = await self.telegram.publish_with_multiple_dialects(
                    article, blogger_url
                )
                
                if telegram_ids:
                    results['telegram_message_ids'] = telegram_ids
                    results['success'] = True
                    logger.info(f"✅ تم النشر على تيليجرام: {len(telegram_ids)} رسالة")
                else:
                    results['errors'].append("فشل النشر على تيليجرام")
                    logger.warning("⚠️ فشل النشر على تيليجرام")
            
            else:
                results['errors'].append("فشل النشر على بلوجر")
                logger.error("❌ فشل النشر على بلوجر")
            
        except Exception as e:
            error_msg = f"خطأ في عملية النشر: {str(e)}"
            results['errors'].append(error_msg)
            logger.error("❌ خطأ في عملية النشر الشاملة", e)
        
        return results
    
    async def test_all_connections(self) -> Dict:
        """اختبار جميع الاتصالات"""
        results = {
            'blogger': False,
            'telegram': False,
            'overall': False
        }
        
        # اختبار بلوجر
        results['blogger'] = self.blogger.test_connection()
        
        # اختبار تيليجرام
        results['telegram'] = await self.telegram.test_connection()
        
        # النتيجة الإجمالية
        results['overall'] = results['blogger'] and results['telegram']
        
        if results['overall']:
            logger.info("✅ جميع الاتصالات تعمل بنجاح")
        else:
            logger.warning("⚠️ بعض الاتصالات لا تعمل بشكل صحيح")
        
        return results
