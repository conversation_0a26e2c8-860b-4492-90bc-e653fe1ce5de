# اختبار سريع لنظام إنشاء الصور بالذكاء الاصطناعي
import asyncio
import sys
import os

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def quick_test():
    """اختبار سريع للنظام الجديد"""
    
    print("🎨 اختبار سريع لنظام إنشاء الصور بالذكاء الاصطناعي")
    print("=" * 60)
    
    try:
        # استيراد الوحدات
        from modules.image_guard import ai_image_generator
        print("✅ تم استيراد مولد الصور بنجاح")
        
        # اختبار التكوين
        print("\n🔧 فحص التكوين...")
        print(f"   • مفتاح Freepik: {'✅ موجود' if ai_image_generator.freepik_api_key else '❌ غير موجود'}")
        print(f"   • مفتاح FluxAI: {'✅ موجود' if ai_image_generator.fluxai_api_key else '❌ غير موجود'}")
        
        # اختبار إنشاء prompt
        print("\n📝 اختبار إنشاء prompts...")
        
        test_article = {
            'title': 'أفضل ألعاب 2025',
            'content': 'مقال عن أفضل الألعاب المنتظرة في 2025',
            'keywords': ['gaming', 'video games', '2025'],
            'category': 'gaming_news'
        }
        
        visual_elements = ai_image_generator._analyze_article_for_visuals(
            test_article['title'], 
            test_article['content'], 
            test_article['keywords']
        )
        
        print(f"   • نوع المحتوى: {visual_elements['content_type']}")
        print(f"   • الموضوع الرئيسي: {visual_elements['main_theme']}")
        print(f"   • الألعاب المكتشفة: {visual_elements['game_names']}")
        
        prompts = ai_image_generator._create_optimized_prompts(visual_elements, 2)
        print(f"   • تم إنشاء {len(prompts)} prompt")
        
        for i, prompt in enumerate(prompts, 1):
            print(f"      {i}. {prompt['prompt'][:80]}...")
        
        # اختبار الصور الاحتياطية
        print("\n📸 اختبار الصور الاحتياطية...")
        fallback_images = await ai_image_generator._get_fallback_gaming_images(2, visual_elements)
        
        if fallback_images:
            print(f"   ✅ تم الحصول على {len(fallback_images)} صورة احتياطية")
            for i, img in enumerate(fallback_images, 1):
                print(f"      {i}. {img['description'][:60]}...")
        else:
            print("   ❌ فشل في الحصول على صور احتياطية")
        
        # عرض الإحصائيات
        print("\n📊 الإحصائيات الحالية:")
        stats = ai_image_generator.get_generation_stats()
        for key, value in stats.items():
            print(f"   • {key}: {value}")
        
        print("\n🎉 الاختبار السريع مكتمل بنجاح!")
        print("💡 يمكنك الآن تشغيل الاختبار الشامل: python test_ai_image_generation.py")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 تأكد من تثبيت جميع المتطلبات: pip install -r requirements.txt")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(quick_test())
    
    if success:
        print("\n✅ النظام جاهز للاستخدام!")
    else:
        print("\n⚠️ يرجى مراجعة الأخطاء أعلاه")
