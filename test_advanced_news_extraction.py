# اختبار شامل لأنظمة البحث واستخراج الأخبار المتقدمة
import asyncio
import sys
import os
import json
from datetime import datetime

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.advanced_news_apis import advanced_news_apis
from modules.advanced_web_scraper import advanced_web_scraper
from modules.content_scraper import ContentScraper

async def test_advanced_news_systems():
    """اختبار شامل لأنظمة البحث واستخراج الأخبار المتقدمة"""
    
    print("🚀 بدء اختبار أنظمة البحث واستخراج الأخبار المتقدمة")
    print("=" * 70)
    
    try:
        # 1. اختبار APIs الأخبار المتقدمة
        print("\n📰 اختبار APIs الأخبار المتقدمة...")
        news_api_test = await advanced_news_apis.test_all_apis()
        
        print(f"📊 نتائج اختبار APIs الأخبار:")
        for api_name, result in news_api_test.items():
            if isinstance(result, dict) and 'available' in result:
                status = "✅ متوفر" if result['available'] else "❌ غير متوفر"
                articles_count = result.get('articles_found', 0)
                print(f"   • {api_name}: {status} ({articles_count} مقال)")
                if result.get('error'):
                    print(f"     خطأ: {result['error']}")
        
        print(f"   • الحالة العامة: {'✅ جاهز' if news_api_test['overall_status'] else '❌ غير جاهز'}")
        
        # 2. اختبار محركات البحث المتقدمة
        print("\n🔍 اختبار محركات البحث المتقدمة...")
        search_test = await advanced_web_scraper.test_all_search_engines()
        
        print(f"📊 نتائج اختبار محركات البحث:")
        for engine_name, result in search_test.items():
            if isinstance(result, dict) and 'available' in result:
                status = "✅ متوفر" if result['available'] else "❌ غير متوفر"
                results_count = result.get('results_found', 0)
                print(f"   • {engine_name}: {status} ({results_count} نتيجة)")
                if result.get('error'):
                    print(f"     خطأ: {result['error']}")
        
        print(f"   • الحالة العامة: {'✅ جاهز' if search_test['overall_status'] else '❌ غير جاهز'}")
        
        # 3. اختبار البحث الشامل عن أخبار الألعاب
        print("\n🎮 اختبار البحث الشامل عن أخبار الألعاب...")
        
        test_keywords = ['gaming news', 'video game updates', 'new game releases']
        
        for keyword in test_keywords:
            print(f"\n🔍 البحث عن: {keyword}")
            
            # البحث باستخدام APIs الأخبار
            news_articles = await advanced_news_apis.search_gaming_news_comprehensive(
                keywords=[keyword],
                max_articles=10,
                days_back=7
            )
            
            # البحث باستخدام محركات البحث
            web_articles = await advanced_web_scraper.comprehensive_gaming_search(
                query=keyword,
                max_results=10,
                include_direct_scraping=True
            )
            
            print(f"   📰 APIs الأخبار: {len(news_articles)} مقال")
            print(f"   🔍 محركات البحث: {len(web_articles)} مقال")
            
            # عرض أفضل 3 نتائج من كل مصدر
            if news_articles:
                print(f"   📰 أفضل نتائج APIs الأخبار:")
                for i, article in enumerate(news_articles[:3], 1):
                    print(f"      {i}. {article.get('title', '')[:60]}...")
                    print(f"         المصدر: {article.get('source', 'غير محدد')}")
                    print(f"         الجودة: {article.get('quality_score', 0):.1f}/10")
            
            if web_articles:
                print(f"   🔍 أفضل نتائج محركات البحث:")
                for i, article in enumerate(web_articles[:3], 1):
                    print(f"      {i}. {article.get('title', '')[:60]}...")
                    print(f"         المصدر: {article.get('source', 'غير محدد')}")
                    print(f"         الجودة: {article.get('quality_score', 0):.1f}/10")
        
        # 4. اختبار النظام المتكامل
        print("\n🚀 اختبار النظام المتكامل...")
        
        content_scraper = ContentScraper()
        integrated_results = await content_scraper.advanced_search_and_extract(
            'gaming news today',
            max_results=20
        )
        
        if integrated_results:
            print(f"✅ النظام المتكامل: تم العثور على {len(integrated_results)} مقال عالي الجودة")
            
            # تحليل مصادر النتائج
            sources = {}
            extraction_methods = {}
            
            for article in integrated_results:
                source = article.get('source', 'غير محدد')
                method = article.get('extraction_method', 'غير محدد')
                
                sources[source] = sources.get(source, 0) + 1
                extraction_methods[method] = extraction_methods.get(method, 0) + 1
            
            print(f"📊 توزيع المصادر:")
            for source, count in sorted(sources.items(), key=lambda x: x[1], reverse=True)[:5]:
                print(f"   • {source}: {count} مقال")
            
            print(f"📊 توزيع طرق الاستخراج:")
            for method, count in extraction_methods.items():
                print(f"   • {method}: {count} مقال")
            
            # عرض أفضل 5 مقالات
            print(f"🏆 أفضل 5 مقالات:")
            for i, article in enumerate(integrated_results[:5], 1):
                print(f"   {i}. {article.get('title', '')[:70]}...")
                print(f"      المصدر: {article.get('source', 'غير محدد')}")
                print(f"      الجودة: {article.get('quality_score', 0):.1f}/10")
                print(f"      الطريقة: {article.get('extraction_method', 'غير محدد')}")
        else:
            print("❌ النظام المتكامل: لم يتم العثور على مقالات")
        
        # 5. اختبار البحث عن المواضيع الرائجة
        print("\n🔥 اختبار البحث عن المواضيع الرائجة...")
        
        trending_topics = await advanced_news_apis.get_trending_gaming_topics(days_back=3)
        trending_web = await advanced_web_scraper.search_trending_gaming_topics(max_results=10)
        
        if trending_topics:
            print(f"🔥 المواضيع الرائجة (APIs): {len(trending_topics)} موضوع")
            for i, topic in enumerate(trending_topics[:5], 1):
                print(f"   {i}. {topic.get('topic', '')}: {topic.get('mention_count', 0)} ذكر")
        
        if trending_web:
            print(f"🔥 المواضيع الرائجة (Web): {len(trending_web)} مقال")
            for i, article in enumerate(trending_web[:3], 1):
                print(f"   {i}. {article.get('title', '')[:60]}...")
        
        # 6. عرض الإحصائيات الشاملة
        print("\n📈 الإحصائيات الشاملة:")
        
        news_stats = advanced_news_apis.get_usage_stats()
        web_stats = advanced_web_scraper.get_extraction_stats()
        integrated_stats = content_scraper.get_advanced_extraction_stats()
        
        print(f"📰 إحصائيات APIs الأخبار:")
        print(f"   • إجمالي المقالات: {news_stats.get('total_articles_found', 0)}")
        print(f"   • معدل النجاح: {news_stats.get('success_rate', 0):.1f}%")
        print(f"   • إجمالي الطلبات: {news_stats.get('total_api_calls', 0)}")
        
        print(f"🔍 إحصائيات محركات البحث:")
        print(f"   • إجمالي المقالات: {web_stats.get('total_articles_extracted', 0)}")
        print(f"   • معدل النجاح: {web_stats.get('success_rate', 0):.1f}%")
        print(f"   • إجمالي الطلبات: {web_stats.get('total_calls', 0)}")
        
        # 7. حفظ تقرير الاختبار
        test_report = {
            'timestamp': datetime.now().isoformat(),
            'news_apis_test': news_api_test,
            'search_engines_test': search_test,
            'integrated_results_count': len(integrated_results) if integrated_results else 0,
            'trending_topics_count': len(trending_topics) if trending_topics else 0,
            'statistics': {
                'news_apis': news_stats,
                'web_scraper': web_stats,
                'integrated': integrated_stats
            },
            'test_status': 'success'
        }
        
        with open('test_advanced_news_extraction_report.json', 'w', encoding='utf-8') as f:
            json.dump(test_report, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 تم حفظ تقرير الاختبار في: test_advanced_news_extraction_report.json")
        
        # 8. النتيجة النهائية
        overall_success = (
            news_api_test['overall_status'] or 
            search_test['overall_status'] or 
            (integrated_results and len(integrated_results) > 0)
        )
        
        if overall_success:
            print("\n🎉 نجح الاختبار! أنظمة البحث واستخراج الأخبار المتقدمة تعمل بشكل ممتاز")
            print("💡 يمكنك الآن الاستفادة من:")
            print("   • APIs الأخبار المتعددة للحصول على أحدث الأخبار")
            print("   • محركات البحث المتقدمة للعثور على محتوى متنوع")
            print("   • النظام المتكامل للبحث الشامل والذكي")
            print("   • تحليل المواضيع الرائجة تلقائياً")
            return True
        else:
            print("\n⚠️ الاختبار مكتمل مع بعض المشاكل - راجع التفاصيل أعلاه")
            return False
            
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار الأنظمة المتقدمة: {e}")
        print(f"\n❌ فشل الاختبار: {e}")
        return False

async def test_specific_game_search():
    """اختبار البحث عن ألعاب محددة"""
    
    print("\n🎯 اختبار البحث عن ألعاب محددة...")
    
    specific_games = ['Cyberpunk 2077', 'Minecraft', 'Fortnite']
    
    for game in specific_games:
        print(f"\n🎮 البحث عن أخبار: {game}")
        
        # البحث باستخدام APIs الأخبار
        game_news = await advanced_news_apis.search_specific_game_news(game, max_articles=5)
        
        # البحث باستخدام محركات البحث
        game_web = await advanced_web_scraper.comprehensive_gaming_search(
            query=f'{game} news',
            max_results=5
        )
        
        print(f"   📰 APIs الأخبار: {len(game_news)} مقال")
        print(f"   🔍 محركات البحث: {len(game_web)} مقال")
        
        if game_news:
            best_article = max(game_news, key=lambda x: x.get('quality_score', 0))
            print(f"   🏆 أفضل مقال: {best_article.get('title', '')[:50]}...")

if __name__ == "__main__":
    print("🚀 بدء اختبار أنظمة البحث واستخراج الأخبار المتقدمة")
    
    # تشغيل الاختبار الأساسي
    success = asyncio.run(test_advanced_news_systems())
    
    if success:
        # تشغيل اختبارات إضافية
        asyncio.run(test_specific_game_search())
        
        print("\n🎉 جميع الاختبارات مكتملة!")
        print("💡 يمكنك الآن استخدام الأنظمة المتقدمة في main.py")
        print("🚀 الوكيل الآن قادر على:")
        print("   • البحث في APIs أخبار متعددة")
        print("   • استخراج المحتوى من محركات بحث متقدمة")
        print("   • الاستخراج المباشر من مواقع الألعاب المتخصصة")
        print("   • تحليل المواضيع الرائجة تلقائياً")
        print("   • تصفية وترتيب النتائج بذكاء")
    else:
        print("\n⚠️ يرجى مراجعة الأخطاء وإعادة المحاولة")
        print("💡 تأكد من:")
        print("   • إعداد مفاتيح APIs في ملف .env")
        print("   • الاتصال بالإنترنت")
        print("   • تثبيت جميع المتطلبات")
