# أداة إعداد وكيل أخبار ماين كرافت
import os
import sys
import json
import webbrowser
from typing import Dict, Optional
import asyncio
from modules.logger import logger
from modules.publisher import BloggerPublisher, TelegramPublisher

class BotSetup:
    """مساعد إعداد البوت"""
    
    def __init__(self):
        self.config_file = "config/bot_config.json"
        self.env_file = ".env"
        self.setup_complete = False
    
    def start_setup(self):
        """بدء عملية الإعداد"""
        print("🚀 مرحباً بك في إعداد وكيل أخبار ماين كرافت!")
        print("=" * 60)
        
        try:
            # إنشاء المجلدات المطلوبة
            self._create_directories()
            
            # جمع المعلومات المطلوبة
            config = self._collect_configuration()
            
            # حفظ التكوين
            self._save_configuration(config)
            
            # اختبار الإعدادات
            if self._test_configuration(config):
                print("\n✅ تم إعداد البوت بنجاح!")
                print("يمكنك الآن تشغيل البوت باستخدام: python main.py")
                self.setup_complete = True
            else:
                print("\n❌ فشل في اختبار الإعدادات، يرجى المراجعة")
                
        except KeyboardInterrupt:
            print("\n⚠️ تم إلغاء الإعداد")
        except Exception as e:
            print(f"\n❌ خطأ في الإعداد: {e}")
    
    def _create_directories(self):
        """إنشاء المجلدات المطلوبة"""
        directories = ["config", "data", "logs", "images"]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            print(f"📁 تم إنشاء مجلد: {directory}")
    
    def _collect_configuration(self) -> Dict:
        """إرجاع التكوين الثابت"""
        print("\n📝 جاري تحميل التكوين...")
        
        config = {
            'GEMINI_API_KEY': "AIzaSyBiMajBlgp6rC6tTvkAUa6s34EG4VeYZxk",
            'TELEGRAM_BOT_TOKEN': "**********************************************",
            'TELEGRAM_CHANNEL_ID': "@Football_news136",
            'BLOGGER_CLIENT_SECRET_FILE': "client_secret.json",
            'BLOGGER_BLOG_ID': "3839855275391297132",
            'YOUTUBE_API_KEY': "",  # يمكن إضافته لاحقاً
            'SEARCH_INTERVAL_HOURS': "2",

            # مفاتيح APIs إنشاء الصور بالذكاء الاصطناعي - ميزة جديدة قوية!
            'FREEPIK_API_KEY': "FPSX1ee910637a8ec349e6d8c7f17a57740b",  # المفتاح الجديد المحدث
            'FLUXAI_API_KEY': "b6863038ac459a1f8cd9e30d82cdd989",
            'LEONARDO_AI_API_KEY': "",  # اختياري
            'MIDJOURNEY_API_KEY': "",   # اختياري

            # مفاتيح APIs الصور التقليدية
            'PEXELS_API_KEY': "",       # اختياري
            'PIXABAY_API_KEY': "",      # اختياري
            'UNSPLASH_ACCESS_KEY': "",  # اختياري

            # مفاتيح APIs الأخبار المتقدمة - نظام جديد قوي!
            'NEWSAPI_KEY': "",          # NewsAPI.org (اختياري)
            'NEWSDATA_KEY': "pub_6a04788f4edc429a8fb798dc3af6a6fb",  # NewsData.io
            'THENEWSAPI_KEY': "",       # TheNewsAPI.com (اختياري)
            'GNEWS_KEY': "",            # GNews.io (اختياري)

            # مفاتيح APIs البحث المتقدم
            'BRAVE_SEARCH_KEY': "",     # Brave Search API (اختياري)
            'GOOGLE_SEARCH_KEY': "AIzaSyC3ihvVEvFSgaw_MAjcJLBZQno_8L1F-Tk"  # Google Custom Search الجديد
        }
        
        print("✅ تم تحميل التكوين بنجاح.")
        return config
    
    def _get_user_input(self, prompt: str, default: str = None, required: bool = False, help_text: str = None) -> str:
        """الحصول على إدخال من المستخدم"""
        if help_text:
            print(f"💡 {help_text}")
        
        while True:
            if default:
                user_input = input(f"{prompt} [{default}]: ").strip()
                if not user_input:
                    user_input = default
            else:
                user_input = input(f"{prompt}: ").strip()
            
            if user_input or not required:
                return user_input
            
            print("❌ هذا الحقل مطلوب، يرجى الإدخال")
    
    def _save_configuration(self, config: Dict):
        """حفظ التكوين في الملفات"""
        try:
            # حفظ في ملف JSON
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            # حفظ في ملف .env
            env_content = []
            for key, value in config.items():
                if value:
                    env_content.append(f"{key}={value}")
            
            with open(self.env_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(env_content))
            
            print(f"💾 تم حفظ التكوين في: {self.config_file} و {self.env_file}")
            
        except Exception as e:
            print(f"❌ فشل في حفظ التكوين: {e}")
            raise
    
    def _test_configuration(self, config: Dict) -> bool:
        """اختبار التكوين المحفوظ"""
        print("\n🔍 اختبار الإعدادات...")
        
        success = True
        
        # اختبار تيليجرام
        try:
            print("📱 اختبار تيليجرام...")
            telegram_publisher = TelegramPublisher(
                config['TELEGRAM_BOT_TOKEN'],
                config['TELEGRAM_CHANNEL_ID']
            )
            
            # هذا الاختبار يتطلب async
            async def test_telegram():
                return await telegram_publisher.test_connection()
            
            telegram_success = asyncio.run(test_telegram())
            
            if telegram_success:
                print("✅ تيليجرام يعمل بنجاح")
            else:
                print("❌ فشل اختبار تيليجرام")
                success = False
                
        except Exception as e:
            print(f"❌ خطأ في اختبار تيليجرام: {e}")
            success = False
        
        # اختبار بلوجر (يتطلب مصادقة)
        try:
            print("📝 إعداد بلوجر...")
            blogger_publisher = BloggerPublisher(
                client_secret_file=config['BLOGGER_CLIENT_SECRET_FILE'],
                blog_id=config['BLOGGER_BLOG_ID']
            )
            
            # بدء عملية المصادقة التلقائية
            print("🔐 يرجى اتباع التعليمات في المتصفح الذي سيتم فتحه الآن للمصادقة مع Blogger...")
            blogger_publisher.run_local_server_authentication()
            
            # اختبار الاتصال
            if blogger_publisher.test_connection():
                print("✅ بلوجر يعمل بنجاح")
            else:
                print("❌ فشل اختبار بلوجر")
                success = False
                
        except Exception as e:
            print(f"❌ خطأ في إعداد بلوجر: {e}")
            print("يمكنك إعداد بلوجر لاحقاً باستخدام الإعدادات اليدوية")
            # لا نعتبر هذا فشلاً كاملاً
        
        return success
    
    def load_existing_config(self) -> Optional[Dict]:
        """تحميل التكوين الموجود"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"⚠️ فشل في تحميل التكوين الموجود: {e}")
        
        return None
    
    def show_status(self):
        """عرض حالة الإعداد"""
        config = self.load_existing_config()
        
        if not config:
            print("❌ لم يتم العثور على تكوين محفوظ")
            print("يرجى تشغيل الإعداد أولاً: python setup_bot.py")
            return
        
        print("📊 حالة إعدادات البوت:")
        print("-" * 30)
        
        required_keys = [
            'GEMINI_API_KEY',
            'TELEGRAM_BOT_TOKEN',
            'TELEGRAM_CHANNEL_ID',
            'BLOGGER_CLIENT_ID',
            'BLOGGER_CLIENT_SECRET',
            'BLOGGER_BLOG_ID'
        ]
        
        for key in required_keys:
            status = "✅" if config.get(key) else "❌"
            print(f"{status} {key}")
        
        optional_keys = ['YOUTUBE_API_KEY', 'SEARCH_INTERVAL_HOURS']
        
        print("\nالإعدادات الاختيارية:")
        for key in optional_keys:
            status = "✅" if config.get(key) else "⚪"
            print(f"{status} {key}")

def main():
    """الدالة الرئيسية لإعداد البوت"""
    setup = BotSetup()
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "status":
            setup.show_status()
        elif sys.argv[1] == "help":
            print("""
🔧 أداة إعداد وكيل أخبار ماين كرافت

الاستخدام:
  python setup_bot.py          - بدء الإعداد التفاعلي
  python setup_bot.py status   - عرض حالة الإعدادات
  python setup_bot.py help     - عرض هذه المساعدة

المتطلبات:
  - مفتاح Gemini API من Google AI Studio
  - توكن بوت تيليجرام من BotFather
  - بيانات Google Cloud Console لبلوجر
            """)
        else:
            print(f"❌ أمر غير معروف: {sys.argv[1]}")
            print("استخدم: python setup_bot.py help للمساعدة")
    else:
        setup.start_setup()

if __name__ == "__main__":
    main()
