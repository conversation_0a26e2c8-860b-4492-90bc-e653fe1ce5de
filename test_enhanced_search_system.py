#!/usr/bin/env python3
# اختبار نظام البحث المحسن
import asyncio
import time
import json
from datetime import datetime
import sys
import os

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.enhanced_search_integration import enhanced_search
from modules.smart_search_manager import smart_search_manager, SearchRequest, SearchPriority
from modules.rate_limit_manager import rate_limit_manager
from modules.search_analytics import search_analytics
from modules.advanced_cache_system import advanced_cache
from modules.logger import logger

class EnhancedSearchTester:
    """اختبار نظام البحث المحسن"""
    
    def __init__(self):
        self.test_queries = [
            "gaming news today",
            "new video game releases 2025",
            "latest gaming updates",
            "breaking gaming news",
            "indie game announcements",
            "AAA games 2025",
            "gaming industry news",
            "esports updates",
            "mobile gaming news",
            "PC gaming updates"
        ]
        
        self.test_results = {
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'test_details': []
        }
    
    async def run_comprehensive_test(self):
        """تشغيل اختبار شامل"""
        print("🧪 بدء اختبار نظام البحث المحسن الشامل")
        print("=" * 60)
        
        try:
            # 1. اختبار المكونات الأساسية
            await self.test_basic_components()
            
            # 2. اختبار البحث المحسن
            await self.test_enhanced_search()
            
            # 3. اختبار التخزين المؤقت
            await self.test_caching_system()
            
            # 4. اختبار إدارة معدل الطلبات
            await self.test_rate_limiting()
            
            # 5. اختبار التحليلات
            await self.test_analytics()
            
            # 6. اختبار الأداء
            await self.test_performance()
            
            # 7. عرض النتائج النهائية
            self.display_final_results()
            
        except Exception as e:
            logger.error(f"❌ فشل في الاختبار الشامل: {e}")
            print(f"❌ خطأ في الاختبار: {e}")
    
    async def test_basic_components(self):
        """اختبار المكونات الأساسية"""
        print("\n🔧 اختبار المكونات الأساسية...")
        
        # اختبار التخزين المؤقت
        test_passed = await self._test_component(
            "Advanced Cache System",
            self._test_cache_basic_operations
        )
        
        # اختبار مدير معدل الطلبات
        test_passed = await self._test_component(
            "Rate Limit Manager",
            self._test_rate_limit_basic
        )
        
        # اختبار مدير البحث الذكي
        test_passed = await self._test_component(
            "Smart Search Manager",
            self._test_smart_search_basic
        )
    
    async def test_enhanced_search(self):
        """اختبار البحث المحسن"""
        print("\n🔍 اختبار البحث المحسن...")
        
        for i, query in enumerate(self.test_queries[:5]):  # اختبار أول 5 استعلامات
            test_name = f"Enhanced Search - Query {i+1}"
            
            async def test_search():
                results = await enhanced_search.enhanced_search(
                    query=query,
                    max_results=5,
                    priority="auto"
                )
                
                if not isinstance(results, list):
                    raise Exception("النتائج ليست قائمة")
                
                print(f"  ✅ '{query}': {len(results)} نتيجة")
                return True
            
            await self._test_component(test_name, test_search)
            
            # تأخير بين الاختبارات
            await asyncio.sleep(2)
    
    async def test_caching_system(self):
        """اختبار نظام التخزين المؤقت"""
        print("\n💾 اختبار نظام التخزين المؤقت...")
        
        async def test_cache_performance():
            test_data = {"test": "data", "timestamp": time.time()}
            
            # اختبار الحفظ
            success = advanced_cache.set("test_key", test_data, ttl=60)
            if not success:
                raise Exception("فشل في حفظ البيانات")
            
            # اختبار الاسترجاع
            retrieved_data = advanced_cache.get("test_key")
            if retrieved_data != test_data:
                raise Exception("البيانات المسترجعة لا تطابق البيانات المحفوظة")
            
            # اختبار الحذف
            deleted = advanced_cache.delete("test_key")
            if not deleted:
                raise Exception("فشل في حذف البيانات")
            
            return True
        
        await self._test_component("Cache Performance", test_cache_performance)
        
        # اختبار إحصائيات التخزين المؤقت
        async def test_cache_stats():
            stats = advanced_cache.get_stats()
            required_keys = ['hits', 'misses', 'hit_rate', 'total_entries']
            
            for key in required_keys:
                if key not in stats:
                    raise Exception(f"مفتاح مفقود في الإحصائيات: {key}")
            
            print(f"  📊 إحصائيات التخزين المؤقت: {stats}")
            return True
        
        await self._test_component("Cache Statistics", test_cache_stats)
    
    async def test_rate_limiting(self):
        """اختبار إدارة معدل الطلبات"""
        print("\n⏱️ اختبار إدارة معدل الطلبات...")
        
        async def test_rate_limit_check():
            # اختبار فحص الحدود
            can_request, message, wait_time = await rate_limit_manager.can_make_request("test_service")
            
            if not isinstance(can_request, bool):
                raise Exception("نتيجة فحص الحدود ليست boolean")
            
            if not isinstance(message, str):
                raise Exception("رسالة الحدود ليست string")
            
            if not isinstance(wait_time, (int, float)):
                raise Exception("وقت الانتظار ليس رقم")
            
            print(f"  ✅ فحص الحدود: {can_request}, رسالة: {message}")
            return True
        
        await self._test_component("Rate Limit Check", test_rate_limit_check)
        
        async def test_request_recording():
            # اختبار تسجيل الطلبات
            await rate_limit_manager.record_request(
                service="test_service",
                success=True,
                response_time=1.5,
                cost=0.01
            )
            
            # الحصول على الإحصائيات
            stats = rate_limit_manager.get_service_stats("test_service")
            
            if not isinstance(stats, dict):
                raise Exception("إحصائيات الخدمة ليست dictionary")
            
            print(f"  📊 إحصائيات الخدمة: {stats}")
            return True
        
        await self._test_component("Request Recording", test_request_recording)
    
    async def test_analytics(self):
        """اختبار نظام التحليلات"""
        print("\n📊 اختبار نظام التحليلات...")
        
        async def test_analytics_recording():
            from modules.search_analytics import SearchAnalytics
            
            # إنشاء تحليلات اختبار
            analytics = SearchAnalytics(
                query="test query",
                timestamp=time.time(),
                search_engine="test_engine",
                results_count=5,
                response_time=2.0,
                cache_hit=False,
                quality_score=75.0,
                cost=0.02,
                success=True
            )
            
            # تسجيل التحليلات
            search_analytics.record_search(analytics)
            
            # الحصول على مقاييس الأداء
            metrics = search_analytics.get_performance_metrics(1)  # آخر ساعة
            
            if not hasattr(metrics, 'avg_response_time'):
                raise Exception("مقاييس الأداء لا تحتوي على avg_response_time")
            
            print(f"  📈 مقاييس الأداء: متوسط الوقت {metrics.avg_response_time}s")
            return True
        
        await self._test_component("Analytics Recording", test_analytics_recording)
    
    async def test_performance(self):
        """اختبار الأداء"""
        print("\n⚡ اختبار الأداء...")
        
        async def test_search_speed():
            start_time = time.time()
            
            # تشغيل عدة عمليات بحث متوازية
            tasks = []
            for query in self.test_queries[:3]:
                task = enhanced_search.enhanced_search(query, max_results=3, priority="free")
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            end_time = time.time()
            total_time = end_time - start_time
            
            successful_searches = sum(1 for r in results if isinstance(r, list))
            
            print(f"  ⚡ {successful_searches} عمليات بحث في {total_time:.2f} ثانية")
            print(f"  📊 متوسط الوقت لكل بحث: {total_time/len(tasks):.2f} ثانية")
            
            if total_time > 30:  # أكثر من 30 ثانية يعتبر بطيء
                raise Exception(f"الأداء بطيء جداً: {total_time:.2f} ثانية")
            
            return True
        
        await self._test_component("Search Speed", test_search_speed)
    
    async def _test_component(self, component_name: str, test_function):
        """اختبار مكون واحد"""
        self.test_results['total_tests'] += 1
        
        try:
            start_time = time.time()
            result = await test_function()
            end_time = time.time()
            
            if result:
                self.test_results['passed_tests'] += 1
                status = "✅ نجح"
                print(f"  {status} {component_name} ({end_time - start_time:.2f}s)")
            else:
                self.test_results['failed_tests'] += 1
                status = "❌ فشل"
                print(f"  {status} {component_name}")
            
            self.test_results['test_details'].append({
                'component': component_name,
                'status': 'passed' if result else 'failed',
                'duration': end_time - start_time
            })
            
            return result
            
        except Exception as e:
            self.test_results['failed_tests'] += 1
            print(f"  ❌ فشل {component_name}: {e}")
            
            self.test_results['test_details'].append({
                'component': component_name,
                'status': 'failed',
                'error': str(e),
                'duration': 0
            })
            
            return False
    
    async def _test_cache_basic_operations(self):
        """اختبار العمليات الأساسية للتخزين المؤقت"""
        # اختبار الحفظ والاسترجاع
        test_data = {"message": "Hello Cache", "number": 42}
        
        success = advanced_cache.set("basic_test", test_data)
        if not success:
            return False
        
        retrieved = advanced_cache.get("basic_test")
        if retrieved != test_data:
            return False
        
        # اختبار الحذف
        deleted = advanced_cache.delete("basic_test")
        if not deleted:
            return False
        
        # التأكد من الحذف
        retrieved_after_delete = advanced_cache.get("basic_test")
        if retrieved_after_delete is not None:
            return False
        
        return True
    
    async def _test_rate_limit_basic(self):
        """اختبار العمليات الأساسية لمعدل الطلبات"""
        # اختبار فحص الحدود
        can_request, message, wait_time = await rate_limit_manager.can_make_request("basic_test")
        
        if not isinstance(can_request, bool):
            return False
        
        # اختبار تسجيل طلب
        await rate_limit_manager.record_request("basic_test", True, 1.0)
        
        return True
    
    async def _test_smart_search_basic(self):
        """اختبار العمليات الأساسية للبحث الذكي"""
        # إنشاء طلب بحث بسيط
        request = SearchRequest(
            query="test query",
            max_results=1,
            priority=SearchPriority.FREE
        )
        
        # محاولة البحث (قد يفشل بسبب عدم توفر APIs، لكن يجب ألا يحدث خطأ)
        try:
            results = await smart_search_manager.search(request)
            # النجاح إذا لم يحدث خطأ، حتى لو كانت النتائج فارغة
            return True
        except Exception as e:
            # فشل فقط إذا كان خطأ في الكود، وليس في APIs
            if "API" in str(e) or "network" in str(e).lower():
                return True  # خطأ متوقع في APIs
            return False  # خطأ في الكود
    
    def display_final_results(self):
        """عرض النتائج النهائية"""
        print("\n" + "=" * 60)
        print("📋 نتائج الاختبار النهائية")
        print("=" * 60)
        
        total = self.test_results['total_tests']
        passed = self.test_results['passed_tests']
        failed = self.test_results['failed_tests']
        
        success_rate = (passed / total * 100) if total > 0 else 0
        
        print(f"📊 إجمالي الاختبارات: {total}")
        print(f"✅ نجح: {passed}")
        print(f"❌ فشل: {failed}")
        print(f"📈 معدل النجاح: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("\n🎉 النظام يعمل بشكل ممتاز!")
        elif success_rate >= 60:
            print("\n⚠️ النظام يعمل بشكل جيد مع بعض المشاكل")
        else:
            print("\n❌ النظام يحتاج إلى إصلاحات")
        
        # عرض تفاصيل الاختبارات الفاشلة
        failed_tests = [t for t in self.test_results['test_details'] if t['status'] == 'failed']
        if failed_tests:
            print("\n❌ الاختبارات الفاشلة:")
            for test in failed_tests:
                print(f"  - {test['component']}: {test.get('error', 'خطأ غير محدد')}")
        
        # حفظ النتائج في ملف
        self.save_results_to_file()
    
    def save_results_to_file(self):
        """حفظ النتائج في ملف"""
        try:
            results_file = f"test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 تم حفظ النتائج في: {results_file}")
            
        except Exception as e:
            print(f"❌ فشل في حفظ النتائج: {e}")

async def main():
    """الدالة الرئيسية"""
    tester = EnhancedSearchTester()
    await tester.run_comprehensive_test()

if __name__ == "__main__":
    asyncio.run(main())
