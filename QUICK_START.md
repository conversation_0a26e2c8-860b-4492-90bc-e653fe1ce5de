# 🚀 دليل التشغيل السريع - وكيل أخبار ماين كرافت

## التشغيل في 5 خطوات

### 1. 📋 المتطلبات الأساسية
تأكد من وجود:
- Python 3.9+ 
- مفتاح Gemini API من [Google AI Studio](https://makersuite.google.com/app/apikey)
- توكن تيليجرام من [@BotFather](https://t.me/BotFather)
- إعدادات Google Cloud Console للبوت

### 2. ⬇️ التحضير
```bash
cd minecraft_news_bot
pip install -r requirements.txt
```

### 3. ⚙️ الإعداد السريع
```bash
python setup_bot.py
```
اتبع الإرشادات لإدخال:
- مفتاح Gemini API
- توكن تيليجرام البوت
- معرف قناة تيليجرام
- بيانات Google Cloud (Client ID, Secret, Blog ID)

### 4. 🧪 الاختبار
```bash
python setup_bot.py status
```

### 5. 🚀 التشغيل
```bash
python main.py
```
أو
```bash
python run.py
```

## ⚡ التشغيل السريع (طريقة واحدة)
```bash
python run.py
```
هذا الأمر سيقوم بكل شيء تلقائياً!

## 📊 مراقبة البوت

### عرض السجلات المباشرة
```bash
tail -f logs/bot.log
```

### فحص الحالة
```bash
python setup_bot.py status
```

### إحصائيات سريعة
البوت سيعرض إحصائيات دورية في السجلات تشمل:
- عدد المقالات المعالجة
- معدل النجاح
- أخطاء النظام
- وقت التشغيل

## 🔧 استكشاف الأخطاء السريع

### مشكلة: خطأ في Gemini API
**الحل**: تأكد من صحة المفتاح ووجود رصيد

### مشكلة: فشل النشر على تيليجرام  
**الحل**: تأكد من صحة التوكن ومعرف القناة

### مشكلة: خطأ في بلوجر
**الحل**: أعد عملية المصادقة من الإعدادات

### مشكلة: لا يجمع محتوى
**الحل**: تحقق من الاتصال بالإنترنت والمصادر

## 🛑 إيقاف البوت
اضغط `Ctrl+C` في الطرفية

## 📁 الملفات المهمة
- `main.py` - الملف الرئيسي
- `logs/bot.log` - سجل العمليات
- `data/articles.db` - قاعدة البيانات
- `config/bot_config.json` - ملف التكوين

## 🆘 الدعم السريع
راجع `README.md` للتفاصيل الكاملة أو تحقق من السجلات للأخطاء.

---
💡 **نصيحة**: البوت مصمم للعمل 24/7، تأكد من استقرار الخادم والإنترنت.
