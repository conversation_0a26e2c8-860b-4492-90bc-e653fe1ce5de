#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام إدارة الصور الذكي
"""

import asyncio
import sys
import os
import json
from datetime import datetime

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.smart_image_manager import SmartImageManager, ImageGenerationPolicy

async def test_smart_image_generation():
    """اختبار إنشاء الصور الذكي"""
    print("🧪 اختبار نظام إدارة الصور الذكي...")
    print("=" * 60)
    
    # إنشاء سياسة مخصصة للاختبار
    test_policy = ImageGenerationPolicy(
        max_images_per_article=1,
        max_daily_generations=10,  # حد منخفض للاختبار
        min_article_quality_score=5.0,
        cache_duration_hours=1,
        reuse_similar_images=True
    )
    
    # إنشاء مدير الصور الذكي
    smart_manager = SmartImageManager(test_policy)
    
    print(f"📋 سياسة الإنشاء:")
    print(f"   حد أقصى صور/مقال: {test_policy.max_images_per_article}")
    print(f"   حد أقصى صور/يوم: {test_policy.max_daily_generations}")
    print(f"   حد أدنى جودة المقال: {test_policy.min_article_quality_score}/10")
    print(f"   إعادة استخدام الصور: {'✅' if test_policy.reuse_similar_images else '❌'}")
    
    # مقالات اختبار بجودات مختلفة
    test_articles = [
        {
            'title': 'Breaking: New PlayStation 5 Exclusive Game Announced',
            'content': 'Sony has just announced an exciting new exclusive game for PlayStation 5. This highly anticipated title promises to deliver stunning graphics and innovative gameplay mechanics that will showcase the full power of the PS5 hardware. The game features an immersive storyline, cutting-edge ray tracing technology, and haptic feedback integration.',
            'keywords': ['PlayStation 5', 'exclusive game', 'Sony', 'gaming', 'announcement'],
            'quality_expected': 'high'
        },
        {
            'title': 'Xbox Game Pass Update',
            'content': 'Xbox Game Pass receives new games this month.',
            'keywords': ['Xbox', 'Game Pass'],
            'quality_expected': 'low'
        },
        {
            'title': 'Cyberpunk 2077 Major Update Released',
            'content': 'CD Projekt RED has released a major update for Cyberpunk 2077, addressing numerous bugs and adding new features. The update includes performance improvements, new quests, and enhanced graphics options. Players can now enjoy a more stable and immersive experience in Night City.',
            'keywords': ['Cyberpunk 2077', 'update', 'CD Projekt RED', 'gaming', 'patch'],
            'quality_expected': 'high'
        },
        {
            'title': 'Similar PlayStation 5 Game News',  # مشابه للأول
            'content': 'Another PlayStation 5 exclusive game has been announced by Sony. This new title will feature advanced graphics and gameplay.',
            'keywords': ['PlayStation 5', 'exclusive', 'Sony', 'gaming'],
            'quality_expected': 'medium'
        },
        {
            'title': 'Nintendo Switch OLED Review',
            'content': 'The Nintendo Switch OLED model offers an enhanced gaming experience with its vibrant OLED display, improved audio, and sleek design. Our comprehensive review covers performance, battery life, and game compatibility.',
            'keywords': ['Nintendo Switch', 'OLED', 'review', 'gaming', 'handheld'],
            'quality_expected': 'high'
        }
    ]
    
    results = []
    
    print(f"\n🎯 اختبار إنشاء الصور لـ {len(test_articles)} مقال...")
    
    for i, article in enumerate(test_articles, 1):
        print(f"\n📰 المقال {i}: {article['title'][:50]}...")
        print(f"   الجودة المتوقعة: {article['quality_expected']}")
        
        try:
            # حساب جودة المقال
            quality_score = smart_manager._calculate_article_quality_score(article)
            print(f"   نقاط الجودة الفعلية: {quality_score:.1f}/10")
            
            # فحص ما إذا كان يجب إنشاء صورة
            should_generate, reason = smart_manager._should_generate_image(article)
            print(f"   قرار الإنشاء: {'✅ نعم' if should_generate else '❌ لا'} - {reason}")
            
            # محاولة إنشاء صورة
            image_result = await smart_manager.generate_smart_image_for_article(article)
            
            if image_result:
                print(f"   ✅ تم إنشاء صورة: {image_result.get('source', 'Unknown')}")
                print(f"      الرابط: {image_result.get('url', 'N/A')[:60]}...")
                print(f"      طريقة الإنشاء: {image_result.get('generation_method', 'N/A')}")
                results.append({
                    'article': article['title'],
                    'success': True,
                    'method': image_result.get('generation_method', 'unknown'),
                    'api_used': image_result.get('api_used', 'unknown')
                })
            else:
                print(f"   ❌ لم يتم إنشاء صورة")
                results.append({
                    'article': article['title'],
                    'success': False,
                    'reason': reason
                })
            
            # تأخير قصير بين المقالات
            await asyncio.sleep(1)
            
        except Exception as e:
            print(f"   ❌ خطأ: {e}")
            results.append({
                'article': article['title'],
                'success': False,
                'error': str(e)
            })
    
    return results, smart_manager

async def test_caching_system(smart_manager):
    """اختبار نظام التخزين المؤقت"""
    print(f"\n🗄️ اختبار نظام التخزين المؤقت...")
    
    # مقال مشابه للاختبار
    similar_article = {
        'title': 'PlayStation 5 Exclusive Game Update',
        'content': 'Sony announces updates for PlayStation 5 exclusive games.',
        'keywords': ['PlayStation 5', 'exclusive', 'Sony', 'gaming']
    }
    
    print(f"📰 اختبار مقال مشابه: {similar_article['title']}")
    
    # محاولة إنشاء صورة (يجب أن يستخدم التخزين المؤقت)
    cached_image = await smart_manager.generate_smart_image_for_article(similar_article)
    
    if cached_image:
        if cached_image.get('generation_method') == 'smart_managed':
            print("   ✅ تم إنشاء صورة جديدة")
        else:
            print("   ♻️ تم استخدام صورة من التخزين المؤقت")
    else:
        print("   ❌ لم يتم العثور على صورة")

def test_daily_stats(smart_manager):
    """اختبار إحصائيات اليوم"""
    print(f"\n📊 اختبار إحصائيات اليوم...")
    
    stats = smart_manager.get_daily_stats()
    
    print(f"📈 إحصائيات اليوم ({stats['date']}):")
    print(f"   الصور المُنشأة: {stats['images_generated']}/{stats['policy']['max_daily_generations']}")
    print(f"   المقالات المُعالجة: {stats['articles_processed']}")
    print(f"   استدعاءات API:")
    for api, calls in stats['api_calls'].items():
        if calls > 0:
            print(f"      {api}: {calls}")
    print(f"   معدل نجاح التخزين المؤقت: {stats['cache_hit_rate']:.1f}%")
    print(f"   الحصة المتبقية: {stats['remaining_quota']} صورة")
    
    return stats

def test_quota_management(smart_manager):
    """اختبار إدارة الحصة"""
    print(f"\n🚫 اختبار إدارة الحصة...")
    
    # محاولة تجاوز الحد اليومي
    original_quota = smart_manager.daily_stats['images_generated']
    smart_manager.daily_stats['images_generated'] = smart_manager.policy.max_daily_generations
    
    test_article = {
        'title': 'Test Article for Quota',
        'content': 'This is a test article to check quota management.',
        'keywords': ['test', 'gaming']
    }
    
    should_generate, reason = smart_manager._should_generate_image(test_article)
    
    if not should_generate and "حد اليومي" in reason:
        print("   ✅ إدارة الحصة تعمل بشكل صحيح")
        print(f"   السبب: {reason}")
    else:
        print("   ❌ إدارة الحصة لا تعمل بشكل صحيح")
    
    # إعادة تعيين الحصة
    smart_manager.daily_stats['images_generated'] = original_quota

async def test_quality_filtering(smart_manager):
    """اختبار فلترة الجودة"""
    print(f"\n🎯 اختبار فلترة الجودة...")
    
    # مقالات بجودات مختلفة
    quality_test_articles = [
        {
            'title': 'A',  # جودة منخفضة جداً
            'content': 'Short.',
            'keywords': [],
            'expected': False
        },
        {
            'title': 'High Quality Gaming Article with Detailed Content',
            'content': 'This is a comprehensive gaming article with detailed analysis, multiple paragraphs, and extensive coverage of the topic. It includes various aspects of gaming, reviews, and insights that provide value to readers.',
            'keywords': ['gaming', 'review', 'analysis', 'detailed'],
            'expected': True
        }
    ]
    
    for article in quality_test_articles:
        quality_score = smart_manager._calculate_article_quality_score(article)
        should_generate, reason = smart_manager._should_generate_image(article)
        
        print(f"📰 '{article['title'][:30]}...'")
        print(f"   نقاط الجودة: {quality_score:.1f}/10")
        print(f"   متوقع: {'✅' if article['expected'] else '❌'}")
        print(f"   فعلي: {'✅' if should_generate else '❌'}")
        print(f"   السبب: {reason}")
        
        if (should_generate == article['expected']):
            print("   ✅ فلترة الجودة تعمل بشكل صحيح")
        else:
            print("   ❌ فلترة الجودة تحتاج تحسين")

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار نظام إدارة الصور الذكي...")
    
    try:
        # اختبار إنشاء الصور الذكي
        results, smart_manager = await test_smart_image_generation()
        
        # اختبار نظام التخزين المؤقت
        await test_caching_system(smart_manager)
        
        # اختبار إحصائيات اليوم
        stats = test_daily_stats(smart_manager)
        
        # اختبار إدارة الحصة
        test_quota_management(smart_manager)
        
        # اختبار فلترة الجودة
        await test_quality_filtering(smart_manager)
        
        # تحليل النتائج
        print("\n" + "=" * 60)
        print("📋 ملخص نتائج الاختبار:")
        print("-" * 30)
        
        successful_generations = sum(1 for r in results if r['success'])
        total_articles = len(results)
        
        print(f"إجمالي المقالات: {total_articles}")
        print(f"الصور المُنشأة: {successful_generations}")
        print(f"معدل النجاح: {(successful_generations/total_articles)*100:.1f}%")
        print(f"الصور المُنشأة اليوم: {stats['images_generated']}")
        print(f"معدل التخزين المؤقت: {stats['cache_hit_rate']:.1f}%")
        
        # تفاصيل النتائج
        print(f"\n📊 تفاصيل النتائج:")
        for result in results:
            status = "✅" if result['success'] else "❌"
            article_name = result['article'][:40]
            if result['success']:
                method = result.get('method', 'unknown')
                api = result.get('api_used', 'unknown')
                print(f"   {status} {article_name}... ({method} - {api})")
            else:
                reason = result.get('reason', result.get('error', 'unknown'))
                print(f"   {status} {article_name}... ({reason})")
        
        # تقييم النجاح
        if successful_generations > 0 and stats['cache_hit_rate'] >= 0:
            print("\n🎉 نظام إدارة الصور الذكي يعمل بنجاح!")
            print("\n💡 الميزات المحققة:")
            print("   • تحسين استهلاك API (صورة واحدة لكل مقال)")
            print("   • فلترة جودة المقالات")
            print("   • نظام تخزين مؤقت ذكي")
            print("   • إدارة حصة يومية")
            print("   • إحصائيات مفصلة")
            print("   • إعادة استخدام الصور المشابهة")
            
            return True
        else:
            print("\n⚠️ نظام إدارة الصور يحتاج مزيد من التحسين")
            return False
            
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
