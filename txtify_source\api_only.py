import os
import tempfile
from fastapi import FastAPI, Form, HTTPException
from fastapi.responses import JSONResponse
from loguru import logger
import whisper
import yt_dlp

# Basic setup
app = FastAPI()

# Define a writable directory for the model cache
CACHE_DIR = "/tmp/whisper_cache"
os.makedirs(CACHE_DIR, exist_ok=True)

# Global variable to hold the model. We'll load it on the first request.
model = None

def get_model():
    """Loads the model if it hasn't been loaded yet."""
    global model
    if model is None:
        logger.info("Model not loaded. Loading 'base' model now...")
        model = whisper.load_model("base", download_root=CACHE_DIR)
        logger.info("Model loaded successfully.")
    return model

@app.get("/")
def read_root():
    return {"message": "Welcome to the YouTube Transcriber API. Use the /transcribe_youtube endpoint with a POST request to transcribe a video."}

@app.post("/transcribe_youtube", response_class=JSONResponse)
async def transcribe_youtube_endpoint(youtube_url: str = Form(...)):
    """
    A simplified endpoint to transcribe a YouTube video using openai-whisper.
    """
    logger.info(f"Received request to transcribe: {youtube_url}")

    try:
        # Download audio from YouTube
        with tempfile.TemporaryDirectory() as tmpdir:
            ydl_opts = {
                'format': 'bestaudio/best',
                'outtmpl': os.path.join(tmpdir, 'audio.%(ext)s'),
                'postprocessors': [{
                    'key': 'FFmpegExtractAudio',
                    'preferredcodec': 'mp3',
                    'preferredquality': '192',
                }],
            }
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                ydl.download([youtube_url])
                audio_file_path = os.path.join(tmpdir, 'audio.mp3')

            # Get the model (it will be loaded on the first request)
            transcription_model = get_model()

            # Transcribe the audio file
            logger.info(f"Transcribing audio file: {audio_file_path}")
            result = transcription_model.transcribe(audio_file_path)
            transcript = result["text"]
            logger.info("Transcription successful.")

        return JSONResponse(content={"transcript": transcript})

    except Exception as e:
        logger.error(f"An error occurred: {e}")
        raise HTTPException(status_code=500, detail=str(e))
