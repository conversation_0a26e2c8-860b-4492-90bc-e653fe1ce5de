#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح NewsData.io API
"""

import asyncio
import sys
import os

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.advanced_news_apis import AdvancedNewsAPIs

async def test_newsdata_fix():
    """اختبار إصلاح NewsData.io API"""
    print("🔧 اختبار إصلاح NewsData.io API...")
    
    try:
        # إنشاء مثيل من AdvancedNewsAPIs
        news_apis = AdvancedNewsAPIs()
        
        # اختبار البحث المحسن
        print("📊 اختبار البحث المحسن في NewsData.io...")
        
        articles = await news_apis._search_newsdata(
            keywords=['gaming', 'video games'],
            max_results=15,
            days_back=7
        )
        
        if articles:
            print(f"✅ نجح الإصلاح! تم العثور على {len(articles)} مقال")
            print("📄 عينة من المقالات:")
            for i, article in enumerate(articles[:3], 1):
                title = article.get('title', 'بدون عنوان')[:60]
                source = article.get('source', 'مصدر غير محدد')
                print(f"   {i}. {title}... (المصدر: {source})")
            
            return True
        else:
            print("⚠️ لم يتم العثور على مقالات، لكن لا توجد أخطاء 422")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

async def main():
    """الدالة الرئيسية"""
    print("🧪 بدء اختبار إصلاح NewsData.io...")
    print("=" * 50)
    
    success = await test_newsdata_fix()
    
    print("=" * 50)
    if success:
        print("🎉 تم إصلاح مشكلة NewsData.io بنجاح!")
        print("💡 لن تظهر أخطاء 422 بعد الآن")
    else:
        print("❌ لا تزال هناك مشاكل تحتاج إصلاح")
    
    return success

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
