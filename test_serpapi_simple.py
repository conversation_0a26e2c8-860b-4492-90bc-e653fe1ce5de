#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لـ SerpAPI
"""

import aiohttp
import asyncio
import json

async def test_serpapi_direct():
    """اختبار مباشر لـ SerpAPI"""
    
    api_key = "cc5c6327238d333d9499f892db554a46e3b3c10e"
    base_url = "https://serpapi.com/search.json"
    
    params = {
        'api_key': api_key,
        'q': 'gaming news',
        'num': 5,
        'hl': 'en',
        'gl': 'us'
    }
    
    headers = {
        'Accept': 'application/json',
        'User-Agent': 'Gaming News Bot/1.0'
    }
    
    print("🔍 اختبار SerpAPI مباشرة...")
    print(f"URL: {base_url}")
    print(f"المعاملات: {params}")
    print(f"الرؤوس: {headers}")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(base_url, headers=headers, params=params) as response:
                print(f"كود الاستجابة: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    print("✅ نجح الطلب!")
                    
                    if 'organic_results' in data:
                        print(f"عدد النتائج العادية: {len(data['organic_results'])}")
                        if data['organic_results']:
                            first_result = data['organic_results'][0]
                            print(f"أول نتيجة: {first_result.get('title', '')[:50]}...")
                    
                    if 'news_results' in data:
                        print(f"عدد الأخبار: {len(data['news_results'])}")
                        if data['news_results']:
                            first_news = data['news_results'][0]
                            print(f"أول خبر: {first_news.get('title', '')[:50]}...")
                    
                    # عرض بعض البيانات
                    print(f"المفاتيح المتاحة: {list(data.keys())}")
                    
                else:
                    error_text = await response.text()
                    print(f"❌ فشل الطلب: {response.status}")
                    print(f"نص الخطأ: {error_text}")
                    
                    # محاولة تحليل JSON للخطأ
                    try:
                        error_data = json.loads(error_text)
                        if 'error' in error_data:
                            print(f"رسالة الخطأ: {error_data['error']}")
                    except:
                        pass
    
    except Exception as e:
        print(f"❌ خطأ في الطلب: {e}")

async def test_serpapi_news():
    """اختبار البحث في الأخبار"""
    
    api_key = "cc5c6327238d333d9499f892db554a46e3b3c10e"
    base_url = "https://serpapi.com/search.json"
    
    params = {
        'api_key': api_key,
        'q': 'gaming news',
        'tbm': 'nws',  # البحث في الأخبار
        'num': 5,
        'hl': 'en',
        'gl': 'us'
    }
    
    print("\n📰 اختبار البحث في الأخبار...")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(base_url, params=params) as response:
                print(f"كود الاستجابة: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    print("✅ نجح البحث في الأخبار!")
                    
                    if 'news_results' in data:
                        print(f"عدد الأخبار: {len(data['news_results'])}")
                        for i, news in enumerate(data['news_results'][:3], 1):
                            print(f"{i}. {news.get('title', '')[:60]}...")
                            print(f"   المصدر: {news.get('source', '')}")
                            print(f"   التاريخ: {news.get('date', '')}")
                    else:
                        print("لا توجد أخبار في النتائج")
                        print(f"المفاتيح المتاحة: {list(data.keys())}")
                else:
                    error_text = await response.text()
                    print(f"❌ فشل البحث في الأخبار: {response.status}")
                    print(f"نص الخطأ: {error_text}")
    
    except Exception as e:
        print(f"❌ خطأ في البحث في الأخبار: {e}")

if __name__ == "__main__":
    print("🧪 اختبار SerpAPI مباشرة...")
    
    try:
        asyncio.run(test_serpapi_direct())
        asyncio.run(test_serpapi_news())
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
