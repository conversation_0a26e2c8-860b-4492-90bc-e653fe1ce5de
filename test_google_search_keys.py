#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مفاتيح Google Search API الجديدة
"""

import asyncio
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.web_search import WebSearch
from modules.advanced_web_scraper import AdvancedWebScraper
from config.settings import BotConfig, google_search_api_manager
from modules.logger import logger

class GoogleSearchKeysTest:
    """فئة اختبار مفاتيح Google Search API"""
    
    def __init__(self):
        self.test_queries = [
            "gaming news today",
            "new video games 2025",
            "minecraft updates",
            "gaming industry news",
            "esports tournaments"
        ]
    
    async def test_web_search_module(self):
        """اختبار وحدة البحث عبر الويب"""
        print("\n🔍 اختبار وحدة البحث عبر الويب...")
        
        try:
            web_search = WebSearch(BotConfig.GOOGLE_SEARCH_ENGINE_ID)
            
            if not web_search.enabled:
                print("❌ وحدة البحث عبر الويب غير مفعلة")
                return False
            
            print(f"✅ تم تهيئة البحث مع {web_search.get_available_keys_count()} مفتاح متاح")
            
            # اختبار عدة استعلامات
            total_results = 0
            successful_searches = 0
            
            for query in self.test_queries:
                print(f"\n🔎 البحث عن: {query}")
                results = web_search.search(query, num_results=3)
                
                if results and len(results) > 0:
                    successful_searches += 1
                    total_results += len(results)
                    print(f"   ✅ {len(results)} نتيجة")
                    for i, result in enumerate(results[:2], 1):
                        title = result.get('title', 'بدون عنوان')[:50]
                        print(f"      {i}. {title}...")
                else:
                    print(f"   ❌ لا توجد نتائج")
                
                # تأخير قصير بين الطلبات
                await asyncio.sleep(1)
            
            # عرض الإحصائيات
            print(f"\n📊 إحصائيات الاختبار:")
            print(f"   - إجمالي الاستعلامات: {len(self.test_queries)}")
            print(f"   - الاستعلامات الناجحة: {successful_searches}")
            print(f"   - إجمالي النتائج: {total_results}")
            print(f"   - معدل النجاح: {(successful_searches/len(self.test_queries)*100):.1f}%")
            
            # عرض إحصائيات المفاتيح
            api_stats = web_search.get_api_usage_stats()
            if api_stats:
                print(f"\n🔑 إحصائيات المفاتيح:")
                print(f"   - إجمالي المفاتيح: {api_stats.get('total_keys', 0)}")
                print(f"   - المفاتيح المتاحة: {api_stats.get('available_keys', 0)}")
                print(f"   - المفاتيح المحظورة: {api_stats.get('blacklisted_keys', 0)}")
                print(f"   - إجمالي الطلبات: {api_stats.get('total_calls', 0)}")
            
            return successful_searches > 0
            
        except Exception as e:
            print(f"❌ خطأ في اختبار وحدة البحث: {e}")
            return False
    
    async def test_advanced_scraper(self):
        """اختبار نظام الاستخراج المتقدم"""
        print("\n🕷️ اختبار نظام الاستخراج المتقدم...")
        
        try:
            scraper = AdvancedWebScraper()
            
            # اختبار البحث الشامل
            query = "gaming news"
            results = await scraper.comprehensive_gaming_search(query, max_results=10)
            
            if results and len(results) > 0:
                print(f"✅ نظام الاستخراج المتقدم: {len(results)} نتيجة")
                
                # عرض عينة من النتائج
                for i, result in enumerate(results[:3], 1):
                    title = result.get('title', 'بدون عنوان')[:60]
                    source = result.get('source', 'مصدر غير معروف')
                    print(f"   {i}. {title}... (المصدر: {source})")
                
                return True
            else:
                print("❌ نظام الاستخراج المتقدم لا يعيد نتائج")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في اختبار نظام الاستخراج المتقدم: {e}")
            return False
    
    async def test_api_key_rotation(self):
        """اختبار تدوير مفاتيح API"""
        print("\n🔄 اختبار تدوير مفاتيح API...")
        
        try:
            if not google_search_api_manager:
                print("❌ مدير مفاتيح Google Search غير متاح")
                return False
            
            print(f"🔑 عدد المفاتيح المتاحة: {len(google_search_api_manager.keys)}")
            
            # اختبار الحصول على مفاتيح متعددة
            keys_used = set()
            for i in range(min(3, len(google_search_api_manager.keys))):
                key = google_search_api_manager.get_key()
                masked_key = f"{key[:8]}...{key[-8:]}"
                keys_used.add(masked_key)
                print(f"   المفتاح {i+1}: {masked_key}")
            
            print(f"✅ تم استخدام {len(keys_used)} مفتاح مختلف")
            
            # عرض إحصائيات الاستخدام
            stats = google_search_api_manager.get_usage_stats()
            print(f"\n📊 إحصائيات مدير المفاتيح:")
            print(f"   - إجمالي المفاتيح: {stats.get('total_keys', 0)}")
            print(f"   - المفاتيح المتاحة: {stats.get('available_keys', 0)}")
            print(f"   - إجمالي الطلبات: {stats.get('total_calls', 0)}")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في اختبار تدوير المفاتيح: {e}")
            return False
    
    async def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🚀 بدء اختبار مفاتيح Google Search API الجديدة...\n")
        
        tests = [
            ("تدوير مفاتيح API", self.test_api_key_rotation),
            ("وحدة البحث عبر الويب", self.test_web_search_module),
            ("نظام الاستخراج المتقدم", self.test_advanced_scraper)
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            try:
                print(f"\n{'='*50}")
                result = await test_func()
                results[test_name] = result
                status = "✅ نجح" if result else "❌ فشل"
                print(f"\n{test_name}: {status}")
            except Exception as e:
                results[test_name] = False
                print(f"\n{test_name}: ❌ خطأ - {e}")
        
        # تقرير نهائي
        print(f"\n{'='*50}")
        print("📋 تقرير الاختبارات النهائي:")
        print(f"{'='*50}")
        
        passed = sum(1 for result in results.values() if result)
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ نجح" if result else "❌ فشل"
            print(f"   {test_name}: {status}")
        
        print(f"\n📊 النتيجة الإجمالية: {passed}/{total} اختبار نجح")
        success_rate = (passed / total) * 100
        print(f"📈 معدل النجاح: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("\n🎉 ممتاز! مفاتيح Google Search API تعمل بشكل جيد")
        elif success_rate >= 60:
            print("\n⚠️ جيد، لكن يحتاج تحسين")
        else:
            print("\n🚨 يحتاج إلى مراجعة وإصلاح")
        
        return success_rate >= 60

async def main():
    """الدالة الرئيسية"""
    tester = GoogleSearchKeysTest()
    success = await tester.run_all_tests()
    
    if success:
        print("\n✅ اختبار مفاتيح Google Search API مكتمل بنجاح!")
    else:
        print("\n❌ فشل في اختبار مفاتيح Google Search API")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
