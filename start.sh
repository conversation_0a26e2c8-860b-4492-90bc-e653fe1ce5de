#!/bin/bash
# سكريبت تشغيل وكيل أخبار ماين كرافت

echo "🚀 بدء تشغيل وكيل أخبار ماين كرافت..."

# التحقق من وجود Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 غير مثبت، يرجى تثبيته أولاً"
    exit 1
fi

# التحقق من وجود المتطلبات
if [ ! -f "requirements.txt" ]; then
    echo "❌ ملف requirements.txt غير موجود"
    exit 1
fi

# التحقق من التكوين
if [ ! -f ".env" ] && [ ! -f "config/bot_config.json" ]; then
    echo "⚠️ لم يتم العثور على تكوين البوت"
    echo "تشغيل الإعداد التفاعلي..."
    python3 setup_bot.py
    
    if [ $? -ne 0 ]; then
        echo "❌ فشل في الإعداد"
        exit 1
    fi
fi

# تثبيت المتطلبات إذا لزم الأمر
echo "📦 التحقق من المتطلبات..."
pip3 install -r requirements.txt --quiet

# إنشاء المجلدات المطلوبة
mkdir -p logs data config images

# تشغيل البوت
echo "🤖 تشغيل البوت..."
python3 main.py

# في حالة توقف البوت بشكل غير متوقع
if [ $? -ne 0 ]; then
    echo "❌ توقف البوت بشكل غير متوقع"
    echo "📋 آخر سطر من السجلات:"
    tail -n 5 logs/bot.log 2>/dev/null || echo "لا توجد سجلات متاحة"
fi
