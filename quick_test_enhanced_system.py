#!/usr/bin/env python3
# اختبار سريع للنظام المحسن
import asyncio
import time
import json
import sys
import os

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def print_header(title):
    """طباعة عنوان مع تنسيق"""
    print("\n" + "="*60)
    print(f"🔧 {title}")
    print("="*60)

def print_result(test_name, success, details=""):
    """طباعة نتيجة اختبار"""
    status = "✅ نجح" if success else "❌ فشل"
    print(f"{status} {test_name}")
    if details:
        print(f"   📝 {details}")

async def test_basic_imports():
    """اختبار الاستيرادات الأساسية"""
    print_header("اختبار الاستيرادات الأساسية")
    
    try:
        # اختبار استيراد المكونات الأساسية
        from modules.smart_search_manager import smart_search_manager
        print_result("Smart Search Manager", True, "تم الاستيراد بنجاح")
        
        from modules.advanced_cache_system import advanced_cache
        print_result("Advanced Cache System", True, "تم الاستيراد بنجاح")
        
        from modules.rate_limit_manager import rate_limit_manager
        print_result("Rate Limit Manager", True, "تم الاستيراد بنجاح")
        
        from modules.search_analytics import search_analytics
        print_result("Search Analytics", True, "تم الاستيراد بنجاح")
        
        from modules.enhanced_search_integration import enhanced_search
        print_result("Enhanced Search Integration", True, "تم الاستيراد بنجاح")
        
        return True
        
    except Exception as e:
        print_result("Basic Imports", False, f"خطأ في الاستيراد: {e}")
        return False

async def test_cache_system():
    """اختبار نظام التخزين المؤقت"""
    print_header("اختبار نظام التخزين المؤقت")
    
    try:
        from modules.advanced_cache_system import advanced_cache
        
        # اختبار الحفظ
        test_data = {"message": "Hello Enhanced System", "timestamp": time.time()}
        success = advanced_cache.set("test_key_enhanced", test_data, ttl=60)
        print_result("Cache Set", success, f"حفظ البيانات: {success}")
        
        # اختبار الاسترجاع
        retrieved_data = advanced_cache.get("test_key_enhanced")
        cache_hit = retrieved_data == test_data
        print_result("Cache Get", cache_hit, f"استرجاع البيانات: {cache_hit}")
        
        # اختبار الإحصائيات
        stats = advanced_cache.get_stats()
        has_stats = isinstance(stats, dict) and 'hits' in stats
        print_result("Cache Stats", has_stats, f"الإحصائيات: {stats}")
        
        # تنظيف
        advanced_cache.delete("test_key_enhanced")
        
        return success and cache_hit and has_stats
        
    except Exception as e:
        print_result("Cache System", False, f"خطأ: {e}")
        return False

async def test_rate_limiting():
    """اختبار نظام إدارة معدل الطلبات"""
    print_header("اختبار نظام إدارة معدل الطلبات")
    
    try:
        from modules.rate_limit_manager import rate_limit_manager
        
        # اختبار فحص الحدود
        can_request, message, wait_time = await rate_limit_manager.can_make_request("test_service_enhanced")
        print_result("Rate Limit Check", isinstance(can_request, bool), 
                    f"يمكن الطلب: {can_request}, الرسالة: {message}")
        
        # اختبار تسجيل الطلب
        await rate_limit_manager.record_request("test_service_enhanced", True, 1.5, 0.01)
        print_result("Request Recording", True, "تم تسجيل الطلب بنجاح")
        
        # اختبار الإحصائيات
        stats = rate_limit_manager.get_service_stats("test_service_enhanced")
        has_stats = isinstance(stats, dict)
        print_result("Rate Limit Stats", has_stats, f"الإحصائيات: {stats}")
        
        return True
        
    except Exception as e:
        print_result("Rate Limiting", False, f"خطأ: {e}")
        return False

async def test_search_analytics():
    """اختبار نظام التحليلات"""
    print_header("اختبار نظام التحليلات")
    
    try:
        from modules.search_analytics import search_analytics, SearchAnalytics
        
        # إنشاء تحليلات اختبار
        analytics = SearchAnalytics(
            query="test enhanced query",
            timestamp=time.time(),
            search_engine="enhanced_test",
            results_count=5,
            response_time=1.8,
            cache_hit=True,
            quality_score=85.0,
            cost=0.015,
            success=True
        )
        
        # تسجيل التحليلات
        search_analytics.record_search(analytics)
        print_result("Analytics Recording", True, "تم تسجيل التحليلات")
        
        # الحصول على مقاييس الأداء
        metrics = search_analytics.get_performance_metrics(1)
        has_metrics = hasattr(metrics, 'avg_response_time')
        print_result("Performance Metrics", has_metrics, 
                    f"متوسط الوقت: {getattr(metrics, 'avg_response_time', 'غير متاح')}")
        
        # الحصول على إحصائيات الجلسة
        session_stats = search_analytics.get_current_session_stats()
        has_session_stats = isinstance(session_stats, dict)
        print_result("Session Stats", has_session_stats, f"إحصائيات الجلسة: {session_stats}")
        
        return True
        
    except Exception as e:
        print_result("Search Analytics", False, f"خطأ: {e}")
        return False

async def test_enhanced_search():
    """اختبار البحث المحسن"""
    print_header("اختبار البحث المحسن")
    
    try:
        from modules.enhanced_search_integration import enhanced_search
        
        # اختبار بحث بسيط
        print("🔍 تشغيل بحث اختبار...")
        start_time = time.time()
        
        results = await enhanced_search.enhanced_search(
            query="gaming news test",
            max_results=3,
            priority="free"
        )
        
        end_time = time.time()
        search_time = end_time - start_time
        
        is_list = isinstance(results, list)
        print_result("Enhanced Search", is_list, 
                    f"النتائج: {len(results) if is_list else 'خطأ'}, الوقت: {search_time:.2f}s")
        
        # اختبار حالة النظام
        status = enhanced_search.get_system_status()
        has_status = isinstance(status, dict)
        print_result("System Status", has_status, f"الحالة: {status}")
        
        return is_list
        
    except Exception as e:
        print_result("Enhanced Search", False, f"خطأ: {e}")
        return False

async def test_integration():
    """اختبار التكامل الشامل"""
    print_header("اختبار التكامل الشامل")
    
    try:
        from enhanced_main_integration import enhanced_collector
        
        print("🔍 تشغيل جمع المحتوى المحسن...")
        start_time = time.time()
        
        # تشغيل جمع المحتوى (مع حد زمني)
        try:
            content = await asyncio.wait_for(
                enhanced_collector.collect_content_enhanced(),
                timeout=30.0  # 30 ثانية كحد أقصى
            )
            
            end_time = time.time()
            collection_time = end_time - start_time
            
            is_list = isinstance(content, list)
            content_count = len(content) if is_list else 0
            
            print_result("Content Collection", is_list, 
                        f"المحتوى: {content_count} مقال، الوقت: {collection_time:.2f}s")
            
            # عرض عينة من النتائج
            if content_count > 0:
                print("\n📰 عينة من النتائج:")
                for i, article in enumerate(content[:2]):
                    title = article.get('title', 'غير محدد')[:60]
                    quality = article.get('total_quality_score', article.get('quality_score', 0))
                    print(f"   {i+1}. {title}... (جودة: {quality:.1f})")
            
            return is_list and content_count >= 0
            
        except asyncio.TimeoutError:
            print_result("Content Collection", False, "انتهت المهلة الزمنية (30s)")
            return False
        
    except Exception as e:
        print_result("Integration Test", False, f"خطأ: {e}")
        return False

async def run_comprehensive_test():
    """تشغيل اختبار شامل"""
    print("🧪 بدء الاختبار الشامل للنظام المحسن")
    print("⏰ هذا قد يستغرق بضع دقائق...")
    
    test_results = []
    
    # تشغيل جميع الاختبارات
    tests = [
        ("الاستيرادات الأساسية", test_basic_imports),
        ("نظام التخزين المؤقت", test_cache_system),
        ("إدارة معدل الطلبات", test_rate_limiting),
        ("نظام التحليلات", test_search_analytics),
        ("البحث المحسن", test_enhanced_search),
        ("التكامل الشامل", test_integration)
    ]
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔄 تشغيل اختبار: {test_name}")
            result = await test_func()
            test_results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
                
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
            test_results.append((test_name, False))
    
    # عرض النتائج النهائية
    print_header("النتائج النهائية")
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    success_rate = (passed / total) * 100 if total > 0 else 0
    
    print(f"📊 إجمالي الاختبارات: {total}")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {total - passed}")
    print(f"📈 معدل النجاح: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("\n🎉 النظام المحسن يعمل بشكل ممتاز!")
        print("💡 يمكنك الآن استخدام النظام في الإنتاج")
    elif success_rate >= 60:
        print("\n⚠️ النظام يعمل بشكل جيد مع بعض المشاكل")
        print("💡 راجع الاختبارات الفاشلة وأصلح المشاكل")
    else:
        print("\n❌ النظام يحتاج إلى إصلاحات")
        print("💡 راجع السجلات وأصلح الأخطاء قبل الاستخدام")
    
    # عرض الاختبارات الفاشلة
    failed_tests = [name for name, result in test_results if not result]
    if failed_tests:
        print(f"\n❌ الاختبارات الفاشلة:")
        for test_name in failed_tests:
            print(f"   - {test_name}")
    
    # حفظ النتائج
    results_data = {
        'timestamp': time.time(),
        'total_tests': total,
        'passed_tests': passed,
        'failed_tests': total - passed,
        'success_rate': success_rate,
        'test_details': [{'name': name, 'passed': result} for name, result in test_results]
    }
    
    try:
        with open('quick_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(results_data, f, ensure_ascii=False, indent=2)
        print(f"\n💾 تم حفظ النتائج في: quick_test_results.json")
    except Exception as e:
        print(f"⚠️ فشل في حفظ النتائج: {e}")

async def main():
    """الدالة الرئيسية"""
    await run_comprehensive_test()

if __name__ == "__main__":
    asyncio.run(main())
