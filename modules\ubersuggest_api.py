# نظام Ubersuggest API المجاني
import asyncio
import aiohttp
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
from .logger import logger

class UbersuggestAPI:
    """API Ubersuggest للكلمات المفتاحية المجانية"""
    
    def __init__(self, api_key: str = '24d5c97abaee9b5c8230d1bb50f796de8dd3c629'):
        self.api_key = api_key
        self.base_url = "https://app.neilpatel.com/api"
        self.session = None
        
    async def __aenter__(self):
        """إنشاء جلسة HTTP"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """إغلاق جلسة HTTP"""
        if self.session:
            await self.session.close()
    
    async def get_keyword_suggestions(self, keyword: str, language: str = 'ar', 
                                    country: str = 'SA') -> Dict:
        """الحصول على اقتراحات الكلمات المفتاحية"""
        try:
            # محاكاة استجابة Ubersuggest (في التطبيق الحقيقي، استخدم API الفعلي)
            suggestions = await self._simulate_ubersuggest_data(keyword, language, country)
            
            # تحليل الاقتراحات
            analyzed_suggestions = self._analyze_keyword_suggestions(suggestions)
            
            return {
                'keyword': keyword,
                'language': language,
                'country': country,
                'suggestions': suggestions,
                'analysis': analyzed_suggestions,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ فشل في الحصول على اقتراحات الكلمات المفتاحية لـ {keyword}", e)
            return {}
    
    async def _simulate_ubersuggest_data(self, keyword: str, language: str, country: str) -> List[Dict]:
        """محاكاة بيانات Ubersuggest"""
        import random
        
        # قوالب اقتراحات الكلمات المفتاحية
        suggestion_templates = [
            f"{keyword} 2025",
            f"أفضل {keyword}",
            f"{keyword} مجاني",
            f"{keyword} للمبتدئين",
            f"كيفية {keyword}",
            f"{keyword} جديد",
            f"{keyword} مراجعة",
            f"{keyword} دليل",
            f"{keyword} نصائح",
            f"{keyword} استراتيجية"
        ]
        
        # إضافة اقتراحات خاصة بالألعاب
        if any(game_word in keyword.lower() for game_word in ['game', 'gaming', 'لعبة', 'ألعاب']):
            gaming_templates = [
                f"{keyword} تحديث",
                f"{keyword} أخبار",
                f"{keyword} تحميل",
                f"{keyword} مود",
                f"{keyword} سيرفر",
                f"{keyword} بناء",
                f"{keyword} استراتيجية",
                f"{keyword} مراجعة",
                f"{keyword} فيديو",
                f"{keyword} بث مباشر"
            ]
            suggestion_templates.extend(gaming_templates)
        
        suggestions = []
        for i, template in enumerate(suggestion_templates[:15]):  # أفضل 15 اقتراح
            suggestion = {
                'keyword': template,
                'search_volume': self._generate_search_volume(keyword, i),
                'seo_difficulty': random.randint(20, 80),
                'paid_difficulty': random.randint(30, 90),
                'cpc': round(random.uniform(0.1, 2.5), 2),
                'trend': random.choice(['up', 'down', 'stable']),
                'competition': random.choice(['low', 'medium', 'high'])
            }
            suggestions.append(suggestion)
        
        return suggestions
    
    def _generate_search_volume(self, base_keyword: str, index: int) -> int:
        """توليد حجم البحث المحاكي"""
        import random
        
        # حجم البحث الأساسي
        base_volume = 1000
        
        # تعديل بناءً على نوع الكلمة
        if any(word in base_keyword.lower() for word in ['minecraft', 'fortnite', 'pubg']):
            base_volume *= 5
        elif any(word in base_keyword.lower() for word in ['gaming', 'game', 'ألعاب']):
            base_volume *= 3
        elif any(word in base_keyword.lower() for word in ['news', 'أخبار']):
            base_volume *= 2
        
        # تقليل الحجم للاقتراحات الأطول
        volume_multiplier = max(0.1, 1 - (index * 0.1))
        
        final_volume = int(base_volume * volume_multiplier * random.uniform(0.5, 1.5))
        return max(100, final_volume)  # حد أدنى 100
    
    def _analyze_keyword_suggestions(self, suggestions: List[Dict]) -> Dict:
        """تحليل اقتراحات الكلمات المفتاحية"""
        try:
            if not suggestions:
                return {}
            
            # تحليل إحصائي
            total_volume = sum(s['search_volume'] for s in suggestions)
            avg_difficulty = sum(s['seo_difficulty'] for s in suggestions) / len(suggestions)
            avg_cpc = sum(s['cpc'] for s in suggestions) / len(suggestions)
            
            # تصنيف الاقتراحات
            high_volume = [s for s in suggestions if s['search_volume'] > 1000]
            low_difficulty = [s for s in suggestions if s['seo_difficulty'] < 40]
            high_value = [s for s in suggestions if s['search_volume'] > 500 and s['seo_difficulty'] < 50]
            
            # اقتراحات Long-tail
            long_tail = [s for s in suggestions if len(s['keyword'].split()) >= 3]
            
            # تحليل الاتجاهات
            trending_up = [s for s in suggestions if s['trend'] == 'up']
            
            return {
                'total_suggestions': len(suggestions),
                'total_search_volume': total_volume,
                'average_difficulty': round(avg_difficulty, 1),
                'average_cpc': round(avg_cpc, 2),
                'high_volume_keywords': len(high_volume),
                'low_difficulty_keywords': len(low_difficulty),
                'high_value_opportunities': len(high_value),
                'long_tail_keywords': len(long_tail),
                'trending_keywords': len(trending_up),
                'top_opportunities': self._identify_top_opportunities(suggestions),
                'keyword_categories': self._categorize_keywords(suggestions)
            }
            
        except Exception as e:
            logger.error("❌ فشل في تحليل اقتراحات الكلمات المفتاحية", e)
            return {}
    
    def _identify_top_opportunities(self, suggestions: List[Dict]) -> List[Dict]:
        """تحديد أفضل الفرص"""
        # حساب نقاط الفرصة لكل كلمة مفتاحية
        for suggestion in suggestions:
            volume_score = min(50, (suggestion['search_volume'] / 100) * 10)
            difficulty_score = max(0, 50 - suggestion['seo_difficulty'])
            trend_score = 10 if suggestion['trend'] == 'up' else 5 if suggestion['trend'] == 'stable' else 0
            
            suggestion['opportunity_score'] = volume_score + difficulty_score + trend_score
        
        # ترتيب حسب نقاط الفرصة
        top_opportunities = sorted(suggestions, key=lambda x: x['opportunity_score'], reverse=True)[:5]
        
        return [
            {
                'keyword': opp['keyword'],
                'search_volume': opp['search_volume'],
                'seo_difficulty': opp['seo_difficulty'],
                'opportunity_score': round(opp['opportunity_score'], 1),
                'recommendation': self._generate_keyword_recommendation(opp)
            }
            for opp in top_opportunities
        ]
    
    def _generate_keyword_recommendation(self, keyword_data: Dict) -> str:
        """توليد توصية للكلمة المفتاحية"""
        volume = keyword_data['search_volume']
        difficulty = keyword_data['seo_difficulty']
        
        if volume > 1000 and difficulty < 40:
            return "فرصة ذهبية - حجم بحث عالي وصعوبة منخفضة"
        elif volume > 500 and difficulty < 60:
            return "فرصة جيدة - توازن مناسب بين الحجم والصعوبة"
        elif difficulty < 30:
            return "سهل الترتيب - مناسب للبداية"
        elif volume > 2000:
            return "حجم بحث عالي - يستحق الاستثمار"
        else:
            return "فرصة متوسطة - مناسب للمحتوى الداعم"
    
    def _categorize_keywords(self, suggestions: List[Dict]) -> Dict:
        """تصنيف الكلمات المفتاحية"""
        categories = {
            'informational': [],  # معلوماتية
            'commercial': [],     # تجارية
            'navigational': [],   # تصفحية
            'transactional': []   # معاملاتية
        }
        
        for suggestion in suggestions:
            keyword = suggestion['keyword'].lower()
            
            # كلمات معلوماتية
            if any(word in keyword for word in ['كيف', 'ماذا', 'لماذا', 'دليل', 'نصائح', 'how', 'what', 'guide']):
                categories['informational'].append(suggestion)
            
            # كلمات تجارية
            elif any(word in keyword for word in ['أفضل', 'مراجعة', 'مقارنة', 'best', 'review', 'vs']):
                categories['commercial'].append(suggestion)
            
            # كلمات معاملاتية
            elif any(word in keyword for word in ['تحميل', 'شراء', 'مجاني', 'download', 'buy', 'free']):
                categories['transactional'].append(suggestion)
            
            # كلمات تصفحية (افتراضي)
            else:
                categories['navigational'].append(suggestion)
        
        return {
            category: len(keywords) for category, keywords in categories.items()
        }
    
    async def get_competitor_keywords(self, domain: str, limit: int = 20) -> Dict:
        """الحصول على كلمات المنافسين المفتاحية"""
        try:
            # محاكاة بيانات كلمات المنافسين
            competitor_keywords = await self._simulate_competitor_data(domain, limit)
            
            return {
                'domain': domain,
                'total_keywords': len(competitor_keywords),
                'keywords': competitor_keywords,
                'analysis': self._analyze_competitor_keywords(competitor_keywords),
                'opportunities': self._find_competitor_opportunities(competitor_keywords),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ فشل في الحصول على كلمات المنافس {domain}", e)
            return {}
    
    async def _simulate_competitor_data(self, domain: str, limit: int) -> List[Dict]:
        """محاكاة بيانات كلمات المنافسين"""
        import random
        
        # كلمات مفتاحية شائعة في مجال الألعاب
        gaming_keywords = [
            'gaming news', 'video game reviews', 'minecraft updates', 'fortnite news',
            'gaming laptops', 'best games 2025', 'game trailers', 'gaming tips',
            'esports news', 'game releases', 'gaming hardware', 'game guides',
            'mobile games', 'pc games', 'console games', 'indie games',
            'game mods', 'gaming community', 'game streaming', 'gaming tutorials'
        ]
        
        competitor_keywords = []
        for i in range(min(limit, len(gaming_keywords))):
            keyword = random.choice(gaming_keywords)
            competitor_keywords.append({
                'keyword': keyword,
                'position': random.randint(1, 50),
                'search_volume': random.randint(500, 5000),
                'traffic': random.randint(50, 1000),
                'difficulty': random.randint(30, 80),
                'url': f"https://{domain}/{keyword.replace(' ', '-')}"
            })
        
        return competitor_keywords
    
    def _analyze_competitor_keywords(self, keywords: List[Dict]) -> Dict:
        """تحليل كلمات المنافسين"""
        if not keywords:
            return {}
        
        # تحليل المواقع
        top_10_positions = [k for k in keywords if k['position'] <= 10]
        avg_position = sum(k['position'] for k in keywords) / len(keywords)
        total_traffic = sum(k['traffic'] for k in keywords)
        
        # تحليل الصعوبة
        easy_keywords = [k for k in keywords if k['difficulty'] < 40]
        medium_keywords = [k for k in keywords if 40 <= k['difficulty'] < 70]
        hard_keywords = [k for k in keywords if k['difficulty'] >= 70]
        
        return {
            'total_keywords': len(keywords),
            'top_10_rankings': len(top_10_positions),
            'average_position': round(avg_position, 1),
            'estimated_traffic': total_traffic,
            'keyword_difficulty_distribution': {
                'easy': len(easy_keywords),
                'medium': len(medium_keywords),
                'hard': len(hard_keywords)
            },
            'top_performing_keywords': sorted(keywords, key=lambda x: x['traffic'], reverse=True)[:5]
        }
    
    def _find_competitor_opportunities(self, keywords: List[Dict]) -> List[Dict]:
        """العثور على فرص من كلمات المنافسين"""
        opportunities = []
        
        for keyword in keywords:
            # فرص للكلمات التي يرتب عليها المنافس في المواقع 11-30
            if 11 <= keyword['position'] <= 30 and keyword['difficulty'] < 60:
                opportunities.append({
                    'keyword': keyword['keyword'],
                    'competitor_position': keyword['position'],
                    'search_volume': keyword['search_volume'],
                    'difficulty': keyword['difficulty'],
                    'opportunity_type': 'easy_outrank',
                    'recommendation': 'يمكن تجاوز المنافس بمحتوى أفضل'
                })
            
            # فرص للكلمات عالية الحجم
            elif keyword['search_volume'] > 2000 and keyword['position'] > 20:
                opportunities.append({
                    'keyword': keyword['keyword'],
                    'competitor_position': keyword['position'],
                    'search_volume': keyword['search_volume'],
                    'difficulty': keyword['difficulty'],
                    'opportunity_type': 'high_volume',
                    'recommendation': 'كلمة عالية الحجم - تستحق الاستثمار'
                })
        
        return sorted(opportunities, key=lambda x: x['search_volume'], reverse=True)[:10]
    
    async def get_content_ideas(self, keyword: str, content_type: str = 'article') -> Dict:
        """الحصول على أفكار المحتوى"""
        try:
            # توليد أفكار المحتوى بناءً على الكلمة المفتاحية
            content_ideas = self._generate_content_ideas(keyword, content_type)
            
            return {
                'keyword': keyword,
                'content_type': content_type,
                'ideas': content_ideas,
                'seo_tips': self._generate_seo_tips(keyword),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ فشل في توليد أفكار المحتوى لـ {keyword}", e)
            return {}
    
    def _generate_content_ideas(self, keyword: str, content_type: str) -> List[Dict]:
        """توليد أفكار المحتوى"""
        ideas = []
        
        if content_type == 'article':
            article_templates = [
                f"دليل شامل لـ {keyword}",
                f"أفضل 10 نصائح لـ {keyword}",
                f"كل ما تحتاج معرفته عن {keyword}",
                f"{keyword}: دليل المبتدئين الكامل",
                f"أخطاء شائعة في {keyword} وكيفية تجنبها",
                f"مستقبل {keyword} في 2025",
                f"مقارنة شاملة: {keyword} vs البدائل",
                f"تاريخ وتطور {keyword}"
            ]
            
            for template in article_templates:
                ideas.append({
                    'title': template,
                    'type': 'article',
                    'estimated_length': '1500-2500 كلمة',
                    'difficulty': 'متوسط',
                    'seo_potential': 'عالي'
                })
        
        elif content_type == 'video':
            video_templates = [
                f"شرح {keyword} في 10 دقائق",
                f"أسرار {keyword} التي لا يعرفها الجميع",
                f"تجربة {keyword} المباشرة",
                f"مراجعة صادقة لـ {keyword}",
                f"كيفية البدء في {keyword}"
            ]
            
            for template in video_templates:
                ideas.append({
                    'title': template,
                    'type': 'video',
                    'estimated_length': '8-15 دقيقة',
                    'difficulty': 'متوسط',
                    'engagement_potential': 'عالي'
                })
        
        return ideas[:8]  # أفضل 8 أفكار
    
    def _generate_seo_tips(self, keyword: str) -> List[str]:
        """توليد نصائح SEO"""
        tips = [
            f"استخدم '{keyword}' في العنوان الرئيسي (H1)",
            f"اذكر '{keyword}' في أول 100 كلمة من المحتوى",
            f"أنشئ عناوين فرعية تحتوي على كلمات مرتبطة بـ '{keyword}'",
            f"أضف صور مع alt text يحتوي على '{keyword}'",
            f"اربط المحتوى بمقالات أخرى ذات صلة بـ '{keyword}'",
            f"استخدم كلمات مفتاحية طويلة الذيل مرتبطة بـ '{keyword}'",
            f"أنشئ meta description جذاب يحتوي على '{keyword}'",
            f"تأكد من سرعة تحميل الصفحة لتحسين ترتيب '{keyword}'"
        ]
        
        return tips

# إنشاء مثيل عام لـ Ubersuggest API
ubersuggest_api = UbersuggestAPI()
