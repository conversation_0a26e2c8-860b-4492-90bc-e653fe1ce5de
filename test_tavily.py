#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار Tavily للبحث العميق مع الذكاء الاصطناعي
"""

import asyncio
import sys
import os

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.tavily_search import tavily_search
from modules.logger import logger

async def test_tavily_basic():
    """اختبار أساسي لـ Tavily"""
    
    print("🔍 اختبار Tavily للبحث العميق")
    print("=" * 50)
    
    # 1. اختبار الاتصال
    print("\n1️⃣ اختبار الاتصال...")
    connection_test = await tavily_search.test_connection()
    print(f"حالة الاتصال: {connection_test['status']}")
    print(f"الرسالة: {connection_test['message']}")
    print(f"صحة المفتاح: {connection_test['api_key_valid']}")
    
    if not connection_test['api_key_valid']:
        print("❌ فشل في الاتصال بـ Tavily")
        return False
    
    return True

async def test_tavily_search():
    """اختبار البحث في Tavily"""
    
    # 2. اختبار البحث الأساسي
    print("\n2️⃣ اختبار البحث الأساسي...")
    basic_results = await tavily_search.search("gaming news", search_depth="basic", max_results=3)
    print(f"عدد النتائج الأساسية: {len(basic_results)}")
    
    if basic_results:
        print("\nأول نتيجة أساسية:")
        first_result = basic_results[0]
        print(f"العنوان: {first_result.get('title', '')[:80]}...")
        print(f"المصدر: {first_result.get('source', '')}")
        print(f"درجة الصلة: {first_result.get('relevance_score', 0)}")
        print(f"نوع المحتوى: {first_result.get('content_type', '')}")
        
        # عرض الملخص الذكي إذا كان متوفراً
        if first_result.get('is_ai_generated'):
            print(f"🤖 ملخص ذكي: {first_result.get('content', '')[:150]}...")
    
    # 3. اختبار البحث المتقدم
    print("\n3️⃣ اختبار البحث المتقدم...")
    advanced_results = await tavily_search.search(
        "new video game releases 2025", 
        search_depth="advanced", 
        max_results=5
    )
    print(f"عدد النتائج المتقدمة: {len(advanced_results)}")
    
    if advanced_results:
        print("\nأفضل النتائج المتقدمة:")
        for i, result in enumerate(advanced_results[:3], 1):
            print(f"{i}. {result.get('title', '')[:60]}...")
            print(f"   المصدر: {result.get('source', '')}")
            print(f"   الصلة: {result.get('relevance_score', 0):.1f}/10")
            print(f"   صلة الألعاب: {result.get('gaming_relevance', 0)}")
    
    return len(basic_results) > 0 or len(advanced_results) > 0

async def test_tavily_limits():
    """اختبار إدارة الحدود"""
    
    print("\n4️⃣ اختبار إدارة الحدود...")
    
    # عرض الإحصائيات الحالية
    stats = tavily_search.get_usage_stats()
    print(f"الاستخدام اليومي: {stats['current_daily_usage']}/{stats['daily_limit']}")
    print(f"الاستخدام الشهري: {stats['current_monthly_usage']}/{stats['monthly_limit']}")
    print(f"المتبقي اليوم: {stats['daily_remaining']}")
    print(f"المتبقي هذا الشهر: {stats['monthly_remaining']}")
    print(f"معدل النجاح: {stats['success_rate']:.1f}%")
    print(f"حجم التخزين المؤقت: {stats['cache_size']}")
    
    # تحذير إذا كان الاستخدام مرتفع
    if stats['current_daily_usage'] > stats['daily_limit'] * 0.8:
        print("⚠️ تحذير: الاستخدام اليومي مرتفع!")
    
    if stats['current_monthly_usage'] > stats['monthly_limit'] * 0.8:
        print("⚠️ تحذير: الاستخدام الشهري مرتفع!")

async def test_tavily_cache():
    """اختبار التخزين المؤقت"""
    
    print("\n5️⃣ اختبار التخزين المؤقت...")
    
    # البحث الأول
    print("البحث الأول...")
    start_time = asyncio.get_event_loop().time()
    cache_test1 = await tavily_search.search("gaming", search_depth="basic", max_results=2)
    first_time = asyncio.get_event_loop().time() - start_time
    
    # البحث الثاني (نفس الاستعلام)
    print("البحث الثاني (نفس الاستعلام)...")
    start_time = asyncio.get_event_loop().time()
    cache_test2 = await tavily_search.search("gaming", search_depth="basic", max_results=2)
    second_time = asyncio.get_event_loop().time() - start_time
    
    print(f"وقت البحث الأول: {first_time:.2f} ثانية")
    print(f"وقت البحث الثاني: {second_time:.2f} ثانية")
    
    if second_time < first_time * 0.5:
        print("✅ التخزين المؤقت يعمل بشكل ممتاز!")
    elif len(cache_test1) == len(cache_test2):
        print("✅ التخزين المؤقت يعمل بشكل صحيح")
    else:
        print("⚠️ قد تكون هناك مشكلة في التخزين المؤقت")

async def test_tavily_gaming_focus():
    """اختبار التركيز على محتوى الألعاب"""
    
    print("\n6️⃣ اختبار التركيز على محتوى الألعاب...")
    
    gaming_queries = [
        "latest gaming news",
        "new game releases",
        "gaming industry updates",
        "esports news"
    ]
    
    for query in gaming_queries:
        print(f"\n🎮 البحث عن: {query}")
        results = await tavily_search.search(query, search_depth="basic", max_results=2)
        
        if results:
            gaming_focused = sum(1 for r in results if r.get('gaming_relevance', 0) >= 2)
            print(f"   النتائج: {len(results)}, المركزة على الألعاب: {gaming_focused}")
            
            # عرض أفضل نتيجة
            best_result = max(results, key=lambda x: x.get('gaming_relevance', 0))
            print(f"   أفضل نتيجة: {best_result.get('title', '')[:50]}...")
            print(f"   صلة الألعاب: {best_result.get('gaming_relevance', 0)}")
        else:
            print(f"   لم يتم العثور على نتائج")
        
        # تأخير قصير بين الطلبات
        await asyncio.sleep(1)

async def main():
    """الدالة الرئيسية للاختبار"""
    
    print("🧪 بدء اختبار شامل لـ Tavily...")
    
    try:
        # الاختبارات الأساسية
        basic_success = await test_tavily_basic()
        if not basic_success:
            return
        
        # اختبار البحث
        search_success = await test_tavily_search()
        
        # اختبار الحدود
        await test_tavily_limits()
        
        # اختبار التخزين المؤقت
        await test_tavily_cache()
        
        # اختبار التركيز على الألعاب
        await test_tavily_gaming_focus()
        
        # النتائج النهائية
        print("\n" + "=" * 50)
        print("✅ اكتمل اختبار Tavily بنجاح!")
        
        # عرض ملخص نهائي
        final_stats = tavily_search.get_usage_stats()
        print(f"\n📊 الملخص النهائي:")
        print(f"   • إجمالي البحثات: {final_stats['total_searches']}")
        print(f"   • معدل النجاح: {final_stats['success_rate']:.1f}%")
        print(f"   • الاستخدام اليومي: {final_stats['current_daily_usage']}/{final_stats['daily_limit']}")
        print(f"   • الاستخدام الشهري: {final_stats['current_monthly_usage']}/{final_stats['monthly_limit']}")
        print(f"   • الحالة: {'مفعل' if final_stats['enabled'] else 'معطل'}")
        
        if search_success:
            print("\n💡 التوصية: Tavily يعمل بشكل ممتاز ويمكن استخدامه كمحرك بحث أساسي!")
        else:
            print("\n⚠️ تحذير: Tavily لا يعمل بشكل صحيح - تحقق من المفاتيح والإعدادات")
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        logger.error(f"خطأ في اختبار Tavily: {e}")

if __name__ == "__main__":
    asyncio.run(main())
