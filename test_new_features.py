#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار مخصص للميزات الجديدة في نظام YouTube
"""

import asyncio
import sys
import os

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.advanced_youtube_analyzer import AdvancedYouTubeAnalyzer
from modules.video_approval_system import VideoApprovalSystem
from modules.database import db

async def test_youtube_channel_extraction():
    """اختبار استخراج معرفات القنوات"""
    print("🔍 اختبار استخراج معرفات القنوات...")
    
    analyzer = AdvancedYouTubeAnalyzer()
    
    print(f"📺 عدد القنوات المحددة: {len(analyzer.priority_channels)}")
    
    for i, channel in enumerate(analyzer.priority_channels, 1):
        print(f"\n{i}. 📺 {channel['name']}")
        print(f"   🔗 URL: {channel['url']}")
        print(f"   🆔 ID: {channel.get('id', 'لم يتم استخراجه بعد')}")
        print(f"   🌐 اللغة: {channel['language']}")
        print(f"   ⭐ الأولوية: {channel['priority']}")
        
        # محاولة استخراج معرف القناة إذا لم يكن موجوداً
        if not channel.get('id') and channel['url'].startswith('https://youtube.com/@'):
            try:
                channel_id = analyzer._extract_channel_id_from_handle(channel['url'])
                if channel_id:
                    print(f"   ✅ تم استخراج المعرف: {channel_id}")
                    channel['id'] = channel_id
                else:
                    print(f"   ❌ فشل في استخراج المعرف")
            except Exception as e:
                print(f"   ⚠️ خطأ في الاستخراج: {e}")

async def test_video_filtering():
    """اختبار فلترة الفيديوهات"""
    print("\n🎯 اختبار فلترة الفيديوهات...")
    
    analyzer = AdvancedYouTubeAnalyzer()
    
    # بيانات فيديوهات وهمية للاختبار
    test_videos = [
        {
            'id': 'video1',
            'title': 'أحدث أخبار الألعاب - PlayStation 5 Pro',
            'description': 'في هذا الفيديو نتحدث عن أحدث إصدارات سوني',
            'published_at': '2025-01-20T10:00:00Z',
            'duration': 900  # 15 دقيقة
        },
        {
            'id': 'video2', 
            'title': 'مراجعة فيلم جديد',
            'description': 'مراجعة فيلم سينمائي جديد',
            'published_at': '2025-01-20T10:00:00Z',
            'duration': 1800  # 30 دقيقة - طويل جداً
        },
        {
            'id': 'video3',
            'title': 'تحديث Fortnite الجديد',
            'description': 'كل ما تحتاج معرفته عن التحديث الجديد',
            'published_at': '2024-10-01T10:00:00Z',  # قديم جداً
            'duration': 600  # 10 دقائق
        },
        {
            'id': 'video4',
            'title': 'أفضل ألعاب 2025',
            'description': 'قائمة بأفضل الألعاب المتوقعة هذا العام',
            'published_at': '2025-01-19T10:00:00Z',
            'duration': 1200  # 20 دقيقة
        }
    ]
    
    print(f"📹 اختبار {len(test_videos)} فيديو...")
    
    for video in test_videos:
        print(f"\n🎬 {video['title']}")
        
        # اختبار مناسبة الفيديو
        is_suitable = await analyzer._is_suitable_gaming_video(video, 'ar')
        
        print(f"   ⏱️ المدة: {video['duration']//60}:{video['duration']%60:02d}")
        print(f"   📅 التاريخ: {video['published_at']}")
        print(f"   🎯 مناسب: {'✅ نعم' if is_suitable else '❌ لا'}")

async def test_text_analysis():
    """اختبار تحليل النصوص"""
    print("\n🧠 اختبار تحليل النصوص...")
    
    analyzer = AdvancedYouTubeAnalyzer()
    
    # نصوص تجريبية
    test_texts = [
        {
            'language': 'ar',
            'text': """
            مرحباً بكم في قناتنا، اليوم سنتحدث عن أحدث أخبار الألعاب.
            أعلنت شركة سوني عن إصدار جديد من PlayStation 5 Pro بمعالج أقوى.
            كما تم الكشف عن لعبة جديدة من استوديو Naughty Dog ستصدر العام القادم.
            هناك تحديث جديد للعبة Fortnite سيصدر الأسبوع القادم مع خريطة جديدة.
            نصيحة للاعبين: تأكدوا من تحديث أجهزتكم قبل تشغيل الألعاب الجديدة.
            يمكنكم أيضاً مراجعة قناتنا للمزيد من المراجعات والنصائح.
            """
        },
        {
            'language': 'en',
            'text': """
            Welcome to our gaming channel! Today we're discussing the latest gaming news.
            Microsoft announced a new Xbox Series X update with enhanced performance.
            A new Call of Duty game has been revealed and will launch next month.
            Nintendo is working on a new Mario game for the Switch platform.
            Remember to subscribe to our channel for more gaming content.
            """
        }
    ]
    
    for i, test_case in enumerate(test_texts, 1):
        print(f"\n📝 نص تجريبي {i} ({test_case['language']}):")
        
        analysis = analyzer.analyze_transcript_for_gaming_news(
            test_case['text'], 
            test_case['language']
        )
        
        print(f"   📊 إجمالي الجمل: {analysis['total_sentences']}")
        print(f"   📰 عدد الأخبار: {analysis['news_count']}")
        print(f"   ℹ️ معلومات إضافية: {analysis['info_count']}")
        
        if analysis['main_news']:
            print("   🔥 أهم الأخبار:")
            for j, news in enumerate(analysis['main_news'][:3], 1):
                print(f"      {j}. {news['text'][:80]}...")
                print(f"         أهمية: {news['importance']}%, مواضيع: {news['topics']}")

async def test_database_operations():
    """اختبار عمليات قاعدة البيانات الجديدة"""
    print("\n💾 اختبار عمليات قاعدة البيانات الجديدة...")
    
    # اختبار حفظ فيديو جديد
    test_video_data = {
        'video_id': 'test_video_new_features',
        'title': 'اختبار الميزات الجديدة',
        'channel_id': 'test_channel_123',
        'channel_name': 'Test Gaming Channel',
        'duration': 900,
        'published_date': '2025-01-20T10:00:00Z',
        'transcript_length': 1500,
        'news_extracted': 5,
        'article_id': None
    }
    
    print("📹 اختبار حفظ بيانات فيديو...")
    
    # فحص إذا كان الفيديو معالج من قبل
    is_processed = db.is_video_processed(test_video_data['video_id'])
    print(f"   🔍 فحص المعالجة السابقة: {'معالج' if is_processed else 'غير معالج'}")
    
    if not is_processed:
        # حفظ بيانات الفيديو
        video_id = db.save_processed_video(test_video_data)
        if video_id:
            print(f"   ✅ تم حفظ الفيديو برقم: {video_id}")
            
            # حفظ النص المستخرج
            transcript_data = {
                'transcript_text': 'نص تجريبي مستخرج من الفيديو...',
                'language': 'ar',
                'main_news_count': 5,
                'additional_info_count': 3
            }
            
            if db.save_video_transcript(test_video_data['video_id'], transcript_data):
                print("   ✅ تم حفظ النص المستخرج")
        else:
            print("   ❌ فشل في حفظ بيانات الفيديو")
    else:
        print("   ⚠️ الفيديو موجود مسبقاً")
    
    # اختبار الإحصائيات
    print("\n📊 اختبار الإحصائيات...")
    
    try:
        stats = db.get_video_processing_stats(7)
        if stats:
            print(f"   📹 فيديوهات معالجة (7 أيام): {stats['total_videos_processed']}")
            print(f"   📰 أخبار مستخرجة: {stats['total_news_extracted']}")
            print(f"   📺 قنوات فريدة: {stats['unique_channels']}")
            print(f"   ✅ معدل الموافقة: {stats['approval_rate']:.1f}%")
        else:
            print("   📊 لا توجد إحصائيات متاحة")
    except Exception as e:
        print(f"   ❌ خطأ في الإحصائيات: {e}")
    
    # اختبار أداء القنوات
    try:
        channels = db.get_channel_performance(30)
        if channels:
            print(f"\n📺 أداء القنوات ({len(channels)} قناة):")
            for channel in channels[:3]:  # أول 3 قنوات
                print(f"   • {channel['channel_name']}: {channel['efficiency']:.1f}% كفاءة")
        else:
            print("   📺 لا توجد بيانات أداء القنوات")
    except Exception as e:
        print(f"   ❌ خطأ في أداء القنوات: {e}")

async def test_approval_system():
    """اختبار نظام الموافقة"""
    print("\n📱 اختبار نظام الموافقة...")
    
    approval_system = VideoApprovalSystem()
    
    # بيانات فيديو وهمية
    mock_video = {
        'id': 'approval_test_video',
        'title': 'اختبار نظام الموافقة - أخبار الألعاب',
        'description': 'فيديو تجريبي لاختبار نظام الموافقة الجديد',
        'published_at': '2025-01-20T10:00:00Z',
        'duration': 900,
        'channel_info': {
            'name': 'Test Gaming Channel',
            'language': 'ar',
            'id': 'test_channel_approval'
        }
    }
    
    print(f"🎬 اختبار موافقة الفيديو: {mock_video['title']}")
    
    # متغير لحفظ نتيجة الموافقة
    approval_result = {'approved': False, 'reason': 'في انتظار الاختبار'}
    
    async def test_callback(approved: bool, reason: str):
        approval_result['approved'] = approved
        approval_result['reason'] = reason
        print(f"   📝 نتيجة الموافقة: {'موافق' if approved else 'مرفوض'}")
        print(f"   📄 السبب: {reason}")
    
    try:
        # إرسال طلب الموافقة
        approval_id = await approval_system.request_video_approval(mock_video, test_callback)
        print(f"   🆔 معرف الموافقة: {approval_id}")
        
        # في الاختبار، سيتم الموافقة تلقائياً
        await asyncio.sleep(2)
        
        # عرض الإحصائيات
        pending_count = approval_system.get_pending_approvals_count()
        print(f"   ⏳ موافقات معلقة: {pending_count}")
        
    except Exception as e:
        print(f"   ❌ خطأ في نظام الموافقة: {e}")

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 اختبار شامل للميزات الجديدة في نظام YouTube")
    print("=" * 70)
    
    try:
        # اختبار استخراج معرفات القنوات
        await test_youtube_channel_extraction()
        
        # اختبار فلترة الفيديوهات
        await test_video_filtering()
        
        # اختبار تحليل النصوص
        await test_text_analysis()
        
        # اختبار قاعدة البيانات
        await test_database_operations()
        
        # اختبار نظام الموافقة
        await test_approval_system()
        
        print("\n" + "=" * 70)
        print("✅ اكتمل اختبار جميع الميزات الجديدة بنجاح!")
        print("🎉 النظام جاهز للاستخدام الكامل")
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
