# 🧠 نظام البحث الذكي المحسن

## نظرة عامة

تم تطوير نظام بحث ذكي ومتقدم يهدف إلى تحسين كفاءة استهلاك APIs وتحسين جودة النتائج مع توفير آليات تخزين مؤقت متقدمة وإدارة ذكية لمعدل الطلبات.

## ✨ الميزات الرئيسية

### 🎯 البحث الذكي
- **بحث تدريجي**: يبدأ بالمصادر المجانية ثم ينتقل للمدفوعة حسب الحاجة
- **أولويات ذكية**: تحديد تلقائي للأولوية بناءً على نوع الاستعلام
- **تكامل متعدد المحركات**: دعم Tavily، SerpAPI، Google Search، والمزيد
- **إعادة المحاولة التلقائية**: نظام ذكي لإعادة المحاولة عند الفشل

### 💾 التخزين المؤقت المتقدم
- **ضغط البيانات**: ضغط تلقائي للبيانات الكبيرة
- **تنظيف ذكي**: إزالة تلقائية للبيانات القديمة وغير المستخدمة
- **فهرسة محسنة**: أداء عالي للبحث والاسترجاع
- **إحصائيات مفصلة**: تتبع معدل النجاح والاستخدام

### ⏱️ إدارة معدل الطلبات
- **قواعد مرنة**: إعدادات مخصصة لكل خدمة API
- **تتبع في الوقت الفعلي**: مراقبة الاستخدام لحظة بلحظة
- **حساب التكلفة**: تتبع دقيق للتكاليف
- **تنبيهات ذكية**: إشعارات عند اقتراب الحدود

### 📊 التحليلات المتقدمة
- **مقاييس الأداء**: تحليل شامل لأداء البحث
- **تحليل الاتجاهات**: فهم أنماط الاستخدام
- **تنبيهات تلقائية**: إشعارات للمشاكل والتحسينات
- **تقارير مفصلة**: بيانات شاملة للتحليل

## 🚀 التثبيت والإعداد

### المتطلبات
```bash
pip install aiohttp sqlite3 asyncio dataclasses
```

### الملفات المطلوبة
```
modules/
├── smart_search_manager.py          # مدير البحث الذكي
├── advanced_cache_system.py         # نظام التخزين المؤقت
├── rate_limit_manager.py            # مدير معدل الطلبات
├── search_analytics.py              # نظام التحليلات
└── enhanced_search_integration.py   # التكامل الشامل
```

### الإعداد السريع
```python
from modules.enhanced_search_integration import enhanced_search

# بحث بسيط
results = await enhanced_search.enhanced_search("gaming news today")
```

## 📖 دليل الاستخدام

### البحث الأساسي
```python
# بحث بسيط مع إعدادات افتراضية
results = await enhanced_search.enhanced_search(
    query="gaming news today",
    max_results=10
)

# بحث مع أولوية محددة
results = await enhanced_search.enhanced_search(
    query="breaking gaming news",
    max_results=5,
    priority="premium"  # free, low_cost, premium, emergency
)

# بحث مع نوع محدد
results = await enhanced_search.enhanced_search(
    query="new game releases",
    max_results=8,
    search_type="gaming_news"  # gaming_news, general, trending
)
```

### إدارة التخزين المؤقت
```python
from modules.advanced_cache_system import advanced_cache

# حفظ بيانات
advanced_cache.set("my_key", {"data": "value"}, ttl=3600)

# استرجاع بيانات
data = advanced_cache.get("my_key")

# الحصول على إحصائيات
stats = advanced_cache.get_stats()
print(f"معدل النجاح: {stats['hit_rate']}%")
```

### مراقبة معدل الطلبات
```python
from modules.rate_limit_manager import rate_limit_manager

# فحص إمكانية إجراء طلب
can_request, message, wait_time = await rate_limit_manager.can_make_request("serpapi")

if can_request:
    # إجراء الطلب
    pass
else:
    print(f"انتظار {wait_time} ثانية")
```

### مراقبة الأداء
```python
from modules.search_analytics import search_analytics

# مقاييس الأداء
metrics = search_analytics.get_performance_metrics(24)  # آخر 24 ساعة
print(f"متوسط وقت الاستجابة: {metrics.avg_response_time}s")
print(f"معدل نجاح التخزين المؤقت: {metrics.cache_hit_rate}%")
print(f"التكلفة الإجمالية: ${metrics.total_cost}")

# تحليل الاتجاهات
trends = search_analytics.get_trend_analysis(7)  # آخر 7 أيام
```

## 🧪 الاختبار

### اختبار سريع
```bash
python quick_test_enhanced_system.py
```

### اختبار شامل
```bash
python test_enhanced_search_system.py
```

### اختبار التكامل
```bash
python enhanced_main_integration.py
```

## ⚙️ التكوين

### إعدادات البحث
```python
# في smart_search_manager.py
rate_limits = {
    'serpapi': {'calls_per_minute': 20, 'daily_limit': 1000},
    'tavily': {'calls_per_minute': 10, 'daily_limit': 500},
    'google': {'calls_per_minute': 100, 'daily_limit': 10000}
}
```

### إعدادات التخزين المؤقت
```python
# في advanced_cache_system.py
settings = {
    'max_entries': 50000,
    'default_ttl': 3600,
    'cleanup_interval': 1800,
    'max_memory_usage': 100 * 1024 * 1024
}
```

### إعدادات التحليلات
```python
# في search_analytics.py
settings = {
    'retention_days': 90,
    'alert_thresholds': {
        'response_time': 10.0,
        'success_rate': 80.0,
        'cost_per_day': 5.0
    }
}
```

## 📊 مراقبة النظام

### لوحة المراقبة
```python
# حالة النظام الشاملة
status = enhanced_search.get_system_status()

# إحصائيات التخزين المؤقت
cache_stats = advanced_cache.get_stats()

# إحصائيات معدل الطلبات
rate_stats = rate_limit_manager.get_overall_stats()

# مقاييس الأداء
performance = search_analytics.get_performance_metrics(24)
```

### التنبيهات
```python
# الحصول على التنبيهات الحديثة
alerts = search_analytics.get_alerts(24, severity="error")

for alert in alerts:
    print(f"⚠️ {alert['message']} ({alert['datetime']})")
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة

#### بطء في البحث
```python
# فحص إعدادات التخزين المؤقت
cache_stats = advanced_cache.get_stats()
if cache_stats['hit_rate'] < 50:
    print("معدل نجاح التخزين المؤقت منخفض")

# فحص أداء محركات البحث
metrics = search_analytics.get_performance_metrics(1)
for engine, perf in metrics.engine_performance.items():
    if perf['avg_response_time'] > 5:
        print(f"محرك {engine} بطيء: {perf['avg_response_time']}s")
```

#### تجاوز حدود APIs
```python
# فحص الاستخدام الحالي
for service in ['serpapi', 'tavily', 'google']:
    stats = rate_limit_manager.get_service_stats(service)
    print(f"{service}: {stats['total_requests_24h']} طلب في 24 ساعة")
```

#### جودة نتائج منخفضة
```python
# تحليل جودة النتائج
metrics = search_analytics.get_performance_metrics(24)
if metrics.avg_quality_score < 60:
    print("جودة النتائج منخفضة - راجع معايير الترتيب")
```

## 📈 التحسينات المستقبلية

### المخطط لها
- [ ] تعلم آلي لتحسين الترتيب
- [ ] تحليل المشاعر للنتائج
- [ ] تكامل مع مصادر جديدة
- [ ] واجهة ويب للمراقبة
- [ ] API RESTful للتكامل الخارجي

### اقتراحات التحسين
- تحسين خوارزميات الترتيب
- إضافة المزيد من مصادر البيانات
- تطوير نظام التعلم التكيفي
- تحسين واجهة المستخدم

## 🤝 المساهمة

### إرشادات المساهمة
1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. كتابة اختبارات للكود الجديد
4. التأكد من نجاح جميع الاختبارات
5. إرسال Pull Request

### معايير الكود
- استخدام Type Hints
- توثيق شامل للدوال
- اختبارات وحدة شاملة
- اتباع PEP 8

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## 📞 الدعم

للحصول على المساعدة:
1. راجع هذا الدليل
2. شغل الاختبارات التشخيصية
3. فحص السجلات في `logs/`
4. إنشاء Issue في GitHub

---

**تم تطوير هذا النظام لتحسين كفاءة البحث وتقليل التكاليف مع الحفاظ على جودة عالية للنتائج.**
