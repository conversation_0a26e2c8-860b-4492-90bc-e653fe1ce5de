#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام البحث التاريخي والمحتوى الرائج
"""

import asyncio
import sys
import os
from datetime import datetime

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.web_search import WebSearch
from modules.content_extractor import ContentExtractor
from modules.content_generator import ContentGenerator

class HistoricalSearchTester:
    """فئة اختبار البحث التاريخي"""
    
    def __init__(self):
        self.web_search = WebSearch()
        self.content_extractor = ContentExtractor()
        self.content_generator = ContentGenerator()
        self.test_results = {}
        self.start_time = datetime.now()
    
    async def run_historical_search_tests(self):
        """تشغيل اختبار البحث التاريخي"""
        print("🚀 بدء اختبار نظام البحث التاريخي والمحتوى الرائج...\n")
        
        tests = [
            ("Historical Content Search", self.test_historical_search),
            ("Trending Content Generation", self.test_trending_content),
            ("Deep Sources Search", self.test_deep_sources),
            ("Content Quality Analysis", self.test_content_quality),
            ("Duplicate Detection", self.test_duplicate_detection)
        ]
        
        for test_name, test_func in tests:
            try:
                print(f"🔍 اختبار {test_name}...")
                result = await test_func()
                self.test_results[test_name] = result
                
                if result['success']:
                    print(f"✅ {test_name}: نجح")
                    if 'details' in result:
                        for key, value in result['details'].items():
                            print(f"   • {key}: {value}")
                else:
                    print(f"❌ {test_name}: فشل - {result.get('error', 'خطأ غير محدد')}")
                
                print()
                
            except Exception as e:
                print(f"❌ {test_name}: خطأ في الاختبار - {e}\n")
                self.test_results[test_name] = {'success': False, 'error': str(e)}
        
        await self.generate_test_report()
    
    async def test_historical_search(self):
        """اختبار البحث التاريخي"""
        try:
            historical_content = []
            
            # كلمات بحث تاريخية للاختبار
            test_queries = [
                "gaming news this week",
                "video game updates last week", 
                "new game releases this month"
            ]
            
            for query in test_queries:
                try:
                    search_results = await self.web_search.search_web(query)
                    
                    if search_results:
                        for result in search_results[:2]:  # أفضل 2 نتائج لكل استعلام
                            try:
                                content = await self.content_extractor.extract_content(result['url'])
                                if content and len(content.get('content', '')) > 300:
                                    content['source_type'] = 'historical_search'
                                    content['search_query'] = query
                                    historical_content.append(content)
                                    
                            except Exception as e:
                                continue
                    
                    await asyncio.sleep(1)  # تأخير بين الطلبات
                    
                except Exception as e:
                    continue
            
            # إزالة المحتوى المكرر
            unique_content = []
            seen_titles = set()
            
            for content in historical_content:
                title = content.get('title', '').lower()
                if title and title not in seen_titles and len(title) > 10:
                    seen_titles.add(title)
                    unique_content.append(content)
            
            return {
                'success': len(unique_content) > 0,
                'message': 'البحث التاريخي يعمل بنجاح',
                'details': {
                    'استعلامات مختبرة': len(test_queries),
                    'محتوى مجمع': len(historical_content),
                    'محتوى فريد': len(unique_content),
                    'معدل النجاح': f"{(len(unique_content)/max(1, len(test_queries)))*100:.1f}%",
                    'متوسط طول المحتوى': f"{sum(len(c.get('content', '')) for c in unique_content) // max(1, len(unique_content))} حرف" if unique_content else "0 حرف"
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_trending_content(self):
        """اختبار إنشاء المحتوى الرائج"""
        try:
            trending_content = []
            
            # مواضيع رائجة للاختبار
            test_topics = [
                {
                    'topic': 'أفضل ألعاب 2025 المنتظرة',
                    'keywords': ['gaming 2025', 'upcoming games'],
                    'content_type': 'list_article'
                },
                {
                    'topic': 'نصائح الألعاب للمبتدئين',
                    'keywords': ['gaming tips', 'beginner guide'],
                    'content_type': 'guide'
                }
            ]
            
            for topic_info in test_topics:
                try:
                    prompt = f"""
                    أنشئ مقالاً قصيراً حول "{topic_info['topic']}" للاعبين العرب.
                    
                    المتطلبات:
                    - العنوان جذاب
                    - المحتوى 300-500 كلمة
                    - أسلوب شيق
                    - استخدام الكلمات المفتاحية: {', '.join(topic_info['keywords'])}
                    """
                    
                    generated_content = await self.content_generator.generate_content(prompt)
                    
                    if generated_content:
                        trending_article = {
                            'title': generated_content.get('title', topic_info['topic']),
                            'content': generated_content.get('content', ''),
                            'source_type': 'trending_generated',
                            'keywords': topic_info['keywords'],
                            'content_type': topic_info['content_type']
                        }
                        
                        trending_content.append(trending_article)
                    
                except Exception as e:
                    continue
            
            return {
                'success': len(trending_content) > 0,
                'message': 'إنشاء المحتوى الرائج يعمل بنجاح',
                'details': {
                    'مواضيع مختبرة': len(test_topics),
                    'محتوى مُولد': len(trending_content),
                    'معدل النجاح': f"{(len(trending_content)/len(test_topics))*100:.1f}%",
                    'متوسط طول المحتوى': f"{sum(len(c.get('content', '')) for c in trending_content) // max(1, len(trending_content))} حرف" if trending_content else "0 حرف",
                    'أول عنوان مُولد': trending_content[0]['title'][:50] + "..." if trending_content else "لا يوجد"
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_deep_sources(self):
        """اختبار البحث في المصادر العميقة"""
        try:
            deeper_content = []
            
            # مصادر عميقة للاختبار
            test_sources = [
                "https://venturebeat.com/games",
                "https://techcrunch.com/category/gaming",
                "https://www.theverge.com/games"
            ]
            
            for source_url in test_sources[:2]:  # اختبار أول 2 مصادر
                try:
                    extracted_articles = await self.content_extractor.extract_articles_from_site(source_url)
                    
                    if extracted_articles:
                        for article in extracted_articles[:2]:  # أفضل 2 مقالات
                            if len(article.get('content', '')) > 200:
                                article['source_type'] = 'deep_source'
                                deeper_content.append(article)
                    
                    await asyncio.sleep(2)  # تأخير بين المصادر
                    
                except Exception as e:
                    continue
            
            # إزالة المحتوى المكرر
            unique_deeper = []
            seen_urls = set()
            
            for content in deeper_content:
                url = content.get('url', '')
                if url and url not in seen_urls:
                    seen_urls.add(url)
                    unique_deeper.append(content)
            
            return {
                'success': len(unique_deeper) > 0,
                'message': 'البحث في المصادر العميقة يعمل',
                'details': {
                    'مصادر مختبرة': len(test_sources[:2]),
                    'محتوى مستخرج': len(deeper_content),
                    'محتوى فريد': len(unique_deeper),
                    'معدل النجاح': f"{(len(unique_deeper)/len(test_sources[:2]))*100:.1f}%",
                    'متوسط طول المحتوى': f"{sum(len(c.get('content', '')) for c in unique_deeper) // max(1, len(unique_deeper))} حرف" if unique_deeper else "0 حرف"
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_content_quality(self):
        """اختبار جودة المحتوى"""
        try:
            # اختبار جودة المحتوى المُولد
            test_prompt = """
            أنشئ مقالاً قصيراً عن "أحدث ألعاب الهواتف الذكية" للاعبين العرب.
            المتطلبات: عنوان جذاب، محتوى 200-300 كلمة، أسلوب شيق.
            """
            
            generated_content = await self.content_generator.generate_content(test_prompt)
            
            if generated_content:
                title = generated_content.get('title', '')
                content = generated_content.get('content', '')
                
                # تحليل جودة المحتوى
                quality_score = 0
                quality_factors = []
                
                # فحص العنوان
                if title and len(title) > 10:
                    quality_score += 25
                    quality_factors.append('عنوان مناسب')
                
                # فحص طول المحتوى
                if len(content) >= 200:
                    quality_score += 25
                    quality_factors.append('طول مناسب')
                
                # فحص وجود كلمات عربية
                if any(ord(char) >= 0x0600 and ord(char) <= 0x06FF for char in content):
                    quality_score += 25
                    quality_factors.append('محتوى عربي')
                
                # فحص التنوع في المحتوى
                if len(set(content.split())) > 50:
                    quality_score += 25
                    quality_factors.append('تنوع في المفردات')
                
                return {
                    'success': quality_score >= 50,
                    'message': 'تحليل جودة المحتوى مكتمل',
                    'details': {
                        'نقاط الجودة': f"{quality_score}/100",
                        'عوامل الجودة': quality_factors,
                        'طول العنوان': len(title),
                        'طول المحتوى': len(content),
                        'عدد الكلمات': len(content.split()),
                        'تقييم عام': 'ممتاز' if quality_score >= 75 else 'جيد' if quality_score >= 50 else 'يحتاج تحسين'
                    }
                }
            else:
                return {'success': False, 'error': 'فشل في توليد المحتوى'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_duplicate_detection(self):
        """اختبار كشف المحتوى المكرر"""
        try:
            # إنشاء محتوى اختبار مع تكرار
            test_content = [
                {'title': 'أخبار الألعاب اليوم', 'url': 'test1.com'},
                {'title': 'أخبار الألعاب اليوم', 'url': 'test2.com'},  # مكرر
                {'title': 'مراجعة لعبة جديدة', 'url': 'test3.com'},
                {'title': 'نصائح الألعاب', 'url': 'test4.com'},
                {'title': 'نصائح الألعاب', 'url': 'test5.com'}  # مكرر
            ]
            
            # إزالة المحتوى المكرر
            unique_content = []
            seen_titles = set()
            seen_urls = set()
            
            for content in test_content:
                title = content.get('title', '').lower()
                url = content.get('url', '')
                
                if title not in seen_titles and url not in seen_urls:
                    seen_titles.add(title)
                    seen_urls.add(url)
                    unique_content.append(content)
            
            duplicates_removed = len(test_content) - len(unique_content)
            
            return {
                'success': duplicates_removed > 0,
                'message': 'كشف المحتوى المكرر يعمل بنجاح',
                'details': {
                    'محتوى أصلي': len(test_content),
                    'محتوى فريد': len(unique_content),
                    'مكررات مُزالة': duplicates_removed,
                    'معدل الكشف': f"{(duplicates_removed/len(test_content))*100:.1f}%",
                    'كفاءة التصفية': 'ممتازة' if duplicates_removed >= 2 else 'جيدة'
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def generate_test_report(self):
        """إنشاء تقرير الاختبار"""
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results.values() if result.get('success', False))
        success_rate = (successful_tests / total_tests) * 100 if total_tests > 0 else 0
        
        duration = (datetime.now() - self.start_time).total_seconds()
        
        print("\n" + "="*80)
        print("📊 تقرير اختبار نظام البحث التاريخي والمحتوى الرائج")
        print("="*80)
        
        print(f"🎯 معدل النجاح الإجمالي: {success_rate:.1f}% ({successful_tests}/{total_tests})")
        print(f"⏱️ مدة الاختبار: {duration:.1f} ثانية")
        print(f"📅 تاريخ الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        print("\n📋 تفاصيل النتائج:")
        for test_name, result in self.test_results.items():
            status = "✅ نجح" if result.get('success', False) else "❌ فشل"
            print(f"  • {test_name}: {status}")
            
            if result.get('success') and 'details' in result:
                for key, value in result['details'].items():
                    print(f"    - {key}: {value}")
        
        # تقييم عام
        if success_rate >= 90:
            grade = "A+ ممتاز"
            message = "🎉 نظام البحث التاريخي يعمل بكفاءة استثنائية!"
        elif success_rate >= 80:
            grade = "A جيد جداً"
            message = "👍 النظام يعمل بشكل ممتاز مع إمكانيات تحسين بسيطة"
        elif success_rate >= 70:
            grade = "B جيد"
            message = "⚠️ النظام يعمل بشكل جيد مع حاجة لتحسينات"
        else:
            grade = "C يحتاج تطوير"
            message = "🔧 يحتاج إلى مراجعة وتحسين النظام"
        
        print(f"\n🏆 التقييم العام: {grade}")
        print(f"💬 التوصية: {message}")
        
        # توصيات التحسين
        print(f"\n🚀 التوصيات:")
        if success_rate >= 80:
            print("  • النظام جاهز للاستخدام في الإنتاج")
            print("  • يمكن تفعيل البحث التاريخي كحل احتياطي")
            print("  • إضافة المزيد من المصادر العميقة")
        else:
            print("  • مراجعة خوارزميات البحث التاريخي")
            print("  • تحسين جودة المحتوى المُولد")
            print("  • تطوير آليات كشف المحتوى المكرر")
        
        print("  • مراقبة أداء النظام في البيئة الحقيقية")
        print("  • إضافة المزيد من الكلمات المفتاحية التاريخية")
        print("  • تطوير نظام تقييم جودة المحتوى التلقائي")

async def main():
    """الدالة الرئيسية"""
    tester = HistoricalSearchTester()
    await tester.run_historical_search_tests()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⌨️ تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
