# Use an official Python runtime
FROM python:3.9-slim

# Install ffmpeg
RUN apt-get update && apt-get install -y ffmpeg

# Set the working directory
WORKDIR /app

# Copy and install requirements
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy the application file
COPY api_only.py .

# Expose the port and run the application
EXPOSE 7860
CMD ["uvicorn", "api_only:app", "--host", "0.0.0.0", "--port", "7860"]
