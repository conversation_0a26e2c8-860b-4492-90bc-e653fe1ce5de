# مدير الصور المحسن مع حد أقصى للصور
import asyncio
import time
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import json
import os

from .logger import logger
from .smart_image_manager import smart_image_manager
from .advanced_cache_system import advanced_cache

@dataclass
class ImageGenerationRequest:
    """طلب توليد صورة"""
    prompt: str
    article_title: str
    image_type: str  # main, secondary, thumbnail
    priority: int = 1
    style: str = "gaming"

@dataclass
class ImageGenerationResult:
    """نتيجة توليد صورة"""
    url: str
    prompt: str
    image_type: str
    generation_time: float
    cost: float
    quality_score: float
    cached: bool = False

class EnhancedImageManager:
    """مدير الصور المحسن مع حد أقصى للصور"""
    
    def __init__(self):
        # إعدادات الصور
        self.settings = {
            'max_images_per_article': 3,      # حد أقصى 3 صور لكل مقال
            'image_types': ['main', 'secondary', 'thumbnail'],
            'max_daily_generations': 50,      # حد أقصى يومي
            'cache_duration': 86400,          # 24 ساعة
            'quality_threshold': 60.0,        # حد أدنى للجودة
            'cost_per_image': 0.02,          # تكلفة تقديرية لكل صورة
        }
        
        # تتبع الاستخدام اليومي
        self.daily_usage = {
            'date': datetime.now().date(),
            'images_generated': 0,
            'total_cost': 0.0,
            'cache_hits': 0,
            'cache_misses': 0
        }
        
        # قوالب الصور حسب النوع
        self.image_templates = {
            'main': {
                'style': 'high-quality gaming artwork',
                'resolution': 'high',
                'priority': 3
            },
            'secondary': {
                'style': 'gaming screenshot style',
                'resolution': 'medium',
                'priority': 2
            },
            'thumbnail': {
                'style': 'simple gaming icon',
                'resolution': 'small',
                'priority': 1
            }
        }
        
        logger.info("🎨 تم تهيئة مدير الصور المحسن (حد أقصى 3 صور)")
    
    async def generate_images_for_article(self, article: Dict) -> Dict:
        """توليد الصور للمقال مع حد أقصى 3 صور"""
        try:
            article_title = article.get('title', '')
            logger.info(f"🎨 بدء توليد الصور للمقال: {article_title[:50]}...")
            
            # فحص الحد اليومي
            if not self._check_daily_limit():
                logger.warning("⚠️ تم تجاوز الحد اليومي لتوليد الصور")
                return self._get_fallback_images(article)
            
            # تحديد أنواع الصور المطلوبة
            required_images = self._determine_required_images(article)
            
            # توليد الصور
            generated_images = []
            total_cost = 0.0
            
            for image_request in required_images:
                try:
                    # فحص التخزين المؤقت أولاً
                    cached_image = await self._get_cached_image(image_request)
                    
                    if cached_image:
                        generated_images.append(cached_image)
                        self.daily_usage['cache_hits'] += 1
                        logger.info(f"📦 استخدام صورة مخزنة مؤقتاً: {image_request.image_type}")
                    else:
                        # توليد صورة جديدة
                        new_image = await self._generate_single_image(image_request)
                        
                        if new_image:
                            generated_images.append(new_image)
                            total_cost += new_image.cost
                            self.daily_usage['images_generated'] += 1
                            self.daily_usage['cache_misses'] += 1
                            
                            # حفظ في التخزين المؤقت
                            await self._cache_image(image_request, new_image)
                            
                            logger.info(f"✅ تم توليد صورة جديدة: {image_request.image_type}")
                        else:
                            logger.warning(f"⚠️ فشل في توليد صورة: {image_request.image_type}")
                    
                    # تأخير بين توليد الصور
                    await asyncio.sleep(2)
                    
                except Exception as e:
                    logger.warning(f"⚠️ فشل في توليد صورة {image_request.image_type}: {e}")
                    continue
            
            # تحديث التكلفة الإجمالية
            self.daily_usage['total_cost'] += total_cost
            
            # تنظيم النتائج
            result = self._organize_image_results(generated_images, article)
            
            logger.info(f"🎨 تم توليد {len(generated_images)} صورة للمقال (تكلفة: ${total_cost:.3f})")
            return result
            
        except Exception as e:
            logger.error(f"❌ فشل في توليد الصور للمقال: {e}")
            return self._get_fallback_images(article)
    
    def _determine_required_images(self, article: Dict) -> List[ImageGenerationRequest]:
        """تحديد الصور المطلوبة للمقال"""
        try:
            article_title = article.get('title', '')
            content = article.get('content', '')
            importance_score = article.get('importance_score', 0)
            
            required_images = []
            
            # الصورة الرئيسية (دائماً مطلوبة)
            main_prompt = self._create_main_image_prompt(article_title, content)
            required_images.append(ImageGenerationRequest(
                prompt=main_prompt,
                article_title=article_title,
                image_type='main',
                priority=3,
                style='gaming_artwork'
            ))
            
            # الصورة الثانوية (للمقالات المهمة)
            if importance_score >= 70 or len(content) > 1000:
                secondary_prompt = self._create_secondary_image_prompt(article_title, content)
                required_images.append(ImageGenerationRequest(
                    prompt=secondary_prompt,
                    article_title=article_title,
                    image_type='secondary',
                    priority=2,
                    style='gaming_screenshot'
                ))
            
            # الصورة المصغرة (للمقالات الطويلة أو المهمة جداً)
            if importance_score >= 85 or len(content) > 1500:
                thumbnail_prompt = self._create_thumbnail_prompt(article_title)
                required_images.append(ImageGenerationRequest(
                    prompt=thumbnail_prompt,
                    article_title=article_title,
                    image_type='thumbnail',
                    priority=1,
                    style='gaming_icon'
                ))
            
            # التأكد من عدم تجاوز الحد الأقصى
            return required_images[:self.settings['max_images_per_article']]
            
        except Exception as e:
            logger.error(f"❌ فشل في تحديد الصور المطلوبة: {e}")
            return []
    
    def _create_main_image_prompt(self, title: str, content: str) -> str:
        """إنشاء prompt للصورة الرئيسية"""
        try:
            # استخراج الكلمات المفتاحية من العنوان
            keywords = self._extract_visual_keywords(title, content)
            
            # إنشاء prompt محسن
            base_prompt = f"High-quality gaming artwork featuring {', '.join(keywords[:3])}"
            
            # إضافة تفاصيل بصرية
            visual_elements = [
                "detailed digital art",
                "vibrant colors",
                "gaming atmosphere",
                "professional quality",
                "4K resolution"
            ]
            
            prompt = f"{base_prompt}, {', '.join(visual_elements)}"
            
            return prompt[:200]  # حد أقصى للطول
            
        except Exception as e:
            logger.debug(f"فشل في إنشاء prompt الصورة الرئيسية: {e}")
            return f"Gaming artwork for {title[:50]}"
    
    def _create_secondary_image_prompt(self, title: str, content: str) -> str:
        """إنشاء prompt للصورة الثانوية"""
        try:
            keywords = self._extract_visual_keywords(title, content)
            
            # prompt أكثر تخصصاً للصورة الثانوية
            base_prompt = f"Gaming screenshot style image showing {', '.join(keywords[:2])}"
            
            style_elements = [
                "in-game perspective",
                "realistic gaming graphics",
                "action scene",
                "detailed environment"
            ]
            
            prompt = f"{base_prompt}, {', '.join(style_elements)}"
            
            return prompt[:200]
            
        except Exception as e:
            logger.debug(f"فشل في إنشاء prompt الصورة الثانوية: {e}")
            return f"Gaming scene for {title[:50]}"
    
    def _create_thumbnail_prompt(self, title: str) -> str:
        """إنشاء prompt للصورة المصغرة"""
        try:
            # استخراج الكلمة المفتاحية الرئيسية
            main_keyword = self._extract_main_keyword(title)
            
            prompt = f"Simple gaming icon representing {main_keyword}, minimalist design, clear symbol, gaming style"
            
            return prompt[:150]
            
        except Exception as e:
            logger.debug(f"فشل في إنشاء prompt الصورة المصغرة: {e}")
            return f"Gaming icon for {title[:30]}"
    
    def _extract_visual_keywords(self, title: str, content: str) -> List[str]:
        """استخراج الكلمات المفتاحية البصرية"""
        try:
            import re
            
            text = f"{title} {content}".lower()
            
            # كلمات مفتاحية بصرية للألعاب
            visual_keywords = []
            
            # أنواع الألعاب
            game_types = re.findall(r'\b(?:rpg|fps|mmorpg|strategy|racing|sports|puzzle|adventure|action|simulation)\b', text)
            visual_keywords.extend(game_types)
            
            # عناصر بصرية
            visual_elements = re.findall(r'\b(?:character|weapon|vehicle|building|landscape|creature|robot|spaceship|castle|forest)\b', text)
            visual_keywords.extend(visual_elements)
            
            # أسماء الألعاب والشخصيات
            proper_nouns = re.findall(r'\b[A-Z][a-zA-Z]{2,15}\b', title)
            visual_keywords.extend([noun.lower() for noun in proper_nouns])
            
            # تنظيف وإزالة التكرار
            unique_keywords = []
            for keyword in visual_keywords:
                if keyword not in unique_keywords and len(keyword) > 2:
                    unique_keywords.append(keyword)
            
            return unique_keywords[:5]  # أفضل 5 كلمات
            
        except Exception as e:
            logger.debug(f"فشل في استخراج الكلمات المفتاحية البصرية: {e}")
            return ['gaming', 'video game']
    
    def _extract_main_keyword(self, title: str) -> str:
        """استخراج الكلمة المفتاحية الرئيسية"""
        try:
            import re
            
            # البحث عن أسماء الألعاب (عادة بأحرف كبيرة)
            game_names = re.findall(r'\b[A-Z][a-zA-Z\s]{2,20}\b', title)
            if game_names:
                return game_names[0].strip()
            
            # البحث عن كلمات مفتاحية مهمة
            important_words = re.findall(r'\b(?:game|gaming|update|release|trailer|review|news)\b', title.lower())
            if important_words:
                return important_words[0]
            
            # العودة لأول كلمة مهمة
            words = title.split()
            for word in words:
                if len(word) > 3:
                    return word.lower()
            
            return 'gaming'
            
        except Exception as e:
            logger.debug(f"فشل في استخراج الكلمة المفتاحية الرئيسية: {e}")
            return 'gaming'
    
    async def _get_cached_image(self, request: ImageGenerationRequest) -> Optional[ImageGenerationResult]:
        """الحصول على صورة مخزنة مؤقتاً"""
        try:
            # إنشاء مفتاح التخزين المؤقت
            cache_key = self._generate_image_cache_key(request)
            
            # البحث في التخزين المؤقت
            cached_data = advanced_cache.get(cache_key)
            
            if cached_data:
                return ImageGenerationResult(
                    url=cached_data['url'],
                    prompt=cached_data['prompt'],
                    image_type=cached_data['image_type'],
                    generation_time=0.0,
                    cost=0.0,
                    quality_score=cached_data.get('quality_score', 80.0),
                    cached=True
                )
            
            return None
            
        except Exception as e:
            logger.debug(f"فشل في الحصول على صورة مخزنة: {e}")
            return None
    
    async def _generate_single_image(self, request: ImageGenerationRequest) -> Optional[ImageGenerationResult]:
        """توليد صورة واحدة"""
        try:
            start_time = time.time()
            
            # استخدام النظام الذكي الموجود
            image_data = await smart_image_manager.generate_smart_image_for_article({
                'title': request.article_title,
                'image_prompts': [request.prompt],
                'content_type': request.style
            })
            
            if image_data:
                generation_time = time.time() - start_time
                
                return ImageGenerationResult(
                    url=image_data['url'],
                    prompt=request.prompt,
                    image_type=request.image_type,
                    generation_time=generation_time,
                    cost=self.settings['cost_per_image'],
                    quality_score=image_data.get('quality_score', 75.0),
                    cached=False
                )
            
            return None
            
        except Exception as e:
            logger.error(f"❌ فشل في توليد صورة واحدة: {e}")
            return None
    
    async def _cache_image(self, request: ImageGenerationRequest, result: ImageGenerationResult):
        """حفظ الصورة في التخزين المؤقت"""
        try:
            cache_key = self._generate_image_cache_key(request)
            
            cache_data = {
                'url': result.url,
                'prompt': result.prompt,
                'image_type': result.image_type,
                'quality_score': result.quality_score,
                'generated_at': datetime.now().isoformat()
            }
            
            # حفظ لمدة 24 ساعة
            advanced_cache.set(cache_key, cache_data, ttl=self.settings['cache_duration'])
            
        except Exception as e:
            logger.debug(f"فشل في حفظ الصورة في التخزين المؤقت: {e}")
    
    def _generate_image_cache_key(self, request: ImageGenerationRequest) -> str:
        """إنشاء مفتاح التخزين المؤقت للصورة"""
        cache_string = f"{request.prompt}_{request.image_type}_{request.style}"
        return f"image_{hashlib.md5(cache_string.encode()).hexdigest()}"
    
    def _organize_image_results(self, images: List[ImageGenerationResult], article: Dict) -> Dict:
        """تنظيم نتائج الصور"""
        try:
            result = {
                'images_generated': len(images),
                'total_cost': sum(img.cost for img in images),
                'image_urls': [],
                'image_metadata': [],
                'thumbnail_url': None,
                'thumbnail_metadata': None
            }
            
            # تنظيم الصور حسب النوع
            for image in images:
                if image.image_type == 'thumbnail':
                    result['thumbnail_url'] = image.url
                    result['thumbnail_metadata'] = {
                        'url': image.url,
                        'type': image.image_type,
                        'prompt': image.prompt,
                        'quality_score': image.quality_score,
                        'cached': image.cached
                    }
                else:
                    result['image_urls'].append(image.url)
                    result['image_metadata'].append({
                        'url': image.url,
                        'type': image.image_type,
                        'prompt': image.prompt,
                        'quality_score': image.quality_score,
                        'cached': image.cached
                    })
            
            # إذا لم تكن هناك صورة مصغرة، استخدم الصورة الرئيسية
            if not result['thumbnail_url'] and result['image_urls']:
                result['thumbnail_url'] = result['image_urls'][0]
                result['thumbnail_metadata'] = result['image_metadata'][0].copy()
                result['thumbnail_metadata']['type'] = 'thumbnail'
            
            return result
            
        except Exception as e:
            logger.error(f"❌ فشل في تنظيم نتائج الصور: {e}")
            return {'images_generated': 0, 'total_cost': 0.0, 'image_urls': [], 'image_metadata': []}
    
    def _check_daily_limit(self) -> bool:
        """فحص الحد اليومي"""
        try:
            # إعادة تعيين العداد إذا كان يوم جديد
            current_date = datetime.now().date()
            if self.daily_usage['date'] != current_date:
                self.daily_usage = {
                    'date': current_date,
                    'images_generated': 0,
                    'total_cost': 0.0,
                    'cache_hits': 0,
                    'cache_misses': 0
                }
            
            return self.daily_usage['images_generated'] < self.settings['max_daily_generations']
            
        except Exception as e:
            logger.error(f"❌ فشل في فحص الحد اليومي: {e}")
            return True  # السماح بالمتابعة في حالة الخطأ
    
    def _get_fallback_images(self, article: Dict) -> Dict:
        """الحصول على صور احتياطية"""
        try:
            # إرجاع نتيجة فارغة مع رسالة
            return {
                'images_generated': 0,
                'total_cost': 0.0,
                'image_urls': [],
                'image_metadata': [],
                'thumbnail_url': None,
                'thumbnail_metadata': None,
                'fallback_reason': 'تم تجاوز الحد اليومي أو فشل في التوليد'
            }
            
        except Exception as e:
            logger.error(f"❌ فشل في الحصول على صور احتياطية: {e}")
            return {}
    
    def get_daily_stats(self) -> Dict:
        """الحصول على إحصائيات اليوم"""
        try:
            cache_hit_rate = 0.0
            total_requests = self.daily_usage['cache_hits'] + self.daily_usage['cache_misses']
            
            if total_requests > 0:
                cache_hit_rate = (self.daily_usage['cache_hits'] / total_requests) * 100
            
            return {
                'date': self.daily_usage['date'].isoformat(),
                'images_generated': self.daily_usage['images_generated'],
                'max_daily_limit': self.settings['max_daily_generations'],
                'remaining_quota': self.settings['max_daily_generations'] - self.daily_usage['images_generated'],
                'total_cost': round(self.daily_usage['total_cost'], 3),
                'cache_hit_rate': round(cache_hit_rate, 1),
                'cache_hits': self.daily_usage['cache_hits'],
                'cache_misses': self.daily_usage['cache_misses']
            }
            
        except Exception as e:
            logger.error(f"❌ فشل في جمع الإحصائيات اليومية: {e}")
            return {}

# إنشاء مثيل عام
enhanced_image_manager = EnhancedImageManager()
