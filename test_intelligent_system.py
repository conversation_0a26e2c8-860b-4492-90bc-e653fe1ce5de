#!/usr/bin/env python3
# اختبار النظام الذكي المحسن
import asyncio
import time
import json
import sys
import os

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def print_header(title):
    """طباعة عنوان مع تنسيق"""
    print("\n" + "="*60)
    print(f"🧪 {title}")
    print("="*60)

def print_result(test_name, success, details=""):
    """طباعة نتيجة اختبار"""
    status = "✅ نجح" if success else "❌ فشل"
    print(f"{status} {test_name}")
    if details:
        print(f"   📝 {details}")

async def test_intelligent_news_tracker():
    """اختبار نظام تتبع الأخبار الذكي"""
    print_header("اختبار نظام تتبع الأخبار الذكي")
    
    try:
        from modules.intelligent_news_tracker import intelligent_news_tracker
        
        # إنشاء مقالات اختبار
        test_articles = [
            {
                'title': 'Breaking: New AAA Game Announced at Gaming Conference',
                'content': 'A major gaming studio has announced their latest AAA title featuring cutting-edge graphics and innovative gameplay mechanics. The game is set to release next year with exclusive content.',
                'source': 'IGN',
                'published_date': '2025-01-19'
            },
            {
                'title': 'Regular Gaming Update: Patch Notes Released',
                'content': 'The latest patch for the popular online game includes bug fixes and minor balance changes.',
                'source': 'GameSpot',
                'published_date': '2025-01-19'
            }
        ]
        
        # اختبار التحليل والتتبع
        analyzed_articles = await intelligent_news_tracker.analyze_and_track_news(test_articles)
        
        success = len(analyzed_articles) == len(test_articles)
        print_result("تحليل وتتبع المقالات", success, f"تم تحليل {len(analyzed_articles)} مقال")
        
        # فحص المقالات المهمة
        important_articles = [a for a in analyzed_articles if a.get('importance_score', 0) >= 70]
        print_result("اكتشاف المقالات المهمة", len(important_articles) > 0, 
                    f"تم اكتشاف {len(important_articles)} مقال مهم")
        
        # اختبار الإحصائيات
        stats = intelligent_news_tracker.get_tracking_stats()
        has_stats = isinstance(stats, dict)
        print_result("إحصائيات التتبع", has_stats, f"الإحصائيات: {stats}")
        
        return success and has_stats
        
    except Exception as e:
        print_result("نظام تتبع الأخبار الذكي", False, f"خطأ: {e}")
        return False

async def test_enhanced_image_manager():
    """اختبار مدير الصور المحسن"""
    print_header("اختبار مدير الصور المحسن (حد أقصى 3 صور)")
    
    try:
        from modules.enhanced_image_manager import enhanced_image_manager
        
        # مقال اختبار
        test_article = {
            'title': 'New RPG Game Features Amazing Graphics',
            'content': 'This new role-playing game showcases incredible visual effects and detailed character models that push the boundaries of gaming graphics.',
            'importance_score': 85.0
        }
        
        # اختبار توليد الصور
        print("🎨 اختبار توليد الصور...")
        image_result = await enhanced_image_manager.generate_images_for_article(test_article)
        
        has_images = isinstance(image_result, dict)
        images_count = image_result.get('images_generated', 0) if has_images else 0
        
        print_result("توليد الصور", has_images, f"تم توليد {images_count} صورة")
        
        # فحص الحد الأقصى للصور
        max_limit_respected = images_count <= 3
        print_result("احترام الحد الأقصى (3 صور)", max_limit_respected, 
                    f"عدد الصور: {images_count}/3")
        
        # اختبار الإحصائيات اليومية
        daily_stats = enhanced_image_manager.get_daily_stats()
        has_daily_stats = isinstance(daily_stats, dict)
        print_result("الإحصائيات اليومية", has_daily_stats, f"الإحصائيات: {daily_stats}")
        
        return has_images and max_limit_respected and has_daily_stats
        
    except Exception as e:
        print_result("مدير الصور المحسن", False, f"خطأ: {e}")
        return False

async def test_intelligent_content_processor():
    """اختبار معالج المحتوى الذكي"""
    print_header("اختبار معالج المحتوى الذكي المتكامل")
    
    try:
        from modules.intelligent_content_processor import intelligent_content_processor
        
        # مقالات اختبار
        test_articles = [
            {
                'title': 'Exclusive: Major Gaming Studio Reveals Next-Gen Console',
                'content': 'In an exclusive interview, the CEO of a major gaming studio revealed details about their upcoming next-generation gaming console that promises revolutionary features.',
                'source': 'Polygon',
                'quality_score': 80.0
            },
            {
                'title': 'Gaming News: Small Update Released',
                'content': 'A small update was released for a popular game.',
                'source': 'Unknown',
                'quality_score': 40.0
            }
        ]
        
        # اختبار المعالجة الذكية
        print("🧠 اختبار المعالجة الذكية...")
        processed_articles = await intelligent_content_processor.process_articles_intelligently(test_articles)
        
        processing_success = len(processed_articles) > 0
        print_result("المعالجة الذكية", processing_success, 
                    f"تم معالجة {len(processed_articles)} مقال")
        
        # فحص التحسينات المطبقة
        enhanced_articles = [a for a in processed_articles if a.get('processing_info')]
        has_enhancements = len(enhanced_articles) > 0
        print_result("تطبيق التحسينات", has_enhancements, 
                    f"تم تحسين {len(enhanced_articles)} مقال")
        
        # اختبار الإحصائيات
        processing_stats = intelligent_content_processor.get_processing_stats()
        has_processing_stats = isinstance(processing_stats, dict)
        print_result("إحصائيات المعالجة", has_processing_stats, 
                    f"الإحصائيات: {processing_stats}")
        
        return processing_success and has_enhancements and has_processing_stats
        
    except Exception as e:
        print_result("معالج المحتوى الذكي", False, f"خطأ: {e}")
        return False

async def test_enhanced_intelligent_main():
    """اختبار النظام الرئيسي المحسن"""
    print_header("اختبار النظام الرئيسي المحسن")
    
    try:
        from enhanced_intelligent_main import EnhancedIntelligentNewsBot
        
        # إنشاء الوكيل
        bot = EnhancedIntelligentNewsBot()
        print_result("إنشاء الوكيل", True, "تم إنشاء الوكيل بنجاح")
        
        # اختبار جمع المحتوى (مع حد زمني)
        print("🤖 اختبار جمع الأخبار الذكي...")
        try:
            articles = await asyncio.wait_for(
                bot.run_intelligent_news_collection(),
                timeout=60.0  # دقيقة واحدة كحد أقصى
            )
            
            collection_success = isinstance(articles, list)
            articles_count = len(articles) if collection_success else 0
            
            print_result("جمع الأخبار الذكي", collection_success, 
                        f"تم جمع {articles_count} مقال")
            
            # فحص جودة المقالات
            if articles:
                quality_articles = [a for a in articles if a.get('final_quality_score', 0) >= 60]
                quality_ratio = len(quality_articles) / len(articles) * 100
                
                print_result("جودة المقالات", quality_ratio >= 50, 
                            f"نسبة الجودة: {quality_ratio:.1f}%")
            
            # اختبار الإحصائيات المفصلة
            detailed_stats = bot.get_detailed_stats()
            has_detailed_stats = isinstance(detailed_stats, dict)
            print_result("الإحصائيات المفصلة", has_detailed_stats, 
                        f"الإحصائيات: متوفرة" if has_detailed_stats else "غير متوفرة")
            
            return collection_success and has_detailed_stats
            
        except asyncio.TimeoutError:
            print_result("جمع الأخبار الذكي", False, "انتهت المهلة الزمنية (60s)")
            return False
        
    except Exception as e:
        print_result("النظام الرئيسي المحسن", False, f"خطأ: {e}")
        return False

async def test_integration_features():
    """اختبار ميزات التكامل الخاصة"""
    print_header("اختبار ميزات التكامل الخاصة")
    
    try:
        # اختبار 1: البحث الذكي عن المتابعة
        print("🔍 اختبار البحث الذكي عن المتابعة...")
        
        from modules.intelligent_news_tracker import intelligent_news_tracker
        
        # مقال مهم يحتاج متابعة
        important_article = {
            'title': 'Breaking: Revolutionary Gaming Technology Announced',
            'content': 'A groundbreaking new gaming technology has been announced that could change the industry forever.',
            'source': 'IGN',
            'published_date': '2025-01-19'
        }
        
        # تحليل المقال
        analyzed = await intelligent_news_tracker.analyze_and_track_news([important_article])
        
        follow_up_success = len(analyzed) > 0 and analyzed[0].get('importance_score', 0) > 70
        print_result("البحث الذكي عن المتابعة", follow_up_success, 
                    f"نقاط الأهمية: {analyzed[0].get('importance_score', 0):.1f}" if analyzed else "فشل")
        
        # اختبار 2: حد الصور الأقصى
        print("🎨 اختبار حد الصور الأقصى...")
        
        from modules.enhanced_image_manager import enhanced_image_manager
        
        # مقال يستحق 3 صور
        high_importance_article = {
            'title': 'Exclusive First Look: Next-Gen Gaming Console Revealed',
            'content': 'This is a very long and detailed article about an extremely important gaming announcement that deserves multiple high-quality images to accompany the comprehensive coverage of this groundbreaking news in the gaming industry.',
            'importance_score': 95.0
        }
        
        # توليد الصور
        image_result = await enhanced_image_manager.generate_images_for_article(high_importance_article)
        
        images_generated = image_result.get('images_generated', 0) if image_result else 0
        max_limit_respected = images_generated <= 3
        
        print_result("حد الصور الأقصى", max_limit_respected, 
                    f"تم توليد {images_generated} صورة (الحد الأقصى: 3)")
        
        # اختبار 3: التكامل الشامل
        print("🔗 اختبار التكامل الشامل...")
        
        from modules.intelligent_content_processor import intelligent_content_processor
        
        # معالجة شاملة
        comprehensive_result = await intelligent_content_processor.process_articles_intelligently([
            important_article, high_importance_article
        ])
        
        integration_success = len(comprehensive_result) > 0
        enhanced_count = sum(1 for a in comprehensive_result if a.get('processing_info'))
        
        print_result("التكامل الشامل", integration_success, 
                    f"تم تحسين {enhanced_count} مقال من {len(comprehensive_result)}")
        
        return follow_up_success and max_limit_respected and integration_success
        
    except Exception as e:
        print_result("ميزات التكامل الخاصة", False, f"خطأ: {e}")
        return False

async def run_comprehensive_test():
    """تشغيل اختبار شامل للنظام الذكي"""
    print("🧪 بدء الاختبار الشامل للنظام الذكي المحسن")
    print("⏰ هذا قد يستغرق بضع دقائق...")
    
    test_results = []
    
    # تشغيل جميع الاختبارات
    tests = [
        ("نظام تتبع الأخبار الذكي", test_intelligent_news_tracker),
        ("مدير الصور المحسن", test_enhanced_image_manager),
        ("معالج المحتوى الذكي", test_intelligent_content_processor),
        ("النظام الرئيسي المحسن", test_enhanced_intelligent_main),
        ("ميزات التكامل الخاصة", test_integration_features)
    ]
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔄 تشغيل اختبار: {test_name}")
            result = await test_func()
            test_results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
                
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
            test_results.append((test_name, False))
    
    # عرض النتائج النهائية
    print_header("النتائج النهائية للنظام الذكي")
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    success_rate = (passed / total) * 100 if total > 0 else 0
    
    print(f"📊 إجمالي الاختبارات: {total}")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {total - passed}")
    print(f"📈 معدل النجاح: {success_rate:.1f}%")
    
    # تقييم النظام
    if success_rate >= 80:
        print("\n🎉 النظام الذكي المحسن يعمل بشكل ممتاز!")
        print("💡 الميزات الجديدة:")
        print("   🔍 البحث الذكي مع المتابعة التلقائية")
        print("   🎨 حد أقصى 3 صور لكل مقال")
        print("   🧠 معالجة ذكية للمحتوى")
        print("   📊 تحليلات شاملة ومتقدمة")
    elif success_rate >= 60:
        print("\n⚠️ النظام يعمل بشكل جيد مع بعض المشاكل")
        print("💡 راجع الاختبارات الفاشلة وأصلح المشاكل")
    else:
        print("\n❌ النظام يحتاج إلى إصلاحات")
        print("💡 راجع السجلات وأصلح الأخطاء قبل الاستخدام")
    
    # عرض الاختبارات الفاشلة
    failed_tests = [name for name, result in test_results if not result]
    if failed_tests:
        print(f"\n❌ الاختبارات الفاشلة:")
        for test_name in failed_tests:
            print(f"   - {test_name}")
    
    # حفظ النتائج
    results_data = {
        'timestamp': time.time(),
        'total_tests': total,
        'passed_tests': passed,
        'failed_tests': total - passed,
        'success_rate': success_rate,
        'test_details': [{'name': name, 'passed': result} for name, result in test_results],
        'features_tested': [
            'intelligent_news_tracking',
            'enhanced_image_management',
            'intelligent_content_processing',
            'comprehensive_integration'
        ]
    }
    
    try:
        with open('intelligent_system_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(results_data, f, ensure_ascii=False, indent=2)
        print(f"\n💾 تم حفظ النتائج في: intelligent_system_test_results.json")
    except Exception as e:
        print(f"⚠️ فشل في حفظ النتائج: {e}")

async def main():
    """الدالة الرئيسية"""
    await run_comprehensive_test()

if __name__ == "__main__":
    asyncio.run(main())
