#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لـ Freepik API
"""

import asyncio
import aiohttp
import json

async def test_freepik_simple():
    """اختبار بسيط لـ Freepik API"""
    
    api_key = "FPSX1ee910637a8ec349e6d8c7f17a57740b"
    
    headers = {
        'x-freepik-api-key': api_key,
        'Content-Type': 'application/json'
    }
    
    payload = {
        'prompt': 'A gaming controller',
        'resolution': '2k',
        'aspect_ratio': 'square_1_1',
        'model': 'realism'
    }
    
    print("🧪 اختبار بسيط لـ Freepik API...")
    print(f"المفتاح: {api_key[:10]}...")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                'https://api.freepik.com/v1/ai/mystic',
                headers=headers,
                json=payload,
                timeout=30
            ) as response:
                
                print(f"📊 كود الاستجابة: {response.status}")
                
                if response.status == 200:
                    try:
                        result = await response.json()
                        print(f"✅ استجابة JSON صحيحة")
                        print(f"📋 المفاتيح: {list(result.keys())}")
                        print(f"📄 المحتوى الكامل:")
                        print(json.dumps(result, indent=2, ensure_ascii=False))
                        
                        if 'task_id' in result:
                            print(f"🎯 تم إنشاء مهمة: {result['task_id']}")
                            return True
                        elif 'data' in result:
                            print(f"📊 تم الحصول على بيانات")
                            return True
                        else:
                            print(f"⚠️ استجابة غير متوقعة")
                            return False
                            
                    except json.JSONDecodeError as e:
                        print(f"❌ خطأ في تحليل JSON: {e}")
                        text = await response.text()
                        print(f"📄 النص الخام: {text[:500]}...")
                        return False
                        
                else:
                    print(f"❌ خطأ HTTP: {response.status}")
                    text = await response.text()
                    print(f"📄 رسالة الخطأ: {text[:500]}...")
                    return False
                    
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_freepik_simple())
    print(f"\n{'✅ نجح' if success else '❌ فشل'}")
