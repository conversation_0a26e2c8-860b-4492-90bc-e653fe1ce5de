<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Txtify</title>
    <link rel="icon" href="/static/favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="/static/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Exo:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script async defer src="https://buttons.github.io/buttons.js"></script>
    <script src="/static/faq_scripts.js"></script>
</head>

<body class="dark-theme">
    <div class="navbar">
        <div class="navbar-content">
            <!-- <a href="/" class="logo">Txtify</a> -->
            <a href="/" class="logo">
                <img src="/static/txtify_logo.png" alt="Txtify Logo" class="logo-image">
                <span>Txtify</span>
            </a>            
            <div class="menu-toggle" id="menuToggle">
                <i class="fa fa-bars"></i>
            </div>
            <div class="nav-links" id="navLinks">
                <a href="/">Application</a>
                <a href="/faq">FAQ</a>
                <a href="/contact">Contact</a>
                <a class="github-button" href="https://github.com/lkmeta/txtify"
                    data-color-scheme="no-preference: light; light: light; dark: dark;" data-size="large"
                    aria-label="Star lkmeta/txtify on GitHub">Star</a>
            </div>
            <div class="theme-toggle-wrapper">
                <label class="theme-toggle">
                    <input type="checkbox" id="themeToggle">
                    <div class="toggle-body"></div>
                    <div class="celestial-body"></div>
                </label>
            </div>
        </div>
    </div>

    <div class="container_faq">
        <h1>Frequently Asked Questions (FAQ)</h1>
        <div class="faq-content">
            <h2>General</h2>
            <h3>What is Txtify?</h3>
            <p>Txtify is a FREE and OPEN-SOURCE tool that converts audio and video into text
                using state-of-the-art AI models for rapid and precise transcriptions. With Txtify, you can convert your
                files or urls effortlessly, and it's available for self-hosting, offering you full control over your
                transcription process.</p>

            <!-- add line spacing using dots -->
            <!-- <h3>____________________________________________________________</h3> -->
            <hr class="custom-line">

            <h2>Models</h2>
            <h3>Which AI models are supported?</h3>
            <p>Txtify utilizes advanced AI models from <a href="https://openai.com/index/whisper" target="_blank"
                    style="color: var(--primary-color); font-weight: bold;">Whisper</a> for transcription. The
                including models are: Whisper Tiny, Whisper Base, Whisper Small, Whisper Medium and Whisper Large.
                These models are sourced from <a href="https://huggingface.co/models" target="_blank"
                    style="color: var(--primary-color); font-weight: bold;">Hugging Face</a> repository.</p>

            <!-- <span>____________________________________________________________</span> -->
            <hr class="custom-line">

            <h3>What are the memory requirements and speed for the models?</h3>
            <p style="text-align: center;">Below is a table summarizing the available Whisper models, their memory
                requirements,
                and their relative
                inference speeds. The speed and memory requirements may vary based on the available hardware.</p>

            <br>
            <div class="table-container">
                <table class="model-table">
                    <thead>
                        <tr>
                            <th>Size</th>
                            <th>Parameters</th>
                            <!-- <th>English-only model</th> -->
                            <th>Multilingual model</th>
                            <th>Required VRAM</th>
                            <th>Relative speed</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>tiny</td>
                            <td>39 M</td>
                            <!-- <td>tiny.en</td> -->
                            <td>tiny</td>
                            <td>~1 GB</td>
                            <td>~32x</td>
                        </tr>
                        <tr>
                            <td>base</td>
                            <td>74 M</td>
                            <!-- <td>base.en</td> -->
                            <td>base</td>
                            <td>~1 GB</td>
                            <td>~16x</td>
                        </tr>
                        <tr>
                            <td>small</td>
                            <td>244 M</td>
                            <!-- <td>small.en</td> -->
                            <td>small</td>
                            <td>~2 GB</td>
                            <td>~6x</td>
                        </tr>
                        <tr>
                            <td>medium</td>
                            <td>769 M</td>
                            <!-- <td>medium.en</td> -->
                            <td>medium</td>
                            <td>~5 GB</td>
                            <td>~2x</td>
                        </tr>
                        <tr>
                            <td>large</td>
                            <td>1550 M</td>
                            <!-- <td>N/A</td> -->
                            <td>large</td>
                            <td>~10 GB</td>
                            <td>~1x</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- <h3>____________________________________________________________</h3> -->
            <hr class="custom-line">

            <h2>Languages</h2>
            <h3>Which languages are supported for transcription?</h3>
            <p>Txtify supports transcription in the following
                languages: Afrikaans, Amharic, Arabic, Assamese, Azerbaijani, Belarusian, Bulgarian, Bengali, Bosnian,
                Catalan, Cebuano, Czech, Welsh, Danish, German, Greek, English, Spanish, Estonian, Persian, Finnish,
                French, Galician, Gujarati, Hebrew, Hindi, Croatian, Hungarian, Armenian, Indonesian, Icelandic,
                Italian, Japanese, Javanese, Georgian, Kazakh, Khmer, Kannada, Korean, Lao, Lithuanian, Latvian,
                Malayalam, Mongolian, Marathi, Malay, Burmese, Nepali, Dutch, Punjabi, Polish, Portuguese, Romanian,
                Russian, Sinhala, Slovak, Slovenian, Albanian, Serbian, Swedish, Swahili, Tamil, Telugu, Thai, Turkish,
                Ukrainian, Urdu, Uzbek, Vietnamese, Yiddish, Yoruba, Chinese.</p>
            <!-- <p id="transcription-languages-list"></p> -->

            <!-- <span>____________________________________________________________</span> -->
            <hr class="custom-line">

            <h3>Which languages are supported for translation?</h3>
            <p>Translations are supported in the following
                languages using <a href="https://www.deepl.com/en/translator" target="_blank"
                    style="color: var(--primary-color); font-weight: bold;">DeepL</a>: Arabic, Bulgarian, Czech,
                Danish,
                German, Greek, English, English (British), English (American), Spanish, Estonian, Finnish, French,
                Hungarian, Indonesian, Italian, Japanese, Korean, Lithuanian, Latvian, Norwegian Bokmål, Dutch,
                Polish,
                Portuguese, Portuguese (Brazilian), Portuguese (excluding Brazilian Portuguese), Romanian, Russian,
                Slovak, Slovenian, Swedish, Turkish, Ukrainian, Chinese (simplified).</p>
            <!-- <p id="translation-languages-list" class="language-list"></p> -->

            <!-- <h3>____________________________________________________________</h3> -->
            <hr class="custom-line">

            <h2>Limitations</h2>
            <h3>Are there any limitations?</h3>
            <p>Yes, this version has limitations. You can upload files up to 100MB or transcribe YouTube videos up
                to 10 minutes. However, when you self-host Txtify, you can modify and run the application without these
                limitations, giving you full control over the transcription process.</p>


            <!-- <h3>____________________________________________________________</h3> -->
            <hr class="custom-line">

            <h2>File and Process Deletion</h2>
            <h3>What happens to my files and processes after I close the window?</h3>
            <p>After the window is closed,
                all generated files and the transcription process are automatically deleted to ensure your data
                privacy
                and security.</p>

            <!-- <h3>____________________________________________________________</h3> -->
            <hr class="custom-line">

            <h2>Self-Hosting</h2>
            <h3>Can I self-host Txtify?</h3>
            <p style="text-align: center;">Yes, you can self-host Txtify with full features. Please check the <a
                    href="https://github.com/lkmeta/txtify" target="_blank"
                    style="color: var(--primary-color); font-weight: bold;">GitHub</a> repo for
                instructions.</p>

            <!-- <h3>____________________________________________________________</h3> -->
            <hr class="custom-line">

            <h2>Demo Video</h2>
            <h3>Is there a demo video showcasing the features of Txtify?</h3>
            <p style="text-align: center;">Yes, you can watch the demo video below:</p>
            <iframe width="560" height="315" src="https://www.youtube.com/embed/wha6_4zyXXo" frameborder="0"
                allowfullscreen></iframe>


            <!-- 
            <h2>Stay Updated</h2>
            <p>If you liked the project, consider giving it a star on GitHub to stay updated with the latest versions
                and features. Your support is appreciated!</p>
            <a class="github-button" href="https://github.com/lkmeta/txtify"
                data-color-scheme="no-preference: light; light: light; dark: dark;" data-size="large"
                aria-label="Star lkmeta/txtify on GitHub">Star</a>
 -->
            <!-- <h3>____________________________________________________________</h3> -->
            <hr class="custom-line">

            <h2>Contribute</h2>
            <h3>Want to contribute to Txtify?</h3>
            <p style="text-align: center;">Your contributions are welcome! Feel free to open a pull request on
                our <a href="https://github.com/lkmeta/txtify" target="_blank"
                    style="color: var(--primary-color); font-weight: bold;">GitHub repository</a>.</p>

            <!-- <h3>____________________________________________________________</h3> -->
            <hr class="custom-line">

            <h2>Report Errors</h2>
            <h3>Found any issues or bugs?</h3>
            <p style="text-align: center;">Please report them using the contact form on the <a href="/contact"
                    style="color: var(--primary-color); font-weight: bold;">contact page</a>. We appreciate your
                feedback and will work to resolve any problems as quickly as possible.</p>

            <!-- <h3>____________________________________________________________</h3> -->
            <hr class="custom-line">

            <h2>Contact</h2>

            <p style="text-align: center;">If your question wasn't answered here, please use our <a href="/contact"
                    style="color: var(--primary-color); font-weight: bold;">contact page</a> to reach out to us.</p>

        </div>
    </div>

    <div class="footer">
        &copy; 2025 Txt<span style="color: var(--primary-color);">ify</span>. Created with <i
            class="fa-solid fa-heart heart"></i> by <a href="https://lkmeta.com" target="_blank">lkmeta</a>.
    </div>


    <!-- <script src="/static/scripts.js"></script> -->
    <script src="/static/faq_scripts.js"></script>
</body>

</html>