#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مفاتيح SerpAPI
"""

import requests
import json

def test_serpapi_key(api_key, key_name):
    """اختبار مفتاح SerpAPI"""
    print(f"🔍 اختبار {key_name}: {api_key[:8]}...")
    
    url = "https://serpapi.com/search.json"
    params = {
        'api_key': api_key,
        'q': 'gaming news',
        'num': 3,
        'hl': 'en',
        'gl': 'us'
    }
    
    try:
        response = requests.get(url, params=params, timeout=15)
        print(f"كود الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ المفتاح يعمل!")
            data = response.json()
            
            print(f"المفاتيح المتاحة: {list(data.keys())}")
            
            # فحص النتائج العادية
            if 'organic_results' in data:
                organic_count = len(data['organic_results'])
                print(f"عدد النتائج العادية: {organic_count}")
                
                if organic_count > 0:
                    first_result = data['organic_results'][0]
                    print(f"أول نتيجة: {first_result.get('title', '')[:50]}...")
            
            # فحص الأخبار
            if 'news_results' in data:
                news_count = len(data['news_results'])
                print(f"عدد الأخبار: {news_count}")
                
                if news_count > 0:
                    first_news = data['news_results'][0]
                    print(f"أول خبر: {first_news.get('title', '')[:50]}...")
            
            # فحص معلومات البحث
            if 'search_information' in data:
                search_info = data['search_information']
                print(f"وقت البحث: {search_info.get('query_displayed_time', 'غير محدد')}")
            
            return True
            
        else:
            print(f"❌ خطأ: {response.status_code}")
            try:
                error_data = response.json()
                if 'error' in error_data:
                    print(f"رسالة الخطأ: {error_data['error']}")
            except:
                print(f"نص الخطأ: {response.text[:200]}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ انتهت مهلة الطلب")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ خطأ في الاتصال")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def test_serpapi_news(api_key):
    """اختبار البحث في الأخبار"""
    print(f"\n📰 اختبار البحث في الأخبار...")
    
    url = "https://serpapi.com/search.json"
    params = {
        'api_key': api_key,
        'q': 'gaming news',
        'tbm': 'nws',  # البحث في الأخبار
        'num': 5,
        'hl': 'en',
        'gl': 'us'
    }
    
    try:
        response = requests.get(url, params=params, timeout=15)
        
        if response.status_code == 200:
            data = response.json()
            
            if 'news_results' in data:
                news_results = data['news_results']
                print(f"✅ تم العثور على {len(news_results)} خبر")
                
                for i, news in enumerate(news_results[:3], 1):
                    print(f"{i}. {news.get('title', '')[:60]}...")
                    print(f"   المصدر: {news.get('source', '')}")
                    print(f"   التاريخ: {news.get('date', '')}")
                
                return True
            else:
                print("❌ لم يتم العثور على أخبار")
                return False
        else:
            print(f"❌ فشل البحث في الأخبار: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في البحث في الأخبار: {e}")
        return False

if __name__ == "__main__":
    print("🧪 اختبار مفاتيح SerpAPI")
    print("=" * 50)
    
    # المفاتيح للاختبار
    keys = [
        ("8b221d23f3aa037d438db307927f904933ae3037", "المفتاح الأول"),
        ("bf554c29b8fe075d050e927706581cd1107acbb3", "المفتاح الثاني")
    ]
    
    results = []
    
    for api_key, key_name in keys:
        print(f"\n{'='*30}")
        result = test_serpapi_key(api_key, key_name)
        results.append((key_name, api_key, result))
        
        # إذا نجح المفتاح، اختبر البحث في الأخبار
        if result:
            news_result = test_serpapi_news(api_key)
            print(f"البحث في الأخبار: {'✅ نجح' if news_result else '❌ فشل'}")
    
    # عرض النتائج النهائية
    print(f"\n{'='*50}")
    print("📊 ملخص النتائج:")
    
    working_keys = []
    for key_name, api_key, result in results:
        status = "✅ يعمل" if result else "❌ لا يعمل"
        print(f"{key_name}: {status}")
        if result:
            working_keys.append((key_name, api_key))
    
    if working_keys:
        print(f"\n🎉 تم العثور على {len(working_keys)} مفتاح يعمل!")
        best_key = working_keys[0]
        print(f"💡 أفضل مفتاح للاستخدام: {best_key[0]} ({best_key[1][:8]}...)")
    else:
        print("\n😞 لم يعمل أي من المفاتيح")
