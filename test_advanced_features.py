#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الميزات المتقدمة الجديدة - محدث 2025
"""

import asyncio
import sys
import os
import json
from datetime import datetime

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.user_engagement import engagement_engine
from modules.advanced_seo import advanced_seo
from modules.ai_personality import ai_personality
from modules.intelligent_cms import intelligent_cms

class AdvancedFeatureTester:
    """فئة اختبار الميزات المتقدمة"""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = datetime.now()
    
    async def run_comprehensive_test(self):
        """تشغيل اختبار شامل للميزات المتقدمة"""
        print("🚀 بدء اختبار الميزات المتقدمة 2025...\n")
        
        tests = [
            ("العناوين الفيروسية المتقدمة", self.test_advanced_viral_titles),
            ("تحسين المقتطفات المميزة", self.test_featured_snippets),
            ("البحث الصوتي", self.test_voice_search_optimization),
            ("People Also Ask", self.test_people_also_ask),
            ("الذكاء الاصطناعي المتطور", self.test_advanced_ai),
            ("تحليل المنافسين", self.test_competitor_analysis),
            ("التحسين التلقائي", self.test_auto_optimization)
        ]
        
        for test_name, test_func in tests:
            try:
                print(f"🔍 اختبار {test_name}...")
                result = await test_func()
                self.test_results[test_name] = result
                
                if result['success']:
                    print(f"✅ {test_name}: نجح")
                    if 'details' in result:
                        for key, value in result['details'].items():
                            print(f"   • {key}: {value}")
                else:
                    print(f"❌ {test_name}: فشل - {result.get('error', 'خطأ غير محدد')}")
                
                print()
                
            except Exception as e:
                print(f"❌ {test_name}: خطأ في الاختبار - {e}\n")
                self.test_results[test_name] = {'success': False, 'error': str(e)}
        
        await self.generate_advanced_report()
    
    async def test_advanced_viral_titles(self):
        """اختبار العناوين الفيروسية المتقدمة"""
        try:
            test_cases = [
                ("Minecraft Update 1.21 Released", "Minecraft", "news"),
                ("Best Gaming Laptops 2025", "Gaming", "guide"),
                ("Fortnite vs PUBG Comparison", "Fortnite", "review"),
                ("Secret Tips for Call of Duty", "Call of Duty", "guide"),
                ("Exclusive Interview with Game Developer", "Gaming", "news")
            ]
            
            results = []
            for original, game, content_type in test_cases:
                viral_title = engagement_engine.generate_viral_title(original, game, content_type)
                
                # تحليل جودة العنوان
                quality_score = self._analyze_title_quality(viral_title, original)
                results.append({
                    'original': original,
                    'viral': viral_title,
                    'quality_score': quality_score,
                    'improvement': len(viral_title) > len(original)
                })
            
            avg_quality = sum(r['quality_score'] for r in results) / len(results)
            
            return {
                'success': True,
                'message': 'العناوين الفيروسية المتقدمة تعمل بشكل ممتاز',
                'details': {
                    'عدد الاختبارات': len(test_cases),
                    'متوسط الجودة': f"{avg_quality:.1f}/100",
                    'معدل التحسين': f"{sum(1 for r in results if r['improvement']) / len(results) * 100:.1f}%",
                    'أفضل عنوان': max(results, key=lambda x: x['quality_score'])['viral'][:50] + "..."
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _analyze_title_quality(self, viral_title: str, original_title: str) -> float:
        """تحليل جودة العنوان الفيروسي"""
        score = 50  # نقطة البداية
        
        # وجود رموز تعبيرية
        if any(emoji in viral_title for emoji in ['🔥', '⚡', '🚨', '💥', '🎮']):
            score += 15
        
        # وجود كلمات جذابة
        attractive_words = ['عاجل', 'حصري', 'سر', 'مذهل', 'لن تصدق', 'أفضل']
        if any(word in viral_title for word in attractive_words):
            score += 20
        
        # طول مناسب (50-60 حرف)
        if 50 <= len(viral_title) <= 60:
            score += 10
        
        # تحسن عن العنوان الأصلي
        if len(viral_title) > len(original_title):
            score += 5
        
        return min(100, score)
    
    async def test_featured_snippets(self):
        """اختبار تحسين المقتطفات المميزة"""
        try:
            test_content = "هذا محتوى اختبار لتحسين المقتطفات المميزة في محركات البحث."
            test_keyword = "أفضل ألعاب 2025"
            
            # تحسين المحتوى للمقتطفات المميزة
            optimized_content = advanced_seo.optimize_content_for_featured_snippets(
                test_content, test_keyword
            )
            
            # تحليل التحسينات
            improvements = self._analyze_snippet_optimization(optimized_content, test_content)
            
            return {
                'success': True,
                'message': 'تحسين المقتطفات المميزة يعمل بكفاءة',
                'details': {
                    'طول المحتوى الأصلي': len(test_content),
                    'طول المحتوى المحسن': len(optimized_content),
                    'نسبة الزيادة': f"{(len(optimized_content) / len(test_content) - 1) * 100:.1f}%",
                    'عدد التحسينات': improvements['count'],
                    'أنواع التحسينات': ', '.join(improvements['types'])
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _analyze_snippet_optimization(self, optimized: str, original: str) -> dict:
        """تحليل تحسينات المقتطفات"""
        improvements = {'count': 0, 'types': []}
        
        if 'الأسئلة الشائعة' in optimized:
            improvements['count'] += 1
            improvements['types'].append('أسئلة شائعة')
        
        if '##' in optimized:
            improvements['count'] += 1
            improvements['types'].append('عناوين فرعية')
        
        if '|' in optimized:
            improvements['count'] += 1
            improvements['types'].append('جداول')
        
        if '1.' in optimized or '•' in optimized:
            improvements['count'] += 1
            improvements['types'].append('قوائم')
        
        return improvements
    
    async def test_voice_search_optimization(self):
        """اختبار تحسين البحث الصوتي"""
        try:
            test_content = "محتوى اختبار للبحث الصوتي"
            test_keyword = "كيف ألعب Minecraft"
            
            # تحسين للبحث الصوتي
            voice_optimized = advanced_seo._optimize_for_voice_search(
                advanced_seo, test_content, test_keyword
            )
            
            # تحليل التحسينات الصوتية
            voice_features = self._analyze_voice_optimization(voice_optimized)
            
            return {
                'success': True,
                'message': 'تحسين البحث الصوتي فعال',
                'details': {
                    'أسئلة محادثة': voice_features['conversational_qa'],
                    'أسلوب طبيعي': voice_features['natural_language'],
                    'إجابات مباشرة': voice_features['direct_answers'],
                    'نقاط التحسين': voice_features['optimization_score']
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _analyze_voice_optimization(self, content: str) -> dict:
        """تحليل تحسينات البحث الصوتي"""
        features = {
            'conversational_qa': 'س:' in content and 'ج:' in content,
            'natural_language': any(word in content for word in ['كيف', 'ماذا', 'لماذا']),
            'direct_answers': content.count('ج:') >= 2,
            'optimization_score': 0
        }
        
        # حساب نقاط التحسين
        for feature, present in features.items():
            if feature != 'optimization_score' and present:
                features['optimization_score'] += 25
        
        return features
    
    async def test_people_also_ask(self):
        """اختبار تحسين People Also Ask"""
        try:
            test_keyword = "ألعاب الفيديو"
            
            # توليد محتوى PAA
            paa_content = advanced_seo._generate_people_also_ask_content(
                advanced_seo, test_keyword
            )
            
            # تحليل محتوى PAA
            paa_analysis = self._analyze_paa_content(paa_content)
            
            return {
                'success': True,
                'message': 'تحسين People Also Ask متقدم',
                'details': {
                    'عدد الأسئلة': paa_analysis['question_count'],
                    'تنوع الأسئلة': paa_analysis['question_variety'],
                    'جودة الإجابات': paa_analysis['answer_quality'],
                    'تحسين SEO': paa_analysis['seo_optimization']
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _analyze_paa_content(self, content: str) -> dict:
        """تحليل محتوى People Also Ask"""
        questions = content.count('###')
        
        return {
            'question_count': questions,
            'question_variety': questions >= 5,
            'answer_quality': len(content) > 500,
            'seo_optimization': '##' in content and '###' in content
        }
    
    async def test_advanced_ai(self):
        """اختبار الذكاء الاصطناعي المتطور"""
        try:
            # اختبار اتخاذ قرار متقدم
            context = {
                'situation': 'content_strategy_optimization',
                'urgency': 'high',
                'impact': 'high',
                'data': {'performance_score': 85, 'competition_level': 'high'}
            }
            
            options = [
                {
                    'name': 'viral_content_focus',
                    'data_support': 9,
                    'long_term_impact': 8,
                    'risk_level': 3,
                    'user_benefit': 9,
                    'innovation_level': 7
                },
                {
                    'name': 'seo_optimization_focus',
                    'data_support': 8,
                    'long_term_impact': 9,
                    'risk_level': 2,
                    'user_benefit': 7,
                    'innovation_level': 5
                }
            ]
            
            decision = ai_personality.make_personality_driven_decision(context, options)
            
            # تحليل جودة القرار
            decision_quality = self._analyze_decision_quality(decision)
            
            return {
                'success': True,
                'message': 'الذكاء الاصطناعي المتطور يعمل بكفاءة عالية',
                'details': {
                    'قرار متخذ': decision.get('chosen_option', {}).get('option', {}).get('name', 'غير محدد'),
                    'مستوى الثقة': decision.get('confidence_level', 0),
                    'جودة القرار': decision_quality,
                    'عوامل التحليل': len(decision.get('chosen_option', {}).get('analysis_factors', []))
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _analyze_decision_quality(self, decision: dict) -> str:
        """تحليل جودة القرار"""
        confidence = decision.get('confidence_level', 0)
        
        if confidence >= 80:
            return 'ممتاز'
        elif confidence >= 60:
            return 'جيد جداً'
        elif confidence >= 40:
            return 'جيد'
        else:
            return 'يحتاج تحسين'
    
    async def test_competitor_analysis(self):
        """اختبار تحليل المنافسين"""
        try:
            # محاكاة تحليل المنافسين
            decisions = await intelligent_cms.make_content_decisions()
            
            competitor_analysis = decisions.get('competitor_analysis', {})
            
            return {
                'success': bool(competitor_analysis),
                'message': 'تحليل المنافسين متقدم وشامل',
                'details': {
                    'منافسون محللون': len(competitor_analysis.get('competitors', [])),
                    'مزايا تنافسية': len(competitor_analysis.get('competitive_advantages', [])),
                    'موقع في السوق': competitor_analysis.get('market_positioning', 'غير محدد')
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_auto_optimization(self):
        """اختبار التحسين التلقائي"""
        try:
            # اختبار تنفيذ الاستراتيجية
            strategy_results = await intelligent_cms.execute_content_strategy()
            
            optimization_score = self._calculate_optimization_score(strategy_results)
            
            return {
                'success': True,
                'message': 'التحسين التلقائي يعمل بكفاءة',
                'details': {
                    'قرارات منفذة': len(strategy_results.get('execution_results', {}).get('content_adjustments', [])),
                    'معدل النجاح': strategy_results.get('monitoring_results', {}).get('implementation_success_rate', 0),
                    'نقاط التحسين': optimization_score,
                    'تاريخ المراجعة القادمة': strategy_results.get('next_review_date', 'غير محدد')
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _calculate_optimization_score(self, results: dict) -> float:
        """حساب نقاط التحسين"""
        success_rate = results.get('monitoring_results', {}).get('implementation_success_rate', 0)
        adjustments_count = len(results.get('execution_results', {}).get('content_adjustments', []))
        
        base_score = success_rate
        bonus_score = min(20, adjustments_count * 5)
        
        return min(100, base_score + bonus_score)
    
    async def generate_advanced_report(self):
        """إنشاء تقرير متقدم للاختبارات"""
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results.values() if result.get('success', False))
        success_rate = (successful_tests / total_tests) * 100 if total_tests > 0 else 0
        
        duration = (datetime.now() - self.start_time).total_seconds()
        
        print("\n" + "="*80)
        print("📊 تقرير اختبار الميزات المتقدمة 2025")
        print("="*80)
        
        print(f"🎯 معدل النجاح الإجمالي: {success_rate:.1f}% ({successful_tests}/{total_tests})")
        print(f"⏱️ مدة الاختبار: {duration:.1f} ثانية")
        print(f"📅 تاريخ الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        print("\n📋 تفاصيل النتائج:")
        for test_name, result in self.test_results.items():
            status = "✅ نجح" if result.get('success', False) else "❌ فشل"
            print(f"  • {test_name}: {status}")
            
            if result.get('success') and 'details' in result:
                for key, value in result['details'].items():
                    print(f"    - {key}: {value}")
        
        # تقييم عام
        if success_rate >= 90:
            grade = "A+ ممتاز"
            message = "🎉 جميع الميزات المتقدمة تعمل بكفاءة استثنائية!"
        elif success_rate >= 80:
            grade = "A جيد جداً"
            message = "👍 معظم الميزات تعمل بشكل ممتاز مع تحسينات بسيطة"
        elif success_rate >= 70:
            grade = "B جيد"
            message = "⚠️ الميزات تعمل بشكل جيد لكن تحتاج بعض التحسينات"
        else:
            grade = "C يحتاج تطوير"
            message = "🔧 يحتاج إلى مراجعة وتحسين عدة ميزات"
        
        print(f"\n🏆 التقييم العام: {grade}")
        print(f"💬 التوصية: {message}")
        
        # حفظ التقرير
        try:
            report_data = {
                'timestamp': datetime.now().isoformat(),
                'success_rate': success_rate,
                'duration': duration,
                'results': self.test_results,
                'grade': grade
            }
            
            with open('reports/advanced_features_test_report.json', 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 تم حفظ التقرير في: reports/advanced_features_test_report.json")
            
        except Exception as e:
            print(f"\n⚠️ فشل في حفظ التقرير: {e}")

async def main():
    """الدالة الرئيسية"""
    # إنشاء مجلد التقارير
    os.makedirs('reports', exist_ok=True)
    
    tester = AdvancedFeatureTester()
    await tester.run_comprehensive_test()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⌨️ تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
