# نظام تحليل الأداء والتفاعل المتقدم
import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import statistics
from .logger import logger
from .database import db

class PerformanceAnalytics:
    """نظام تحليل الأداء والتفاعل"""
    
    def __init__(self):
        self.db_path = "data/articles.db"
        self._init_analytics_tables()
    
    def _init_analytics_tables(self):
        """إنشاء جداول التحليلات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # جدول تحليل الأداء اليومي
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS daily_analytics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        date DATE UNIQUE NOT NULL,
                        articles_published INTEGER DEFAULT 0,
                        avg_quality_score REAL DEFAULT 0.0,
                        avg_seo_score REAL DEFAULT 0.0,
                        total_words INTEGER DEFAULT 0,
                        avg_words_per_article REAL DEFAULT 0.0,
                        top_keywords TEXT,
                        top_categories TEXT,
                        success_rate REAL DEFAULT 0.0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # جدول تحليل المحتوى
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS content_analytics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        article_id INTEGER,
                        content_type TEXT,
                        quality_score INTEGER DEFAULT 0,
                        seo_score INTEGER DEFAULT 0,
                        word_count INTEGER DEFAULT 0,
                        keyword_count INTEGER DEFAULT 0,
                        readability_score REAL DEFAULT 0.0,
                        engagement_potential REAL DEFAULT 0.0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (article_id) REFERENCES published_articles (id)
                    )
                ''')
                
                # جدول تتبع الاتجاهات
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS trend_tracking (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        keyword TEXT NOT NULL,
                        frequency INTEGER DEFAULT 1,
                        trend_score REAL DEFAULT 0.0,
                        last_seen DATE,
                        category TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                conn.commit()
                logger.info("✅ تم إنشاء جداول التحليلات بنجاح")
                
        except Exception as e:
            logger.error("❌ فشل في إنشاء جداول التحليلات", e)
    
    def analyze_article_performance(self, article: Dict) -> Dict:
        """تحليل أداء مقال واحد"""
        try:
            content = article.get('content', '')
            title = article.get('title', '')
            keywords = article.get('keywords', [])
            
            # حساب مقاييس الأداء
            word_count = len(content.split())
            keyword_count = len(keywords)
            
            # حساب نقاط القابلية للقراءة
            readability_score = self._calculate_readability_score(content)
            
            # حساب إمكانية التفاعل
            engagement_potential = self._calculate_engagement_potential(article)
            
            # حساب نقاط الجودة الإجمالية
            quality_score = article.get('quality_review', {}).get('quality_score', 0)
            seo_score = article.get('seo_score', 0)
            
            analytics_data = {
                'word_count': word_count,
                'keyword_count': keyword_count,
                'readability_score': readability_score,
                'engagement_potential': engagement_potential,
                'quality_score': quality_score,
                'seo_score': seo_score,
                'overall_score': (quality_score + seo_score + readability_score + engagement_potential) / 4
            }
            
            # حفظ في قاعدة البيانات
            self._save_content_analytics(article.get('id'), article.get('content_type'), analytics_data)
            
            return analytics_data
            
        except Exception as e:
            logger.error("❌ فشل في تحليل أداء المقال", e)
            return {}
    
    def _calculate_readability_score(self, content: str) -> float:
        """حساب نقاط القابلية للقراءة"""
        if not content:
            return 0.0
        
        # إزالة HTML tags
        clean_content = content.replace('<p>', '').replace('</p>', '')
        clean_content = clean_content.replace('<strong>', '').replace('</strong>', '')
        clean_content = clean_content.replace('<h3>', '').replace('</h3>', '')
        
        sentences = clean_content.split('.')
        words = clean_content.split()
        
        if not sentences or not words:
            return 0.0
        
        # حساب متوسط طول الجملة
        avg_sentence_length = len(words) / len(sentences)
        
        # حساب متوسط طول الكلمة
        avg_word_length = sum(len(word) for word in words) / len(words)
        
        # نقاط القابلية للقراءة (كلما قل الرقم، كانت أسهل للقراءة)
        readability = 100 - (avg_sentence_length * 1.5) - (avg_word_length * 2)
        
        return max(0, min(100, readability))
    
    def _calculate_engagement_potential(self, article: Dict) -> float:
        """حساب إمكانية التفاعل مع المقال"""
        score = 0.0
        content = article.get('content', '').lower()
        title = article.get('title', '').lower()
        
        # عوامل التفاعل الإيجابية
        engagement_factors = {
            'questions': ['؟', 'ما رأيكم', 'هل تعتقد', 'كيف ترى'],
            'call_to_action': ['شاركنا', 'أخبرونا', 'تعليق', 'رأيك'],
            'emotional_words': ['مذهل', 'رائع', 'مثير', 'مدهش', 'لا يصدق'],
            'urgency': ['عاجل', 'حصري', 'جديد', 'أول مرة', 'لا تفوت'],
            'lists': ['قائمة', 'أفضل', 'أسوأ', 'ترتيب', 'مقارنة']
        }
        
        for factor_type, keywords in engagement_factors.items():
            factor_score = sum(1 for keyword in keywords if keyword in content or keyword in title)
            score += factor_score * 10  # كل عامل يضيف 10 نقاط
        
        # إضافة نقاط للصور
        if article.get('image_urls'):
            score += 15
        
        # إضافة نقاط لطول المحتوى المناسب
        word_count = len(content.split())
        if 300 <= word_count <= 800:
            score += 20
        
        return min(100, score)
    
    def _save_content_analytics(self, article_id: int, content_type: str, analytics_data: Dict):
        """حفظ تحليلات المحتوى"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO content_analytics 
                    (article_id, content_type, quality_score, seo_score, word_count, 
                     keyword_count, readability_score, engagement_potential)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    article_id,
                    content_type,
                    analytics_data.get('quality_score', 0),
                    analytics_data.get('seo_score', 0),
                    analytics_data.get('word_count', 0),
                    analytics_data.get('keyword_count', 0),
                    analytics_data.get('readability_score', 0.0),
                    analytics_data.get('engagement_potential', 0.0)
                ))
                
                conn.commit()
                
        except Exception as e:
            logger.error("❌ فشل في حفظ تحليلات المحتوى", e)
    
    def generate_daily_report(self, date: str = None) -> Dict:
        """إنشاء تقرير يومي شامل"""
        if not date:
            date = datetime.now().strftime('%Y-%m-%d')
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # إحصائيات المقالات المنشورة
                cursor.execute('''
                    SELECT COUNT(*), AVG(view_count), AVG(engagement_score)
                    FROM published_articles 
                    WHERE DATE(published_at) = ?
                ''', (date,))
                
                article_stats = cursor.fetchone()
                
                # إحصائيات التحليلات
                cursor.execute('''
                    SELECT AVG(quality_score), AVG(seo_score), AVG(readability_score), 
                           AVG(engagement_potential), AVG(word_count)
                    FROM content_analytics ca
                    JOIN published_articles pa ON ca.article_id = pa.id
                    WHERE DATE(pa.published_at) = ?
                ''', (date,))
                
                analytics_stats = cursor.fetchone()
                
                # أفضل الكلمات المفتاحية
                cursor.execute('''
                    SELECT keywords FROM published_articles 
                    WHERE DATE(published_at) = ?
                ''', (date,))
                
                keywords_data = cursor.fetchall()
                all_keywords = []
                for row in keywords_data:
                    if row[0]:
                        keywords = json.loads(row[0])
                        all_keywords.extend(keywords)
                
                # حساب تكرار الكلمات المفتاحية
                keyword_freq = {}
                for keyword in all_keywords:
                    keyword_freq[keyword] = keyword_freq.get(keyword, 0) + 1
                
                top_keywords = sorted(keyword_freq.items(), key=lambda x: x[1], reverse=True)[:10]
                
                report = {
                    'date': date,
                    'articles_published': article_stats[0] if article_stats[0] else 0,
                    'avg_views': round(article_stats[1] if article_stats[1] else 0, 2),
                    'avg_engagement': round(article_stats[2] if article_stats[2] else 0, 2),
                    'avg_quality_score': round(analytics_stats[0] if analytics_stats[0] else 0, 2),
                    'avg_seo_score': round(analytics_stats[1] if analytics_stats[1] else 0, 2),
                    'avg_readability': round(analytics_stats[2] if analytics_stats[2] else 0, 2),
                    'avg_engagement_potential': round(analytics_stats[3] if analytics_stats[3] else 0, 2),
                    'avg_word_count': round(analytics_stats[4] if analytics_stats[4] else 0, 2),
                    'top_keywords': top_keywords,
                    'generated_at': datetime.now().isoformat()
                }
                
                # حفظ التقرير اليومي
                self._save_daily_analytics(report)
                
                return report
                
        except Exception as e:
            logger.error("❌ فشل في إنشاء التقرير اليومي", e)
            return {}
    
    def _save_daily_analytics(self, report: Dict):
        """حفظ التحليلات اليومية"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO daily_analytics 
                    (date, articles_published, avg_quality_score, avg_seo_score, 
                     total_words, avg_words_per_article, top_keywords, success_rate)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    report['date'],
                    report['articles_published'],
                    report['avg_quality_score'],
                    report['avg_seo_score'],
                    report['avg_word_count'] * report['articles_published'],
                    report['avg_word_count'],
                    json.dumps(report['top_keywords'], ensure_ascii=False),
                    (report['avg_quality_score'] + report['avg_seo_score']) / 2
                ))
                
                conn.commit()
                
        except Exception as e:
            logger.error("❌ فشل في حفظ التحليلات اليومية", e)
    
    def get_performance_trends(self, days: int = 7) -> Dict:
        """الحصول على اتجاهات الأداء"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
                
                cursor.execute('''
                    SELECT date, articles_published, avg_quality_score, avg_seo_score, success_rate
                    FROM daily_analytics 
                    WHERE date >= ?
                    ORDER BY date
                ''', (start_date,))
                
                trends_data = cursor.fetchall()
                
                if not trends_data:
                    return {}
                
                # حساب الاتجاهات
                dates = [row[0] for row in trends_data]
                articles = [row[1] for row in trends_data]
                quality_scores = [row[2] for row in trends_data]
                seo_scores = [row[3] for row in trends_data]
                success_rates = [row[4] for row in trends_data]
                
                return {
                    'period_days': days,
                    'dates': dates,
                    'trends': {
                        'articles_published': {
                            'data': articles,
                            'avg': statistics.mean(articles) if articles else 0,
                            'trend': 'increasing' if articles[-1] > articles[0] else 'decreasing' if len(articles) > 1 else 'stable'
                        },
                        'quality_scores': {
                            'data': quality_scores,
                            'avg': statistics.mean(quality_scores) if quality_scores else 0,
                            'trend': 'improving' if quality_scores[-1] > quality_scores[0] else 'declining' if len(quality_scores) > 1 else 'stable'
                        },
                        'seo_scores': {
                            'data': seo_scores,
                            'avg': statistics.mean(seo_scores) if seo_scores else 0,
                            'trend': 'improving' if seo_scores[-1] > seo_scores[0] else 'declining' if len(seo_scores) > 1 else 'stable'
                        }
                    }
                }
                
        except Exception as e:
            logger.error("❌ فشل في الحصول على اتجاهات الأداء", e)
            return {}

# إنشاء مثيل عام للتحليلات
analytics = PerformanceAnalytics()
