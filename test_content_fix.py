#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة عدم التطابق بين العنوان والمحتوى
"""

import sys
import os
import asyncio
from datetime import datetime

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.content_generator import ContentGenerator
from modules.logger import logger

async def test_content_generation():
    """اختبار توليد المحتوى مع التحسينات الجديدة"""
    
    print("🧪 بدء اختبار إصلاح مشكلة التطابق بين العنوان والمحتوى...")
    
    try:
        # إنشاء مولد المحتوى
        content_generator = ContentGenerator()
        
        # حالات اختبار مختلفة
        test_cases = [
            {
                "name": "دليل للمبتدئين",
                "source_content": {
                    "title": "دليل شامل للمبتدئين في عالم الألعاب",
                    "content": "هذا دليل يساعد المبتدئين على فهم أساسيات الألعاب وكيفية البدء في عالم الألعاب الرقمية.",
                    "summary": "دليل مفيد للمبتدئين في الألعاب",
                    "keywords": ["دليل الألعاب", "مبتدئين", "gaming guide", "beginner tips"]
                }
            },
            {
                "name": "تحديث Nintendo Switch",
                "source_content": {
                    "title": "تحديث جديد: Nintendo Switch - آخر التطورات",
                    "content": "Nintendo Switch حصل على تحديث جديد يتضمن ميزات محسنة وإصلاحات للأخطاء.",
                    "summary": "تحديث جديد لجهاز Nintendo Switch",
                    "keywords": ["Nintendo Switch", "تحديث", "gaming update", "console news"]
                }
            },
            {
                "name": "مراجعة لعبة",
                "source_content": {
                    "title": "مراجعة شاملة للعبة Cyberpunk 2077",
                    "content": "مراجعة مفصلة للعبة Cyberpunk 2077 تغطي الجرافيك والقصة وطريقة اللعب.",
                    "summary": "مراجعة كاملة للعبة Cyberpunk 2077",
                    "keywords": ["Cyberpunk 2077", "مراجعة", "game review", "RPG"]
                }
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{'='*60}")
            print(f"🎮 اختبار {i}: {test_case['name']}")
            print(f"{'='*60}")
            
            # توليد المقال
            article = content_generator.generate_article(
                test_case["source_content"], 
                "أخبار_الألعاب", 
                "standard"
            )
            
            if article and 'error' not in article:
                print(f"✅ تم توليد المقال بنجاح")
                print(f"📝 العنوان: {article['title']}")
                print(f"📊 نقاط الجودة: {article.get('quality_review', {}).get('quality_score', 'غير محدد')}")
                
                # فحص التطابق
                quality_review = article.get('quality_review', {})
                title_match = quality_review.get('title_content_match', True)
                
                if title_match:
                    print(f"✅ التطابق بين العنوان والمحتوى: ممتاز")
                else:
                    print(f"❌ التطابق بين العنوان والمحتوى: يحتاج تحسين")
                    print(f"🔍 المشاكل: {quality_review.get('issues', [])}")
                
                # عرض جزء من المحتوى
                content_preview = article.get('content', '')[:200] + "..."
                print(f"📄 معاينة المحتوى: {content_preview}")
                
                # فحص الموافقة
                approved = quality_review.get('approved', False)
                if approved:
                    print(f"✅ المقال معتمد للنشر")
                else:
                    print(f"❌ المقال مرفوض - يحتاج تحسين")
                    print(f"💡 اقتراحات: {quality_review.get('suggestions', [])}")
                
            elif article and 'error' in article:
                print(f"⚠️ رفض توليد المقال: {article['error']}")
            else:
                print(f"❌ فشل في توليد المقال")
            
            print(f"\n{'='*60}")
        
        print(f"\n🎉 انتهى الاختبار بنجاح!")
        print(f"📊 تم اختبار {len(test_cases)} حالات مختلفة")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار إصلاحات وكيل أليكس...")
    
    # تشغيل الاختبار
    asyncio.run(test_content_generation())

if __name__ == "__main__":
    main()
