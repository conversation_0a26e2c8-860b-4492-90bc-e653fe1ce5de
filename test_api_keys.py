#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مفاتيح API المحدثة
"""

import asyncio
import sys
import os

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.web_search import WebSearch
from modules.image_guard import AIImageGenerator
from modules.content_scraper import ContentScraper
from config.settings import BotConfig

async def test_google_search_api():
    """اختبار Google Search API الجديد"""
    print("\n🔍 اختبار Google Search API...")
    
    try:
        web_search = WebSearch(BotConfig.GOOGLE_SEARCH_ENGINE_ID)
        
        if not web_search.enabled:
            print("❌ Google Search API غير مفعل")
            return False
            
        # اختبار بحث بسيط
        results = web_search.search("gaming news today", num_results=3)
        
        if results and len(results) > 0:
            print(f"✅ Google Search API يعمل بنجاح - تم العثور على {len(results)} نتيجة")
            for i, result in enumerate(results[:2], 1):
                print(f"   {i}. {result.get('title', 'بدون عنوان')[:60]}...")
            return True
        else:
            print("❌ Google Search API لا يعيد نتائج")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في Google Search API: {e}")
        return False

async def test_freepik_api():
    """اختبار Freepik API الجديد"""
    print("\n🎨 اختبار Freepik API...")
    
    try:
        ai_image_generator = AIImageGenerator()
        
        if not ai_image_generator.freepik_api_key:
            print("❌ مفتاح Freepik API غير متوفر")
            return False
            
        # اختبار إنشاء صورة بسيطة
        test_prompt = {
            'prompt': 'A simple gaming controller, digital art style, high quality',
            'category': 'test'
        }
        
        result = await ai_image_generator._generate_with_freepik(test_prompt)
        
        if result and result.get('url'):
            print(f"✅ Freepik API يعمل بنجاح")
            print(f"   الرابط: {result['url']}")
            print(f"   الوصف: {result.get('description', 'غير متوفر')}")
            return True
        else:
            print("❌ Freepik API لا يعيد صور")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في Freepik API: {e}")
        return False

async def test_fluxai_api():
    """اختبار FluxAI API"""
    print("\n🎨 اختبار FluxAI API...")
    
    try:
        ai_image_generator = AIImageGenerator()
        
        # اختبار إنشاء صورة بسيطة
        test_prompt = {
            'prompt': 'A gaming setup with RGB lighting, modern style',
            'category': 'test'
        }
        
        result = await ai_image_generator._generate_with_fluxai(test_prompt)
        
        if result and result.get('url'):
            print(f"✅ FluxAI API يعمل بنجاح")
            print(f"   الرابط: {result['url']}")
            print(f"   الوصف: {result.get('description', 'غير متوفر')}")
            return True
        else:
            print("⚠️ FluxAI API لا يعمل حالياً (مشاكل في الخدمة)")
            return False
            
    except Exception as e:
        print(f"⚠️ خطأ في FluxAI API: {e}")
        return False

def test_rss_feeds():
    """اختبار RSS feeds المحدثة"""
    print("\n📡 اختبار RSS feeds...")
    
    try:
        scraper = ContentScraper()
        
        # اختبار جلب RSS feeds
        rss_articles = scraper.extract_from_rss_feeds()
        
        if rss_articles and len(rss_articles) > 0:
            print(f"✅ RSS feeds تعمل بنجاح - تم جمع {len(rss_articles)} مقال")
            for i, article in enumerate(rss_articles[:3], 1):
                print(f"   {i}. {article.get('title', 'بدون عنوان')[:60]}...")
            return True
        else:
            print("⚠️ RSS feeds لا تعيد مقالات (قد تكون طبيعية إذا لم تكن هناك مقالات جديدة)")
            return True  # نعتبرها نجاح لأن عدم وجود مقالات جديدة أمر طبيعي
            
    except Exception as e:
        print(f"❌ خطأ في RSS feeds: {e}")
        return False

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 بدء اختبار مفاتيح API المحدثة...")
    print("=" * 60)
    
    # اختبار جميع APIs
    tests = [
        ("Google Search API", test_google_search_api()),
        ("Freepik API", test_freepik_api()),
        ("FluxAI API", test_fluxai_api()),
        ("RSS Feeds", test_rss_feeds())
    ]
    
    results = {}
    
    for test_name, test_coro in tests:
        try:
            if asyncio.iscoroutine(test_coro):
                result = await test_coro
            else:
                result = test_coro
            results[test_name] = result
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results[test_name] = False
    
    # عرض النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار:")
    print("-" * 30)
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {test_name}: {status}")
    
    print("-" * 30)
    print(f"النتيجة النهائية: {passed_tests}/{total_tests} اختبارات نجحت")
    
    if passed_tests >= 3:  # نحتاج على الأقل 3 من 4 اختبارات تنجح
        print("🎉 معظم APIs تعمل بنجاح!")
        return True
    else:
        print("⚠️ هناك مشاكل في بعض APIs تحتاج إصلاح")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
