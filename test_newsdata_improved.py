#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار NewsData.io API المحسن
"""

import asyncio
import sys
import os

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.advanced_news_apis import AdvancedNewsAPIs

async def test_improved_newsdata():
    """اختبار NewsData.io API المحسن"""
    print("🧪 اختبار NewsData.io API المحسن...")
    print("=" * 60)
    
    # إنشاء مثيل من AdvancedNewsAPIs
    news_apis = AdvancedNewsAPIs()
    
    if not news_apis.newsdata_key:
        print("❌ مفتاح NewsData.io API غير موجود")
        return False
    
    print(f"🔑 المفتاح: {news_apis.newsdata_key[:10]}...")
    
    # اختبار البحث المحسن
    try:
        print("\n🔍 اختبار البحث المحسن...")
        
        # كلمات مفتاحية للاختبار
        test_keywords = ['gaming', 'video games', 'esports']
        
        # البحث باستخدام الدالة المحسنة
        articles = await news_apis._search_newsdata(
            keywords=test_keywords,
            max_results=15,
            days_back=7
        )
        
        print(f"📊 النتائج:")
        print(f"   عدد المقالات المُرجعة: {len(articles)}")
        
        if articles:
            print(f"\n📰 عينة من المقالات عالية الجودة:")
            
            for i, article in enumerate(articles[:5], 1):
                print(f"\n   {i}. العنوان: {article['title'][:60]}...")
                print(f"      المصدر: {article['source']}")
                print(f"      نقاط الجودة: {article.get('quality_score', 'غير محدد')}/10")
                print(f"      نقاط الصلة بالألعاب: {article.get('gaming_relevance_score', 'غير محدد')}")
                print(f"      الرابط: {article['url']}")
            
            # تحليل الجودة
            quality_scores = [article.get('quality_score', 0) for article in articles]
            gaming_scores = [article.get('gaming_relevance_score', 0) for article in articles]
            
            avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0
            avg_gaming = sum(gaming_scores) / len(gaming_scores) if gaming_scores else 0
            
            print(f"\n📊 تحليل الجودة:")
            print(f"   متوسط نقاط الجودة: {avg_quality:.1f}/10")
            print(f"   متوسط نقاط الصلة بالألعاب: {avg_gaming:.1f}")
            
            # فحص المصادر
            sources = {}
            for article in articles:
                source = article['source']
                sources[source] = sources.get(source, 0) + 1
            
            print(f"\n📡 المصادر:")
            for source, count in sorted(sources.items(), key=lambda x: x[1], reverse=True):
                print(f"   {source}: {count} مقال")
            
            return True
            
        else:
            print("⚠️ لم يتم العثور على مقالات متعلقة بالألعاب")
            
            # اختبار تشخيصي
            print("\n🔍 اختبار تشخيصي...")
            
            # اختبار بحث أساسي بدون فلترة
            import aiohttp
            
            params = {
                'apikey': news_apis.newsdata_key,
                'q': 'gaming',
                'language': 'en',
                'size': 10
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get('https://newsdata.io/api/1/news', params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        raw_articles = data.get('results', [])
                        
                        print(f"📊 مقالات خام من API: {len(raw_articles)}")
                        
                        if raw_articles:
                            print(f"\n📄 عينة من المقالات الخام:")
                            for i, article in enumerate(raw_articles[:3], 1):
                                title = article.get('title', 'بدون عنوان')
                                description = article.get('description', 'بدون وصف')
                                print(f"   {i}. {title[:50]}...")
                                print(f"      الوصف: {description[:50]}...")
                                
                                # فحص سبب الرفض
                                full_text = f"{title} {description}".lower()
                                gaming_keywords = ['video game', 'gaming', 'esports', 'gamer']
                                found_keywords = [kw for kw in gaming_keywords if kw in full_text]
                                
                                print(f"      كلمات الألعاب الموجودة: {found_keywords}")
                    else:
                        print(f"❌ خطأ في API: {response.status}")
            
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

async def test_newsdata_comprehensive():
    """اختبار شامل لـ NewsData.io"""
    print("\n🔍 اختبار شامل لجميع APIs الأخبار...")
    
    news_apis = AdvancedNewsAPIs()
    
    try:
        # البحث الشامل
        articles = await news_apis.search_gaming_news_comprehensive(
            keywords=['gaming news', 'video game updates'],
            max_articles=20,
            days_back=7
        )
        
        print(f"📊 البحث الشامل:")
        print(f"   إجمالي المقالات: {len(articles)}")
        
        # تحليل المصادر
        source_types = {}
        for article in articles:
            source_type = article.get('source_type', 'unknown')
            source_types[source_type] = source_types.get(source_type, 0) + 1
        
        print(f"\n📡 توزيع المصادر:")
        for source_type, count in source_types.items():
            print(f"   {source_type}: {count} مقال")
        
        # إحصائيات الاستخدام
        stats = news_apis.get_usage_stats()
        print(f"\n📊 إحصائيات الاستخدام:")
        for api, calls in stats.items():
            if calls > 0:
                print(f"   {api}: {calls} استدعاء")
        
        return len(articles) > 0
        
    except Exception as e:
        print(f"❌ خطأ في البحث الشامل: {e}")
        return False

async def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار NewsData.io API المحسن...")
    
    # اختبار NewsData.io المحسن
    newsdata_success = await test_improved_newsdata()
    
    # اختبار شامل
    comprehensive_success = await test_newsdata_comprehensive()
    
    print("\n" + "=" * 60)
    print("📋 ملخص النتائج:")
    print("-" * 30)
    
    print(f"NewsData.io المحسن: {'✅ نجح' if newsdata_success else '❌ فشل'}")
    print(f"البحث الشامل: {'✅ نجح' if comprehensive_success else '❌ فشل'}")
    
    if newsdata_success:
        print("\n🎉 تم إصلاح NewsData.io API بنجاح!")
        print("💡 التحسينات المطبقة:")
        print("   • استراتيجيات بحث محسنة للألعاب")
        print("   • فلترة أكثر دقة للمحتوى")
        print("   • نظام تقييم جودة متقدم")
        print("   • استهداف مصادر موثوقة")
        
        return True
    else:
        print("\n⚠️ NewsData.io API يحتاج مزيد من التحسين")
        print("🔧 خطوات إضافية مقترحة:")
        print("   • مراجعة معاملات البحث")
        print("   • تحسين كلمات الفلترة")
        print("   • إضافة مصادر أخرى")
        
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
