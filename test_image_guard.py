#!/usr/bin/env python3
# اختبار نظام ImageGuard Pro للصور الآمنة
import asyncio
import sys
import os

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.image_guard import image_guard
from modules.logger import logger

async def test_image_search():
    """اختبار البحث عن الصور الآمنة"""
    print("🧪 بدء اختبار نظام ImageGuard Pro")
    print("=" * 50)
    
    # اختبارات مختلفة
    test_cases = [
        {
            'prompt': 'صورة لعبة فيديو حديثة',
            'article_topic': 'أفضل ألعاب 2025',
            'expected': 'gaming'
        },
        {
            'prompt': 'جهاز تحكم ألعاب',
            'article_topic': 'مراجعة PlayStation 5',
            'expected': 'controller'
        },
        {
            'prompt': 'إعداد ألعاب احترافي',
            'article_topic': 'أفضل إعدادات الألعاب',
            'expected': 'gaming setup'
        },
        {
            'prompt': 'لوحة مفاتيح ألعاب',
            'article_topic': 'أفضل أجهزة الألعاب',
            'expected': 'gaming keyboard'
        }
    ]
    
    successful_tests = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 اختبار {i}/{total_tests}: {test_case['prompt']}")
        print(f"📝 موضوع المقال: {test_case['article_topic']}")
        
        try:
            # البحث عن صورة آمنة
            result = await image_guard.search_safe_image(
                test_case['prompt'], 
                test_case['article_topic']
            )
            
            if result:
                print(f"✅ نجح الاختبار!")
                print(f"🖼️ المصدر: {result.get('source', 'غير محدد')}")
                print(f"📄 الوصف: {result.get('description', 'غير محدد')}")
                print(f"⚖️ الرخصة: {result.get('license', 'غير محدد')}")
                print(f"📝 النسب: {result.get('attribution', 'غير محدد')}")
                print(f"🛡️ آمن لـ AdSense: {result.get('safe_for_adsense', False)}")
                print(f"💼 استخدام تجاري: {result.get('commercial_use', False)}")
                print(f"🔗 الرابط: {result.get('url', 'غير محدد')}")
                
                # فحص الأمان
                if result.get('safe_for_adsense') and result.get('commercial_use'):
                    print("🟢 الصورة آمنة ومناسبة للاستخدام التجاري")
                    successful_tests += 1
                else:
                    print("🟡 تحذير: الصورة قد لا تكون آمنة بالكامل")
            else:
                print("❌ فشل الاختبار - لم يتم العثور على صورة")
                
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
        
        print("-" * 30)
    
    # عرض النتائج النهائية
    print(f"\n📊 نتائج الاختبار:")
    print(f"✅ اختبارات ناجحة: {successful_tests}/{total_tests}")
    print(f"📈 معدل النجاح: {(successful_tests/total_tests)*100:.1f}%")
    
    # عرض إحصائيات الاستخدام
    stats = image_guard.get_usage_stats()
    print(f"\n📊 إحصائيات الاستخدام:")
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    return successful_tests == total_tests

async def test_safety_filters():
    """اختبار مرشحات الأمان"""
    print("\n🛡️ اختبار مرشحات الأمان")
    print("=" * 50)
    
    # اختبارات الكلمات المحظورة
    unsafe_prompts = [
        'صورة عنيفة للعبة',
        'لعبة تحتوي على دماء',
        'صورة كحول في اللعبة',
        'لعبة قمار'
    ]
    
    safe_results = 0
    
    for prompt in unsafe_prompts:
        print(f"\n🔍 اختبار الأمان: {prompt}")
        
        try:
            result = await image_guard.search_safe_image(prompt, '')
            
            if result:
                # فحص ما إذا كانت النتيجة آمنة
                description = result.get('description', '').lower()
                is_safe = True
                
                # فحص الكلمات المحظورة في الوصف
                forbidden_words = ['violence', 'blood', 'alcohol', 'gambling']
                for word in forbidden_words:
                    if word in description:
                        is_safe = False
                        break
                
                if is_safe and result.get('safe_for_adsense', False):
                    print("✅ المرشح يعمل بشكل صحيح - تم إرجاع صورة آمنة")
                    safe_results += 1
                else:
                    print("⚠️ تحذير: تم إرجاع صورة قد تكون غير آمنة")
            else:
                print("✅ المرشح يعمل بشكل صحيح - لم يتم إرجاع صورة")
                safe_results += 1
                
        except Exception as e:
            print(f"❌ خطأ في اختبار الأمان: {e}")
    
    print(f"\n📊 نتائج اختبار الأمان:")
    print(f"✅ اختبارات آمنة: {safe_results}/{len(unsafe_prompts)}")
    
    return safe_results == len(unsafe_prompts)

async def test_fallback_system():
    """اختبار نظام الصور الاحتياطية"""
    print("\n🔄 اختبار نظام الصور الاحتياطية")
    print("=" * 50)
    
    # محاولة البحث بكلمات غير مفيدة
    result = await image_guard.search_safe_image('كلمات غير مفيدة تماماً', '')
    
    if result:
        if result.get('source') in ['Fallback', 'Emergency Fallback']:
            print("✅ نظام الصور الاحتياطية يعمل بشكل صحيح")
            print(f"🖼️ الصورة الاحتياطية: {result.get('description', '')}")
            return True
        else:
            print("🟡 تم العثور على صورة من مصدر عادي")
            return True
    else:
        print("❌ فشل نظام الصور الاحتياطية")
        return False

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار شامل لنظام ImageGuard Pro")
    print("=" * 60)
    
    try:
        # تشغيل جميع الاختبارات
        test1 = await test_image_search()
        test2 = await test_safety_filters()
        test3 = await test_fallback_system()
        
        # النتائج النهائية
        print("\n" + "=" * 60)
        print("📋 ملخص نتائج الاختبار:")
        print(f"🔍 اختبار البحث عن الصور: {'✅ نجح' if test1 else '❌ فشل'}")
        print(f"🛡️ اختبار مرشحات الأمان: {'✅ نجح' if test2 else '❌ فشل'}")
        print(f"🔄 اختبار النظام الاحتياطي: {'✅ نجح' if test3 else '❌ فشل'}")
        
        if test1 and test2 and test3:
            print("\n🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
            return True
        else:
            print("\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الإعدادات.")
            return False
            
    except Exception as e:
        print(f"\n❌ خطأ عام في الاختبار: {e}")
        return False

if __name__ == "__main__":
    try:
        # تشغيل الاختبارات
        success = asyncio.run(main())
        
        if success:
            print("\n✅ اختبار ImageGuard Pro اكتمل بنجاح!")
            sys.exit(0)
        else:
            print("\n❌ فشل في اختبار ImageGuard Pro!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⌨️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
