#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مفصل لـ Freepik API
"""

import asyncio
import aiohttp
import sys
import os

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from config.settings import BotConfig

async def test_freepik_api_detailed():
    """اختبار مفصل لـ Freepik API"""
    print("🎨 اختبار مفصل لـ Freepik API...")
    print(f"المفتاح المستخدم: {BotConfig.FREEPIK_API_KEY[:10]}...")
    
    # إعداد طلب Freepik API - استخدام header الصحيح
    headers = {
        'x-freepik-api-key': BotConfig.FREEPIK_API_KEY,
        'Content-Type': 'application/json',
        'User-Agent': 'GamingNewsBot/1.0'
    }
    
    # اختبار Freepik Mystic API الصحيح
    endpoints_to_test = [
        'https://api.freepik.com/v1/ai/mystic'
    ]

    test_payload = {
        'prompt': 'A simple gaming controller on a dark background, digital art style',
        'resolution': '2k',
        'aspect_ratio': 'square_1_1',
        'model': 'realism',
        'filter_nsfw': True,
        'fixed_generation': False
    }
    
    timeout = aiohttp.ClientTimeout(total=60, connect=30)
    
    for endpoint in endpoints_to_test:
        print(f"\n🔍 اختبار endpoint: {endpoint}")
        
        try:
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(
                    endpoint,
                    headers=headers,
                    json=test_payload
                ) as response:
                    
                    print(f"   📊 كود الاستجابة: {response.status}")
                    
                    if response.status == 200:
                        try:
                            result = await response.json()
                            print(f"   ✅ نجح! تم الحصول على استجابة JSON")
                            print(f"   📋 مفاتيح الاستجابة: {list(result.keys())}")
                            print(f"   📄 محتوى الاستجابة: {result}")

                            # Freepik Mystic API يعيد task_id داخل data
                            if 'data' in result and result['data'].get('task_id'):
                                task_id = result['data']['task_id']
                                status = result['data'].get('status', 'غير محدد')
                                print(f"   🎯 تم إنشاء مهمة بنجاح: {task_id}")
                                print(f"   📊 حالة المهمة: {status}")

                                # في التطبيق الحقيقي، يجب استخدام webhook أو polling للحصول على النتيجة
                                print(f"   ✅ Freepik API يعمل! (المهمة تم إنشاؤها بنجاح)")
                                return True, endpoint, result
                            else:
                                print(f"   ⚠️ لم يتم العثور على task_id في الاستجابة")
                                # قد تكون الاستجابة مختلفة، دعنا نتحقق من المحتوى
                                if 'data' in result:
                                    print(f"   📊 محتوى data: {result['data']}")
                                    if result['data']:
                                        print(f"   ✅ Freepik API يعمل! (تم الحصول على بيانات)")
                                        return True, endpoint, result
                                
                        except Exception as e:
                            print(f"   ❌ خطأ في تحليل JSON: {e}")
                            response_text = await response.text()
                            print(f"   📄 نص الاستجابة: {response_text[:200]}...")
                            
                    elif response.status == 401:
                        print(f"   ❌ خطأ مصادقة (401) - المفتاح قد يكون غير صحيح")
                        response_text = await response.text()
                        print(f"   📄 تفاصيل الخطأ: {response_text}")
                        
                    elif response.status == 403:
                        print(f"   ❌ خطأ صلاحيات (403) - المفتاح قد لا يملك الصلاحيات المطلوبة")
                        response_text = await response.text()
                        print(f"   📄 تفاصيل الخطأ: {response_text}")
                        
                    elif response.status == 404:
                        print(f"   ⚠️ Endpoint غير موجود (404)")
                        
                    else:
                        print(f"   ⚠️ كود استجابة غير متوقع: {response.status}")
                        response_text = await response.text()
                        print(f"   📄 نص الاستجابة: {response_text[:200]}...")
                        
        except aiohttp.ClientConnectorError as e:
            print(f"   ❌ فشل الاتصال: {e}")
        except asyncio.TimeoutError:
            print(f"   ⏰ انتهت مهلة الاتصال")
        except Exception as e:
            print(f"   ❌ خطأ غير متوقع: {e}")
    
    return False, None, None

async def test_freepik_auth():
    """اختبار مصادقة Freepik API"""
    print("\n🔐 اختبار مصادقة Freepik API...")
    
    headers = {
        'x-freepik-api-key': BotConfig.FREEPIK_API_KEY,
        'Content-Type': 'application/json'
    }
    
    # اختبار endpoint بسيط للمصادقة
    auth_endpoints = [
        'https://api.freepik.com/v1/user',
        'https://api.freepik.com/v1/account',
        'https://api.freepik.com/user',
        'https://api.freepik.com/account'
    ]
    
    timeout = aiohttp.ClientTimeout(total=30, connect=15)
    
    for endpoint in auth_endpoints:
        print(f"🔍 اختبار مصادقة: {endpoint}")
        
        try:
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(endpoint, headers=headers) as response:
                    print(f"   📊 كود الاستجابة: {response.status}")
                    
                    if response.status == 200:
                        print(f"   ✅ المصادقة نجحت!")
                        try:
                            result = await response.json()
                            print(f"   📋 معلومات الحساب: {list(result.keys())}")
                            return True
                        except:
                            print(f"   ✅ المصادقة نجحت (استجابة غير JSON)")
                            return True
                    elif response.status == 401:
                        print(f"   ❌ فشل المصادقة - المفتاح غير صحيح")
                    elif response.status == 404:
                        print(f"   ⚠️ Endpoint غير موجود")
                    else:
                        response_text = await response.text()
                        print(f"   📄 استجابة: {response_text[:100]}...")
                        
        except Exception as e:
            print(f"   ❌ خطأ: {e}")
    
    return False

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 بدء اختبار مفصل لـ Freepik API...")
    print("=" * 60)
    
    if not BotConfig.FREEPIK_API_KEY:
        print("❌ مفتاح Freepik API غير موجود في التكوين")
        return False
    
    # اختبار المصادقة أولاً
    auth_success = await test_freepik_auth()
    
    if not auth_success:
        print("\n⚠️ فشل اختبار المصادقة، لكن سنحاول اختبار إنشاء الصور...")
    
    # اختبار إنشاء الصور
    success, working_endpoint, result = await test_freepik_api_detailed()
    
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار:")
    print("-" * 30)
    
    if success:
        print("🎉 Freepik API يعمل بنجاح!")
        print(f"✅ Endpoint الذي يعمل: {working_endpoint}")
        print("✅ تم إنشاء صورة بنجاح")
        return True
    else:
        print("❌ Freepik API لا يعمل حالياً")
        print("\n💡 الأسباب المحتملة:")
        print("   • المفتاح قد يكون منتهي الصلاحية")
        print("   • المفتاح قد لا يملك صلاحيات إنشاء الصور")
        print("   • API endpoint قد يكون تغير")
        print("   • قد تكون هناك قيود على الاستخدام")
        
        print("\n🔧 الحلول المقترحة:")
        print("   • تحقق من صحة المفتاح في لوحة تحكم Freepik")
        print("   • تأكد من أن المفتاح يملك صلاحيات Text-to-Image")
        print("   • راجع وثائق Freepik API للتأكد من endpoints الصحيحة")
        
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
