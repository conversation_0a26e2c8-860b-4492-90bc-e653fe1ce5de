# 🎥 تحديث نظام YouTube المتقدم - ملخص التطوير

## 📋 نظرة عامة على التحديث

تم تطوير نظام متكامل جديد يجعل الوكيل يبحث **أولاً** في قنوات YouTube المحددة قبل أي مصدر آخر، مع استخدام تقنية Whisper لاستخراج النصوص ونظام موافقة تفاعلي عبر Telegram.

## 🆕 الملفات الجديدة المضافة

### 1. الملفات الأساسية
```
modules/
├── advanced_youtube_analyzer.py     # محلل YouTube المتقدم مع Whisper
├── video_approval_system.py         # نظام الموافقة التفاعلي
└── database.py                      # محسن بجداول جديدة

config/
└── settings.py                      # محسن بإعدادات YouTube الجديدة
```

### 2. ملف<PERSON><PERSON> الاختبار والإعداد
```
test_youtube_priority_system.py      # اختبار شامل للنظام
quick_test_youtube_system.py         # اختبار سريع
setup_youtube_priority.py            # إعداد تفاعلي للنظام
```

### 3. ملفات التوثيق
```
YOUTUBE_PRIORITY_SYSTEM.md           # دليل النظام الجديد
YOUTUBE_PRIORITY_UPDATE_SUMMARY.md   # هذا الملف
.env.youtube.example                 # مثال للإعدادات البيئية
```

## 🔧 التعديلات على الملفات الموجودة

### 1. main.py
- ✅ إضافة استيراد المكونات الجديدة
- ✅ تحديث دالة `__init__` لتشمل المحلل المتقدم ونظام الموافقة
- ✅ تحديث `_initialize_components` لتهيئة النظام الجديد
- ✅ تعديل `_main_cycle` للبحث في YouTube أولاً
- ✅ إضافة دوال جديدة:
  - `_collect_from_priority_youtube_channels()`
  - `_request_video_approval()`
  - `_generate_title_from_news()`
  - `_save_processed_video_data()`

### 2. modules/database.py
- ✅ إضافة جداول جديدة:
  - `processed_videos` - لتتبع الفيديوهات المعالجة
  - `video_approvals` - لتسجيل قرارات الموافقة
  - `video_transcripts` - لحفظ النصوص المستخرجة
- ✅ إضافة دوال جديدة:
  - `is_video_processed()`
  - `save_processed_video()`
  - `save_video_transcript()`
  - `log_video_approval_decision()`
  - `get_video_processing_stats()`
  - `get_channel_performance()`

### 3. config/settings.py
- ✅ إضافة إعدادات YouTube الجديدة:
  - `YOUTUBE_API_KEY`
  - `WHISPER_API_URL`
  - `WHISPER_API_KEY`
  - `HF_TOKEN`
  - `MAX_VIDEO_DURATION_MINUTES`
  - `MAX_VIDEO_AGE_DAYS`
  - `APPROVAL_TIMEOUT_MINUTES`
  - `AUTO_APPROVE_ON_TIMEOUT`

## 🎯 الميزات الجديدة المطبقة

### ✅ 1. نظام الأولوية لـ YouTube
- البحث الأول في 5 قنوات محددة بترتيب أولوية
- فلترة ذكية للفيديوهات (مدة أقل من 25 دقيقة، عمر أقل من شهرين)
- استخراج تلقائي لمعرفات القنوات من الروابط

### ✅ 2. استخراج النص بـ Whisper
- تكامل مع Whisper API على Hugging Face
- دعم اللغتين العربية والإنجليزية
- تحليل ذكي للنصوص لاستخراج الأخبار

### ✅ 3. نظام الموافقة التفاعلي
- رسائل Telegram مع أزرار تفاعلية
- معاينة تفصيلية للفيديو قبل المعالجة
- خيارات متعددة: موافقة، رفض، اختيار آخر
- موافقة تلقائية عند انتهاء المهلة

### ✅ 4. تتبع شامل للبيانات
- حفظ تفصيلي لبيانات الفيديوهات المعالجة
- تسجيل قرارات الموافقة
- إحصائيات أداء القنوات
- تقارير دورية للنتائج

## 🔄 سير العمل الجديد

```
1. بدء دورة البحث (كل 3 ساعات)
   ↓
2. البحث في قنوات YouTube المحددة (الأولوية الأولى)
   ↓
3. اختيار أحدث فيديو مناسب
   ↓
4. إرسال طلب موافقة عبر Telegram
   ↓
5. انتظار موافقة المدير (5 دقائق كحد أقصى)
   ↓
6. استخراج النص باستخدام Whisper
   ↓
7. تحليل النص للبحث عن أخبار الألعاب
   ↓
8. توليد مقالات من الأخبار المستخرجة
   ↓
9. نشر المقالات على Blogger و Telegram
   ↓
10. حفظ البيانات والإحصائيات
```

## 📊 القنوات المحددة

| الأولوية | القناة | اللغة | الرابط |
|---------|--------|-------|---------|
| 1 | Abu Reviews | عربي | https://youtube.com/@abureviews |
| 2 | Faisella | عربي | https://youtube.com/@faisella |
| 3 | Nasser Gamer Zone | عربي | https://youtube.com/@nassergamerzone |
| 4 | Gaming Channel | عربي | UCTu5mqtMgH99jp-O8yLNdfg |
| 5 | JorRaptor | إنجليزي | https://youtube.com/@jorraptor |

## 🛠️ متطلبات التشغيل

### المفاتيح المطلوبة
```env
# أساسي
YOUTUBE_API_KEY=your_youtube_api_key
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHANNEL_ID=@your_channel

# Whisper API
WHISPER_API_URL=https://nanami34-ai55.hf.space/api/transcribe
WHISPER_API_KEY=whisper-hf-spaces-2025
HF_TOKEN=*************************************
```

### الإعدادات الاختيارية
```env
MAX_VIDEO_DURATION_MINUTES=25
MAX_VIDEO_AGE_DAYS=60
APPROVAL_TIMEOUT_MINUTES=5
AUTO_APPROVE_ON_TIMEOUT=true
```

## 🧪 كيفية الاختبار

### 1. اختبار سريع
```bash
python quick_test_youtube_system.py
```

### 2. اختبار شامل
```bash
python test_youtube_priority_system.py
```

### 3. إعداد تفاعلي
```bash
python setup_youtube_priority.py
```

### 4. تشغيل النظام
```bash
python main.py
```

## 📱 كيفية استخدام نظام الموافقة

عند العثور على فيديو مناسب، ستصل رسالة على Telegram:

```
🎥 فيديو مقترح للمعالجة

📺 القناة: Abu Reviews
🏷️ العنوان: أحدث أخبار الألعاب لهذا الأسبوع
⏱️ المدة: 15:30
📅 تاريخ النشر: 2025-01-20 14:30
🌐 اللغة: عربي

📝 الوصف: في هذا الفيديو نتحدث عن...
🔗 الرابط: https://youtube.com/watch?v=...

❓ هل تريد معالجة هذا الفيديو لاستخراج الأخبار منه؟

[✅ موافق] [❌ رفض] [🔄 اختر فيديو آخر]
```

## 📈 الإحصائيات المتاحة

### إحصائيات الفيديوهات
```python
stats = db.get_video_processing_stats(7)  # آخر 7 أيام
print(f"الفيديوهات المعالجة: {stats['total_videos_processed']}")
print(f"الأخبار المستخرجة: {stats['total_news_extracted']}")
print(f"معدل الموافقة: {stats['approval_rate']:.1f}%")
```

### أداء القنوات
```python
channels = db.get_channel_performance(30)  # آخر 30 يوم
for channel in channels:
    print(f"{channel['channel_name']}: {channel['efficiency']:.1f}% كفاءة")
```

## 🔧 التخصيص

### إضافة قنوات جديدة
في `modules/advanced_youtube_analyzer.py`:
```python
self.priority_channels.append({
    'url': 'https://youtube.com/@new_channel',
    'name': 'New Gaming Channel',
    'language': 'ar',
    'priority': 6,
    'id': None
})
```

### تغيير معايير الفلترة
```python
self.max_video_duration = 30 * 60  # 30 دقيقة
self.max_video_age_days = 90       # 3 أشهر
```

## 🐛 استكشاف الأخطاء الشائعة

### 1. فشل في استخراج معرفات القنوات
**السبب**: مفتاح YouTube API غير صحيح أو غير مفعل
**الحل**: تأكد من تفعيل YouTube Data API v3 في Google Cloud Console

### 2. فشل في Whisper API
**السبب**: خدمة Hugging Face غير متاحة أو Token غير صحيح
**الحل**: تحقق من حالة الخدمة وصحة HF_TOKEN

### 3. عدم وصول رسائل الموافقة
**السبب**: البوت لا يمكنه إرسال رسائل للقناة/المجموعة
**الحل**: تأكد من إضافة البوت كمدير وصحة TELEGRAM_CHANNEL_ID

## 🎉 النتائج المتوقعة

### الفوائد
- ✅ محتوى أكثر حداثة وجودة من YouTube
- ✅ تحكم كامل في المحتوى المعالج
- ✅ استخراج دقيق للأخبار من الفيديوهات
- ✅ تتبع شامل للأداء والإحصائيات

### التحسينات
- 📈 زيادة جودة المحتوى المنشور
- 📈 تنوع أكبر في مصادر الأخبار
- 📈 تفاعل أفضل مع المحتوى العربي
- 📈 شفافية أكبر في عملية اختيار المحتوى

---

## 🚀 الخطوات التالية

1. **اختبار النظام**: `python quick_test_youtube_system.py`
2. **إعداد المفاتيح**: نسخ `.env.youtube.example` إلى `.env`
3. **تشغيل الإعداد**: `python setup_youtube_priority.py`
4. **بدء التشغيل**: `python main.py`
5. **مراقبة النتائج**: متابعة رسائل Telegram والإحصائيات

🎊 **النظام الجديد جاهز للاستخدام!**
