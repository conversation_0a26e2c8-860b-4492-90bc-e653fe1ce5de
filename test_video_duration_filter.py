#!/usr/bin/env python3
"""
اختبار فلترة مدة الفيديوهات
يختبر النظام المحسن لفلترة الفيديوهات الطويلة
"""

import sys
import os
import asyncio
import logging
from datetime import datetime

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.advanced_youtube_analyzer import AdvancedYouTubeAnalyzer
from modules.logger import logger

# إعداد التسجيل
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

async def test_duration_parsing():
    """اختبار تحليل مدة الفيديوهات"""
    print("🧪 اختبار تحليل مدة الفيديوهات...")
    
    analyzer = AdvancedYouTubeAnalyzer()
    
    # حالات اختبار مختلفة
    test_cases = [
        ("PT5M30S", 330, "5 دقائق و 30 ثانية"),
        ("PT15M", 900, "15 دقيقة"),
        ("PT1H", 3600, "ساعة واحدة"),
        ("PT1H30M", 5400, "ساعة و 30 دقيقة"),
        ("PT2H15M30S", 8130, "ساعتان و 15 دقيقة و 30 ثانية"),
        ("PT45M", 2700, "45 دقيقة"),
        ("PT30S", 30, "30 ثانية"),
        ("PT", 0, "مدة فارغة"),
        ("", 0, "نص فارغ")
    ]
    
    print(f"📊 اختبار {len(test_cases)} حالة...")
    
    for duration_str, expected, description in test_cases:
        result = analyzer._parse_duration(duration_str)
        status = "✅" if result == expected else "❌"
        
        print(f"{status} {description}: '{duration_str}' -> {result} ثانية (متوقع: {expected})")
        
        if result != expected:
            print(f"   ⚠️ خطأ: النتيجة {result} لا تطابق المتوقع {expected}")

async def test_video_filtering():
    """اختبار فلترة الفيديوهات حسب المدة"""
    print("\n🎬 اختبار فلترة الفيديوهات...")
    
    analyzer = AdvancedYouTubeAnalyzer()
    
    # فيديوهات تجريبية
    test_videos = [
        {
            'id': 'short_video',
            'title': 'أخبار ألعاب سريعة - 10 دقائق',
            'description': 'أحدث أخبار الألعاب في 10 دقائق',
            'published_at': '2025-01-20T10:00:00Z',
            'duration_str': 'PT10M'
        },
        {
            'id': 'medium_video',
            'title': 'مراجعة لعبة جديدة - 25 دقيقة',
            'description': 'مراجعة شاملة للعبة الجديدة',
            'published_at': '2025-01-20T11:00:00Z',
            'duration_str': 'PT25M'
        },
        {
            'id': 'long_video',
            'title': 'بودكاست ألعاب طويل - ساعة ونصف',
            'description': 'بودكاست مفصل عن صناعة الألعاب',
            'published_at': '2025-01-20T12:00:00Z',
            'duration_str': 'PT1H30M'
        },
        {
            'id': 'very_long_video',
            'title': 'بث مباشر للألعاب - 3 ساعات',
            'description': 'بث مباشر طويل للعب',
            'published_at': '2025-01-20T13:00:00Z',
            'duration_str': 'PT3H'
        }
    ]
    
    print(f"📹 اختبار {len(test_videos)} فيديو...")
    print(f"⏱️ الحد الأقصى المسموح: {analyzer.max_video_duration // 60} دقيقة")
    
    for video in test_videos:
        duration_seconds = analyzer._parse_duration(video['duration_str'])
        duration_minutes = duration_seconds // 60
        
        # محاكاة فحص المناسبة
        is_duration_ok = duration_seconds <= analyzer.max_video_duration
        
        status = "✅ مقبول" if is_duration_ok else "❌ مرفوض"
        
        print(f"\n🎬 {video['title']}")
        print(f"   ⏱️ المدة: {duration_minutes} دقيقة ({duration_seconds} ثانية)")
        print(f"   🎯 النتيجة: {status}")
        
        if not is_duration_ok:
            print(f"   💡 السبب: المدة ({duration_minutes} دقيقة) تتجاوز الحد المسموح ({analyzer.max_video_duration // 60} دقيقة)")

async def test_real_video_duration():
    """اختبار الحصول على مدة فيديو حقيقي (إذا كان API متاح)"""
    print("\n🌐 اختبار الحصول على مدة فيديو حقيقي...")
    
    analyzer = AdvancedYouTubeAnalyzer()
    
    # فيديو تجريبي قصير (يمكن تغييره)
    test_video_id = "dQw4w9WgXcQ"  # Rick Roll - فيديو قصير معروف
    
    try:
        duration = await analyzer._get_video_duration(test_video_id)
        
        if duration:
            duration_minutes = duration // 60
            duration_seconds = duration % 60
            
            print(f"✅ تم الحصول على مدة الفيديو: {duration_minutes}:{duration_seconds:02d}")
            
            if duration <= analyzer.max_video_duration:
                print(f"✅ الفيديو مقبول (أقل من {analyzer.max_video_duration // 60} دقيقة)")
            else:
                print(f"❌ الفيديو مرفوض (أكثر من {analyzer.max_video_duration // 60} دقيقة)")
        else:
            print("⚠️ لا يمكن الحصول على مدة الفيديو (ربما API غير متاح)")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الفيديو الحقيقي: {e}")

async def test_channel_video_filtering():
    """اختبار فلترة فيديوهات القناة"""
    print("\n📺 اختبار فلترة فيديوهات القناة...")
    
    analyzer = AdvancedYouTubeAnalyzer()
    
    print(f"🔍 البحث عن فيديو مناسب من القنوات المحددة...")
    print(f"⏱️ الحد الأقصى: {analyzer.max_video_duration // 60} دقيقة")
    
    try:
        # محاولة العثور على فيديو مناسب
        video = await analyzer.find_latest_gaming_video()
        
        if video:
            print(f"✅ تم العثور على فيديو مناسب:")
            print(f"   📺 العنوان: {video['title']}")
            print(f"   📅 التاريخ: {video.get('published_at', 'غير محدد')}")
            print(f"   📺 القناة: {video.get('channel_info', {}).get('name', 'غير محدد')}")
            
            # فحص المدة إذا كانت متاحة
            duration = await analyzer._get_video_duration(video['id'])
            if duration:
                duration_minutes = duration // 60
                print(f"   ⏱️ المدة: {duration_minutes} دقيقة")
        else:
            print("❌ لم يتم العثور على فيديو مناسب")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار فلترة القناة: {e}")

def print_summary():
    """طباعة ملخص الاختبارات"""
    print("\n" + "="*60)
    print("📋 ملخص اختبار فلترة مدة الفيديوهات")
    print("="*60)
    print("✅ تم اختبار:")
    print("   • تحليل مدة الفيديوهات من تنسيق YouTube")
    print("   • فلترة الفيديوهات حسب الحد الأقصى للمدة")
    print("   • الحصول على مدة فيديو حقيقي (إذا كان API متاح)")
    print("   • فلترة فيديوهات القنوات المحددة")
    print("\n💡 النتائج المتوقعة:")
    print("   • رفض الفيديوهات أطول من 30 دقيقة")
    print("   • قبول الفيديوهات 30 دقيقة أو أقل")
    print("   • تسجيل واضح لأسباب الرفض")

async def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار نظام فلترة مدة الفيديوهات")
    print("="*60)
    
    # تشغيل جميع الاختبارات
    await test_duration_parsing()
    await test_video_filtering()
    await test_real_video_duration()
    await test_channel_video_filtering()
    
    print_summary()

if __name__ == "__main__":
    asyncio.run(main())
