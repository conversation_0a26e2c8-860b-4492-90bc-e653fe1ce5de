#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مكون Tavily للبحث العميق مع الذكاء الاصطناعي
يوفر بحث عميق متقدم مع إدارة ذكية للحدود الشهرية (1000 طلب/شهر)
"""

import aiohttp
import asyncio
import json
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import hashlib
from .logger import logger
from .database import db
from .api_key_manager import ApiKeyManager
from config.settings import BotConfig

class TavilySearch:
    """مكون Tavily للبحث العميق مع الذكاء الاصطناعي"""
    
    def __init__(self):
        """تهيئة مكون Tavily"""
        # إعداد مدير المفاتيح
        tavily_keys = getattr(BotConfig, 'TAVILY_API_KEYS', [])
        if tavily_keys:
            self.api_manager = ApiKeyManager(
                api_keys=tavily_keys,
                service_name="Tavily",
                auto_recovery_minutes=60,  # إعادة تفعيل بعد ساعة
                load_balancing=True
            )
            self.enabled = True
        else:
            self.api_manager = None
            self.enabled = False
        
        self.base_url = "https://api.tavily.com/search"
        
        # إدارة الحدود الشهرية (1000 طلب/شهر لكل مفتاح)
        self.monthly_limits = {
            'max_requests_per_key': 1000,
            'max_daily_requests': 35,  # توزيع آمن: 1000/30 = 33 تقريباً
            'emergency_reserve': 100,  # احتياطي للحالات الطارئة
        }
        
        # إحصائيات الاستخدام
        self.usage_stats = {
            'total_searches': 0,
            'successful_searches': 0,
            'failed_searches': 0,
            'monthly_usage': {},  # تتبع الاستخدام الشهري لكل مفتاح
            'daily_usage': {},    # تتبع الاستخدام اليومي
            'last_reset': datetime.now()
        }
        
        # تخزين مؤقت ذكي (مدة أطول لتوفير الطلبات)
        self.cache = {}
        self.cache_duration = 7200  # ساعتين للبحث العميق
        
        # إعدادات البحث المحسنة للألعاب
        self.gaming_domains = [
            'ign.com', 'gamespot.com', 'polygon.com', 'kotaku.com',
            'pcgamer.com', 'eurogamer.net', 'gamesindustry.biz',
            'gamedeveloper.com', 'destructoid.com', 'rockpapershotgun.com'
        ]
        
        if self.enabled:
            logger.info(f"🔍 تم تهيئة Tavily للبحث العميق مع {len(tavily_keys)} مفتاح")
            self._load_usage_stats()
        else:
            logger.warning("⚠️ Tavily غير مفعل - لا توجد مفاتيح API")
    
    def _load_usage_stats(self):
        """تحميل إحصائيات الاستخدام من قاعدة البيانات"""
        try:
            # محاولة تحميل الإحصائيات المحفوظة
            saved_stats = db.get_api_usage_stats('tavily')
            if saved_stats:
                self.usage_stats.update(saved_stats)
                logger.debug("📊 تم تحميل إحصائيات Tavily المحفوظة")
        except Exception as e:
            logger.debug(f"لم يتم تحميل إحصائيات Tavily: {e}")
    
    def _save_usage_stats(self):
        """حفظ إحصائيات الاستخدام في قاعدة البيانات"""
        try:
            db.save_api_usage_stats('tavily', self.usage_stats)
        except Exception as e:
            logger.debug(f"فشل في حفظ إحصائيات Tavily: {e}")
    
    def _check_usage_limits(self) -> bool:
        """فحص حدود الاستخدام الشهرية واليومية"""
        current_month = datetime.now().strftime("%Y-%m")
        current_date = datetime.now().strftime("%Y-%m-%d")
        
        # فحص الحد اليومي
        daily_usage = self.usage_stats['daily_usage'].get(current_date, 0)
        if daily_usage >= self.monthly_limits['max_daily_requests']:
            logger.warning(f"⚠️ تم تجاوز الحد اليومي لـ Tavily: {daily_usage}/{self.monthly_limits['max_daily_requests']}")
            return False
        
        # فحص الحد الشهري
        current_key = self.api_manager.get_key()
        monthly_usage = self.usage_stats['monthly_usage'].get(f"{current_key}_{current_month}", 0)
        monthly_limit = self.monthly_limits['max_requests_per_key'] - self.monthly_limits['emergency_reserve']
        
        if monthly_usage >= monthly_limit:
            logger.warning(f"⚠️ تم تجاوز الحد الشهري لـ Tavily: {monthly_usage}/{monthly_limit}")
            # محاولة التبديل إلى مفتاح آخر
            try:
                self.api_manager.rotate_key()
                new_key = self.api_manager.get_key()
                new_monthly_usage = self.usage_stats['monthly_usage'].get(f"{new_key}_{current_month}", 0)
                
                if new_monthly_usage >= monthly_limit:
                    logger.error("❌ جميع مفاتيح Tavily تجاوزت الحد الشهري")
                    return False
                else:
                    logger.info(f"🔄 تم التبديل إلى مفتاح Tavily جديد")
                    return True
            except:
                return False
        
        return True
    
    def _update_usage_stats(self, success: bool = True):
        """تحديث إحصائيات الاستخدام"""
        current_month = datetime.now().strftime("%Y-%m")
        current_date = datetime.now().strftime("%Y-%m-%d")
        current_key = self.api_manager.get_key()
        
        # تحديث الإحصائيات العامة
        self.usage_stats['total_searches'] += 1
        if success:
            self.usage_stats['successful_searches'] += 1
        else:
            self.usage_stats['failed_searches'] += 1
        
        # تحديث الاستخدام اليومي
        self.usage_stats['daily_usage'][current_date] = self.usage_stats['daily_usage'].get(current_date, 0) + 1
        
        # تحديث الاستخدام الشهري
        monthly_key = f"{current_key}_{current_month}"
        self.usage_stats['monthly_usage'][monthly_key] = self.usage_stats['monthly_usage'].get(monthly_key, 0) + 1
        
        # حفظ الإحصائيات
        self._save_usage_stats()
    
    async def search(self, query: str, search_depth: str = "advanced", max_results: int = 10, **kwargs) -> List[Dict]:
        """
        البحث العميق باستخدام Tavily
        :param query: استعلام البحث
        :param search_depth: عمق البحث (basic, advanced)
        :param max_results: عدد النتائج المطلوبة
        :param kwargs: معاملات إضافية
        :return: قائمة النتائج
        """
        if not self.enabled:
            logger.debug("🚫 Tavily غير مفعل")
            return []
        
        # فحص حدود الاستخدام
        if not self._check_usage_limits():
            logger.warning("⚠️ تم تجاوز حدود Tavily - تخطي البحث")
            return []
        
        # فحص التخزين المؤقت
        cache_key = self._generate_cache_key(query, search_depth, max_results, kwargs)
        cached_result = self._get_cached_result(cache_key)
        if cached_result:
            logger.info(f"📦 استخدام نتائج Tavily مخزنة مؤقتاً لـ: {query}")
            return cached_result
        
        try:
            # إعداد طلب البحث
            payload = {
                "api_key": self.api_manager.get_key(),
                "query": f"{query} gaming news",
                "search_depth": search_depth,
                "include_answer": True,
                "include_raw_content": True,
                "max_results": max_results,
                "include_domains": self.gaming_domains,
                "exclude_domains": ["reddit.com", "twitter.com"],  # تجنب المنتديات
                **kwargs
            }
            
            headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
            
            logger.info(f"🔍 البحث العميق في Tavily عن: '{query}' (عمق: {search_depth})")
            
            async with aiohttp.ClientSession() as session:
                async with session.post(self.base_url, json=payload, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        results = self._process_search_results(data, query)
                        
                        # حفظ في التخزين المؤقت
                        self._cache_result(cache_key, results)
                        
                        # تحديث الإحصائيات
                        self._update_usage_stats(success=True)
                        
                        logger.info(f"✅ Tavily نجح: {len(results)} نتيجة عميقة لـ '{query}'")
                        return results
                    
                    elif response.status == 429:
                        logger.warning("⚠️ تم تجاوز حد الطلبات في Tavily")
                        self._update_usage_stats(success=False)
                        return []
                    
                    elif response.status == 401:
                        logger.error("❌ خطأ في مفتاح Tavily API")
                        self._update_usage_stats(success=False)
                        # محاولة التبديل إلى مفتاح آخر
                        try:
                            self.api_manager.rotate_key()
                            return await self.search(query, search_depth, max_results, **kwargs)
                        except:
                            return []
                    
                    else:
                        logger.error(f"❌ خطأ غير متوقع في Tavily: {response.status}")
                        error_text = await response.text()
                        logger.debug(f"تفاصيل الخطأ: {error_text}")
                        self._update_usage_stats(success=False)
                        return []
        
        except Exception as e:
            logger.error(f"❌ خطأ في Tavily: {e}")
            self._update_usage_stats(success=False)
            return []
    
    def _process_search_results(self, data: Dict, query: str) -> List[Dict]:
        """معالجة نتائج البحث من Tavily"""
        results = []
        
        try:
            # معالجة النتائج الأساسية
            if 'results' in data:
                for item in data['results']:
                    result = self._process_single_result(item, query)
                    if result:
                        results.append(result)
            
            # إضافة الإجابة المولدة بالذكاء الاصطناعي إذا متوفرة
            if 'answer' in data and data['answer']:
                ai_summary = {
                    'title': f"ملخص ذكي: {query}",
                    'content': data['answer'],
                    'url': 'tavily://ai-generated-summary',
                    'source': 'Tavily AI',
                    'content_type': 'ai_summary',
                    'relevance_score': 10.0,
                    'search_engine': 'Tavily-AI',
                    'search_time': datetime.now(),
                    'is_ai_generated': True
                }
                results.insert(0, ai_summary)  # ضع الملخص الذكي في المقدمة
            
            # ترتيب حسب الصلة والجودة
            results = self._sort_results_by_relevance(results, query)
            
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة نتائج Tavily: {e}")
        
        return results
    
    def _process_single_result(self, item: Dict, query: str) -> Optional[Dict]:
        """معالجة نتيجة واحدة من Tavily"""
        try:
            result = {
                'title': item.get('title', ''),
                'content': item.get('content', ''),
                'url': item.get('url', ''),
                'score': item.get('score', 0),
                'published_date': item.get('published_date', ''),
                'source': 'Tavily',
                'search_engine': 'Tavily-Deep',
                'search_time': datetime.now(),
                'relevance_score': self._calculate_relevance(item, query),
                'content_type': 'deep_search',
                'raw_content': item.get('raw_content', '')
            }
            
            # فلترة النتائج منخفضة الجودة
            if len(result['title']) < 10 or len(result['content']) < 50:
                return None
            
            # فحص الصلة بالألعاب
            gaming_keywords = ['game', 'gaming', 'video', 'player', 'console', 'pc', 'mobile', 'esports']
            text_to_check = f"{result['title']} {result['content']}".lower()
            
            gaming_relevance = sum(1 for keyword in gaming_keywords if keyword in text_to_check)
            if gaming_relevance < 2:  # يجب أن يحتوي على كلمتين على الأقل
                return None
            
            result['gaming_relevance'] = gaming_relevance
            return result
            
        except Exception as e:
            logger.debug(f"خطأ في معالجة نتيجة Tavily: {e}")
            return None
    
    def _calculate_relevance(self, item: Dict, query: str) -> float:
        """حساب درجة الصلة للنتيجة"""
        try:
            score = item.get('score', 0) * 10  # Tavily score (0-1) -> (0-10)
            
            query_words = query.lower().split()
            title = item.get('title', '').lower()
            content = item.get('content', '').lower()
            
            # نقاط إضافية للكلمات المطابقة
            for word in query_words:
                if word in title:
                    score += 1.5
                if word in content:
                    score += 0.5
            
            # نقاط للحداثة
            if item.get('published_date'):
                score += 1.0
            
            return min(score, 10.0)
            
        except Exception:
            return 5.0
    
    def _sort_results_by_relevance(self, results: List[Dict], query: str) -> List[Dict]:
        """ترتيب النتائج حسب الصلة والجودة"""
        try:
            return sorted(results, 
                         key=lambda x: (x.get('relevance_score', 0), x.get('gaming_relevance', 0)), 
                         reverse=True)
        except Exception:
            return results
    
    def _generate_cache_key(self, query: str, search_depth: str, max_results: int, kwargs: Dict) -> str:
        """إنشاء مفتاح للتخزين المؤقت"""
        cache_data = f"{query}_{search_depth}_{max_results}_{json.dumps(kwargs, sort_keys=True)}"
        return f"tavily_{hashlib.md5(cache_data.encode()).hexdigest()}"
    
    def _get_cached_result(self, cache_key: str) -> Optional[List[Dict]]:
        """الحصول على نتيجة مخزنة مؤقتاً"""
        if cache_key in self.cache:
            cached_data = self.cache[cache_key]
            if datetime.now() - cached_data['timestamp'] < timedelta(seconds=self.cache_duration):
                return cached_data['results']
            else:
                del self.cache[cache_key]
        return None
    
    def _cache_result(self, cache_key: str, results: List[Dict]):
        """حفظ النتيجة في التخزين المؤقت"""
        self.cache[cache_key] = {
            'results': results,
            'timestamp': datetime.now()
        }
        
        # تنظيف التخزين المؤقت القديم
        if len(self.cache) > 50:  # حد أقل للذاكرة
            oldest_key = min(self.cache.keys(), key=lambda k: self.cache[k]['timestamp'])
            del self.cache[oldest_key]
    
    def get_usage_stats(self) -> Dict:
        """الحصول على إحصائيات الاستخدام"""
        current_month = datetime.now().strftime("%Y-%m")
        current_date = datetime.now().strftime("%Y-%m-%d")
        
        # حساب الاستخدام الحالي
        daily_usage = self.usage_stats['daily_usage'].get(current_date, 0)
        monthly_usage = sum(
            usage for key, usage in self.usage_stats['monthly_usage'].items()
            if key.endswith(current_month)
        )
        
        return {
            **self.usage_stats,
            'current_daily_usage': daily_usage,
            'current_monthly_usage': monthly_usage,
            'daily_limit': self.monthly_limits['max_daily_requests'],
            'monthly_limit': self.monthly_limits['max_requests_per_key'],
            'daily_remaining': max(0, self.monthly_limits['max_daily_requests'] - daily_usage),
            'monthly_remaining': max(0, self.monthly_limits['max_requests_per_key'] - monthly_usage),
            'success_rate': (
                self.usage_stats['successful_searches'] / 
                max(self.usage_stats['total_searches'], 1) * 100
            ),
            'cache_size': len(self.cache),
            'enabled': self.enabled
        }
    
    async def test_connection(self) -> Dict:
        """اختبار الاتصال بـ Tavily"""
        try:
            test_results = await self.search("gaming news test", search_depth="basic", max_results=1)
            
            return {
                'status': 'success' if test_results else 'failed',
                'message': f'تم العثور على {len(test_results)} نتيجة' if test_results else 'لم يتم العثور على نتائج',
                'api_key_valid': bool(test_results),
                'usage_stats': self.get_usage_stats()
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'خطأ في الاختبار: {str(e)}',
                'api_key_valid': False,
                'usage_stats': self.get_usage_stats()
            }

# إنشاء مثيل عام
tavily_search = TavilySearch()
