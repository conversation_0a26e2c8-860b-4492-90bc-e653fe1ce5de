# وحدة إدارة مفاتيح API الديناميكية المحسنة
import random
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from .logger import logger

class ApiKeyManager:
    """
    يدير مجموعة من مفاتيح API لخدمة معينة، ويدعم التبديل التلقائي عند الفشل
    مع ميزات متقدمة لتوزيع الحمولة وإعادة التفعيل التلقائي.
    """
    def __init__(self, api_keys: List[str], service_name: str,
                 auto_recovery_minutes: int = 60,
                 load_balancing: bool = True):
        """
        تهيئة مدير المفاتيح المحسن.
        :param api_keys: قائمة بمفاتيح API.
        :param service_name: اسم الخدمة (للتسجيل).
        :param auto_recovery_minutes: دقائق إعادة تفعيل المفاتيح المعطلة.
        :param load_balancing: تفعيل توزيع الحمولة.
        """
        if not isinstance(api_keys, list) or not api_keys:
            raise ValueError("api_keys must be a non-empty list.")

        self.service_name = service_name
        self.keys = api_keys
        self.current_key_index = 0
        self.blacklisted_keys = {}  # {key: blacklist_time}
        self.auto_recovery_minutes = auto_recovery_minutes
        self.load_balancing = load_balancing
        self.usage_stats = {key: {'calls': 0, 'failures': 0, 'last_used': None} for key in api_keys}
        self.total_calls = 0

        logger.info(f"🔑 تم تهيئة مدير المفاتيح المحسن لخدمة '{self.service_name}' مع {len(self.keys)} مفتاح.")

    def get_key(self) -> str:
        """
        الحصول على مفتاح API الصالح الحالي مع إعادة التفعيل التلقائي.
        :return: مفتاح API كسلسلة نصية.
        """
        # إعادة تفعيل المفاتيح المعطلة إذا انتهت مدة العقوبة
        self._auto_recover_keys()

        # إذا كان توزيع الحمولة مفعل، اختر أفضل مفتاح
        if self.load_balancing:
            best_key = self._get_best_key_for_load_balancing()
            if best_key:
                self.current_key_index = self.keys.index(best_key)

        if self.is_all_blacklisted():
            logger.critical(f"🚨 جميع مفاتيح API لخدمة '{self.service_name}' تم إدراجها في القائمة السوداء!")
            raise Exception(f"No valid API keys available for {self.service_name}")

        current_key = self.keys[self.current_key_index]

        # تحديث إحصائيات الاستخدام
        self.usage_stats[current_key]['calls'] += 1
        self.usage_stats[current_key]['last_used'] = datetime.now()
        self.total_calls += 1

        return current_key

    def rotate_key(self) -> str:
        """
        تبديل إلى المفتاح التالي في القائمة ووضع المفتاح الحالي في القائمة السوداء.
        :return: مفتاح API الجديد.
        """
        # الحصول على المفتاح الحالي قبل التبديل
        current_key = self.keys[self.current_key_index]

        # إضافة المفتاح الحالي إلى القائمة السوداء مع الوقت
        self.blacklisted_keys[current_key] = datetime.now()
        self.usage_stats[current_key]['failures'] += 1

        logger.warning(f"🚫 تم وضع المفتاح '{current_key[:4]}...{current_key[-4:]}' في القائمة السوداء لخدمة '{self.service_name}'.")

        # إعادة تفعيل المفاتيح المعطلة إذا انتهت مدة العقوبة
        self._auto_recover_keys()

        # التحقق مما إذا كانت جميع المفاتيح قد استنفدت
        if self.is_all_blacklisted():
            logger.error(f"❌ استنفدت جميع مفاتيح API لخدمة '{self.service_name}'.")
            self._send_exhaustion_alert()
            raise Exception(f"All API keys for {self.service_name} are blacklisted.")

        # البحث عن المفتاح الصالح التالي
        self.current_key_index = (self.current_key_index + 1) % len(self.keys)
        while self.keys[self.current_key_index] in self.blacklisted_keys:
            self.current_key_index = (self.current_key_index + 1) % len(self.keys)

        new_key = self.keys[self.current_key_index]
        logger.info(f"🔄 التبديل إلى مفتاح API جديد لخدمة '{self.service_name}': '{new_key[:4]}...{new_key[-4:]}'")
        return new_key

    def is_all_blacklisted(self) -> bool:
        """التحقق مما إذا كانت جميع المفاتيح في القائمة السوداء."""
        return len(self.blacklisted_keys) == len(self.keys)

    def _auto_recover_keys(self):
        """إعادة تفعيل المفاتيح المعطلة تلقائياً بعد انتهاء مدة العقوبة"""
        current_time = datetime.now()
        keys_to_recover = []

        for key, blacklist_time in self.blacklisted_keys.items():
            if current_time - blacklist_time >= timedelta(minutes=self.auto_recovery_minutes):
                keys_to_recover.append(key)

        for key in keys_to_recover:
            del self.blacklisted_keys[key]
            logger.info(f"🔓 تم إعادة تفعيل المفتاح '{key[:4]}...{key[-4:]}' لخدمة '{self.service_name}' تلقائياً")

    def _get_best_key_for_load_balancing(self) -> Optional[str]:
        """اختيار أفضل مفتاح لتوزيع الحمولة"""
        available_keys = [key for key in self.keys if key not in self.blacklisted_keys]

        if not available_keys:
            return None

        # اختيار المفتاح الأقل استخداماً
        best_key = min(available_keys, key=lambda k: self.usage_stats[k]['calls'])
        return best_key

    def _send_exhaustion_alert(self):
        """إرسال تنبيه عند استنفاد جميع المفاتيح"""
        logger.critical(f"🚨 تنبيه: تم استنفاد جميع مفاتيح API لخدمة '{self.service_name}'!")
        logger.critical(f"📊 إحصائيات الاستخدام:")

        for key, stats in self.usage_stats.items():
            masked_key = f"{key[:4]}...{key[-4:]}"
            logger.critical(f"   {masked_key}: {stats['calls']} استدعاء، {stats['failures']} فشل")

    def get_usage_stats(self) -> Dict:
        """الحصول على إحصائيات الاستخدام"""
        stats = {
            'service_name': self.service_name,
            'total_keys': len(self.keys),
            'blacklisted_keys': len(self.blacklisted_keys),
            'available_keys': len(self.keys) - len(self.blacklisted_keys),
            'total_calls': self.total_calls,
            'key_stats': {}
        }

        for key, key_stats in self.usage_stats.items():
            masked_key = f"{key[:4]}...{key[-4:]}"
            stats['key_stats'][masked_key] = {
                'calls': key_stats['calls'],
                'failures': key_stats['failures'],
                'last_used': key_stats['last_used'].isoformat() if key_stats['last_used'] else None,
                'is_blacklisted': key in self.blacklisted_keys
            }

        return stats

    def mark_key_failed(self, key: str):
        """وضع مفتاح معين في القائمة السوداء"""
        if key in self.keys:
            self.blacklisted_keys[key] = datetime.now()
            self.usage_stats[key]['failures'] += 1
            logger.warning(f"🚫 تم وضع المفتاح '{key[:4]}...{key[-4:]}' في القائمة السوداء لخدمة '{self.service_name}'")
        else:
            logger.warning(f"⚠️ محاولة وضع مفتاح غير موجود في القائمة السوداء: {key[:4]}...")

    def get_available_keys_count(self) -> int:
        """الحصول على عدد المفاتيح المتاحة (غير المعطلة)"""
        return len(self.keys) - len(self.blacklisted_keys)

    def reset_key_failures(self, key: str = None):
        """إعادة تعيين فشل مفتاح معين أو جميع المفاتيح"""
        if key:
            if key in self.blacklisted_keys:
                del self.blacklisted_keys[key]
                logger.info(f"🔓 تم إعادة تفعيل المفتاح '{key[:4]}...{key[-4:]}' يدوياً")
        else:
            self.blacklisted_keys.clear()
            logger.info(f"🔓 تم إعادة تفعيل جميع مفاتيح '{self.service_name}' يدوياً")

# يمكن إنشاء مثيلات مركزية هنا إذا لزم الأمر
# google_api_manager = ApiKeyManager(...)
