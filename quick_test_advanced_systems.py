# اختبار سريع للأنظمة المتقدمة الجديدة
import asyncio
import sys
import os

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def quick_test_advanced_systems():
    """اختبار سريع للأنظمة المتقدمة الجديدة"""
    
    print("🚀 اختبار سريع للأنظمة المتقدمة الجديدة")
    print("=" * 60)
    
    try:
        # 1. اختبار استيراد الوحدات الجديدة
        print("\n📦 فحص استيراد الوحدات...")
        
        try:
            from modules.advanced_news_apis import advanced_news_apis
            print("✅ تم استيراد advanced_news_apis بنجاح")
        except ImportError as e:
            print(f"❌ فشل في استيراد advanced_news_apis: {e}")
            return False
        
        try:
            from modules.advanced_web_scraper import advanced_web_scraper
            print("✅ تم استيراد advanced_web_scraper بنجاح")
        except ImportError as e:
            print(f"❌ فشل في استيراد advanced_web_scraper: {e}")
            return False
        
        try:
            from modules.image_guard import ai_image_generator
            print("✅ تم استيراد ai_image_generator بنجاح")
        except ImportError as e:
            print(f"❌ فشل في استيراد ai_image_generator: {e}")
            return False
        
        # 2. فحص التكوين
        print("\n🔧 فحص التكوين...")
        
        # فحص مفاتيح APIs الأخبار
        print(f"   📰 NewsData API: {'✅ موجود' if advanced_news_apis.newsdata_key else '❌ غير موجود'}")
        print(f"   📰 NewsAPI: {'✅ موجود' if advanced_news_apis.newsapi_key else '❌ غير موجود'}")
        print(f"   📰 TheNewsAPI: {'✅ موجود' if advanced_news_apis.thenewsapi_key else '❌ غير موجود'}")
        print(f"   📰 GNews: {'✅ موجود' if advanced_news_apis.gnews_key else '❌ غير موجود'}")
        
        # فحص مفاتيح محركات البحث
        print(f"   🔍 Brave Search: {'✅ موجود' if advanced_web_scraper.brave_search_key else '❌ غير موجود'}")
        print(f"   🔍 Google Search: {'✅ موجود' if advanced_web_scraper.google_search_key else '❌ غير موجود'}")
        
        # فحص مفاتيح إنشاء الصور
        print(f"   🎨 Freepik: {'✅ موجود' if ai_image_generator.freepik_api_key else '❌ غير موجود'}")
        print(f"   🎨 FluxAI: {'✅ موجود' if ai_image_generator.fluxai_api_key else '❌ غير موجود'}")
        
        # 3. اختبار الدوال الأساسية
        print("\n🧪 اختبار الدوال الأساسية...")
        
        # اختبار تحليل المقال للعناصر المرئية
        test_article = {
            'title': 'أفضل ألعاب 2025 المنتظرة',
            'content': 'مقال عن أفضل الألعاب المنتظرة في 2025 مثل GTA 6 و Cyberpunk',
            'keywords': ['gaming', 'video games', '2025']
        }
        
        visual_elements = ai_image_generator._analyze_article_for_visuals(
            test_article['title'], 
            test_article['content'], 
            test_article['keywords']
        )
        
        print(f"   🎨 تحليل العناصر المرئية:")
        print(f"      • نوع المحتوى: {visual_elements['content_type']}")
        print(f"      • الموضوع الرئيسي: {visual_elements['main_theme']}")
        print(f"      • الألعاب المكتشفة: {visual_elements['game_names']}")
        
        # اختبار إنشاء prompts
        prompts = ai_image_generator._create_optimized_prompts(visual_elements, 2)
        print(f"   📝 إنشاء Prompts: تم إنشاء {len(prompts)} prompt")
        
        for i, prompt in enumerate(prompts, 1):
            print(f"      {i}. {prompt['prompt'][:60]}...")
        
        # 4. اختبار فحص الصلة بالألعاب
        print("\n🎮 اختبار فحص الصلة بالألعاب...")
        
        test_texts = [
            "أفضل ألعاب الفيديو لعام 2025",
            "تحديث جديد للعبة Minecraft",
            "أخبار الطقس اليوم",
            "مراجعة جهاز PlayStation 5"
        ]
        
        for text in test_texts:
            is_gaming = advanced_news_apis._is_gaming_related(text)
            print(f"   {'✅' if is_gaming else '❌'} '{text[:40]}...' - {'متعلق بالألعاب' if is_gaming else 'غير متعلق'}")
        
        # 5. اختبار حساب نقاط الجودة
        print("\n📊 اختبار حساب نقاط الجودة...")
        
        test_articles = [
            {
                'title': 'عنوان قصير',
                'content': 'محتوى قصير',
                'source': {'name': 'مصدر عادي'}
            },
            {
                'title': 'عنوان طويل ومفصل عن أفضل ألعاب الفيديو الجديدة',
                'content': 'محتوى طويل ومفصل يحتوي على معلومات شاملة عن الألعاب الجديدة والتحديثات والمراجعات والأخبار المهمة في عالم الألعاب',
                'source': {'name': 'ign.com'},
                'urlToImage': 'https://example.com/image.jpg',
                'author': 'كاتب محترف'
            }
        ]
        
        for i, article in enumerate(test_articles, 1):
            quality_score = advanced_news_apis._calculate_quality_score(article)
            print(f"   📊 المقال {i}: {quality_score:.1f}/10 نقاط")
        
        # 6. اختبار تنظيف النص
        print("\n🧹 اختبار تنظيف النص...")
        
        dirty_text = "<p>نص مع <strong>HTML tags</strong></p>\n\n\nأسطر فارغة متعددة   مسافات زائدة"
        clean_text = advanced_web_scraper._clean_text(dirty_text)
        
        print(f"   📝 النص الأصلي: {dirty_text[:50]}...")
        print(f"   ✨ النص المنظف: {clean_text[:50]}...")
        
        # 7. عرض الإحصائيات الحالية
        print("\n📈 الإحصائيات الحالية:")
        
        news_stats = advanced_news_apis.get_usage_stats()
        web_stats = advanced_web_scraper.get_extraction_stats()
        image_stats = ai_image_generator.get_generation_stats()
        
        print(f"   📰 APIs الأخبار:")
        print(f"      • إجمالي المقالات: {news_stats.get('total_articles_found', 0)}")
        print(f"      • معدل النجاح: {news_stats.get('success_rate', 0):.1f}%")
        
        print(f"   🔍 محركات البحث:")
        print(f"      • إجمالي المقالات: {web_stats.get('total_articles_extracted', 0)}")
        print(f"      • معدل النجاح: {web_stats.get('success_rate', 0):.1f}%")
        
        print(f"   🎨 إنشاء الصور:")
        print(f"      • إجمالي الصور: {image_stats.get('total_images_created', 0)}")
        print(f"      • معدل النجاح: {image_stats.get('success_rate', 0):.1f}%")
        
        print("\n🎉 الاختبار السريع مكتمل بنجاح!")
        print("💡 جميع الأنظمة المتقدمة جاهزة للاستخدام")
        print("🚀 يمكنك الآن تشغيل:")
        print("   • python test_advanced_news_extraction.py (اختبار شامل)")
        print("   • python test_ai_image_generation.py (اختبار الصور)")
        print("   • python main.py (تشغيل الوكيل مع الميزات الجديدة)")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 تأكد من تثبيت جميع المتطلبات: pip install -r requirements.txt")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(quick_test_advanced_systems())
    
    if success:
        print("\n✅ جميع الأنظمة المتقدمة جاهزة!")
        print("🎯 الوكيل الآن قادر على:")
        print("   🚀 البحث في 150,000+ مصدر حول العالم")
        print("   🎨 إنشاء صور احترافية بالذكاء الاصطناعي")
        print("   🔍 استخراج المحتوى من مواقع متخصصة")
        print("   📊 تحليل المواضيع الرائجة تلقائياً")
        print("   🛡️ ضمان الجودة والأمان")
    else:
        print("\n⚠️ يرجى مراجعة الأخطاء أعلاه")
        print("💡 تأكد من:")
        print("   • تثبيت جميع المتطلبات")
        print("   • إعداد مفاتيح APIs")
        print("   • الاتصال بالإنترنت")
