#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مفصل لـ NewsData.io API
"""

import asyncio
import aiohttp
import sys
import os
import json
from datetime import datetime, timedelta

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from config.settings import BotConfig

async def test_newsdata_api_detailed():
    """اختبار مفصل لـ NewsData.io API"""
    print("📊 اختبار مفصل لـ NewsData.io API...")
    
    api_key = BotConfig.NEWSDATA_KEY
    print(f"المفتاح المستخدم: {api_key[:10]}...")
    
    # اختبار عدة تكوينات مختلفة
    test_configs = [
        {
            'name': 'البحث الأساسي',
            'params': {
                'apikey': api_key,
                'q': 'gaming',
                'language': 'en',
                'size': 10
            }
        },
        {
            'name': 'البحث بالفئة',
            'params': {
                'apikey': api_key,
                'category': 'technology',
                'language': 'en',
                'size': 10
            }
        },
        {
            'name': 'البحث المتقدم',
            'params': {
                'apikey': api_key,
                'q': 'video games OR gaming OR esports',
                'language': 'en',
                'category': 'technology,entertainment',
                'size': 10
            }
        },
        {
            'name': 'البحث بدون فلترة',
            'params': {
                'apikey': api_key,
                'language': 'en',
                'size': 10
            }
        },
        {
            'name': 'البحث بالدومين',
            'params': {
                'apikey': api_key,
                'q': 'gaming',
                'language': 'en',
                'domain': 'ign.com,gamespot.com,polygon.com',
                'size': 10
            }
        }
    ]
    
    base_url = 'https://newsdata.io/api/1/news'
    
    for config in test_configs:
        print(f"\n🔍 اختبار: {config['name']}")
        print(f"   المعاملات: {config['params']}")
        
        try:
            timeout = aiohttp.ClientTimeout(total=30, connect=15)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(base_url, params=config['params']) as response:
                    print(f"   📊 كود الاستجابة: {response.status}")
                    
                    if response.status == 200:
                        try:
                            result = await response.json()
                            print(f"   ✅ استجابة JSON صحيحة")
                            print(f"   📋 مفاتيح الاستجابة: {list(result.keys())}")
                            
                            if 'results' in result:
                                articles = result['results']
                                print(f"   📰 عدد المقالات: {len(articles)}")
                                
                                if articles:
                                    print(f"   📄 عينة من المقالات:")
                                    for i, article in enumerate(articles[:3], 1):
                                        title = article.get('title', 'بدون عنوان')[:60]
                                        source = article.get('source_id', 'مصدر غير محدد')
                                        print(f"      {i}. {title}... (المصدر: {source})")
                                    
                                    # فحص جودة المقالات
                                    gaming_related = 0
                                    for article in articles:
                                        title_content = f"{article.get('title', '')} {article.get('description', '')}"
                                        if any(keyword in title_content.lower() for keyword in ['game', 'gaming', 'video', 'esport', 'console', 'pc', 'mobile']):
                                            gaming_related += 1
                                    
                                    print(f"   🎮 مقالات متعلقة بالألعاب: {gaming_related}/{len(articles)} ({gaming_related/len(articles)*100:.1f}%)")
                                    
                                    return True, config['name'], result
                                else:
                                    print(f"   ⚠️ لا توجد مقالات في النتائج")
                            else:
                                print(f"   ⚠️ لا توجد مفتاح 'results' في الاستجابة")
                                print(f"   📄 محتوى الاستجابة: {result}")
                                
                        except json.JSONDecodeError as e:
                            print(f"   ❌ خطأ في تحليل JSON: {e}")
                            text = await response.text()
                            print(f"   📄 النص الخام: {text[:200]}...")
                            
                    elif response.status == 401:
                        print(f"   ❌ خطأ مصادقة (401) - المفتاح قد يكون غير صحيح")
                        text = await response.text()
                        print(f"   📄 تفاصيل الخطأ: {text}")
                        
                    elif response.status == 403:
                        print(f"   ❌ خطأ صلاحيات (403) - قد تكون هناك قيود على المفتاح")
                        text = await response.text()
                        print(f"   📄 تفاصيل الخطأ: {text}")
                        
                    elif response.status == 429:
                        print(f"   ⚠️ تجاوز حد الطلبات (429)")
                        text = await response.text()
                        print(f"   📄 تفاصيل: {text}")
                        
                    else:
                        print(f"   ⚠️ كود استجابة غير متوقع: {response.status}")
                        text = await response.text()
                        print(f"   📄 نص الاستجابة: {text[:200]}...")
                        
        except aiohttp.ClientConnectorError as e:
            print(f"   ❌ فشل الاتصال: {e}")
        except asyncio.TimeoutError:
            print(f"   ⏰ انتهت مهلة الاتصال")
        except Exception as e:
            print(f"   ❌ خطأ غير متوقع: {e}")
    
    return False, None, None

async def test_newsdata_endpoints():
    """اختبار endpoints مختلفة لـ NewsData.io"""
    print("\n🔍 اختبار endpoints مختلفة...")
    
    api_key = BotConfig.NEWSDATA_KEY
    
    endpoints = [
        'https://newsdata.io/api/1/news',
        'https://newsdata.io/api/1/latest',
        'https://newsdata.io/api/1/archive'
    ]
    
    for endpoint in endpoints:
        print(f"\n📡 اختبار endpoint: {endpoint}")
        
        params = {
            'apikey': api_key,
            'language': 'en',
            'size': 5
        }
        
        try:
            timeout = aiohttp.ClientTimeout(total=20, connect=10)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(endpoint, params=params) as response:
                    print(f"   📊 كود الاستجابة: {response.status}")
                    
                    if response.status == 200:
                        result = await response.json()
                        if 'results' in result and result['results']:
                            print(f"   ✅ يعمل - {len(result['results'])} مقال")
                        else:
                            print(f"   ⚠️ يعمل لكن بدون نتائج")
                    else:
                        text = await response.text()
                        print(f"   ❌ لا يعمل: {text[:100]}...")
                        
        except Exception as e:
            print(f"   ❌ خطأ: {e}")

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 بدء اختبار مفصل لـ NewsData.io API...")
    print("=" * 60)
    
    if not BotConfig.NEWSDATA_KEY:
        print("❌ مفتاح NewsData.io API غير موجود في التكوين")
        return False
    
    # اختبار التكوينات المختلفة
    success, working_config, result = await test_newsdata_api_detailed()
    
    # اختبار endpoints مختلفة
    await test_newsdata_endpoints()
    
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار:")
    print("-" * 30)
    
    if success:
        print("🎉 NewsData.io API يعمل بنجاح!")
        print(f"✅ التكوين الذي يعمل: {working_config}")
        print("✅ تم الحصول على مقالات بنجاح")
        
        print("\n💡 توصيات للتحسين:")
        print("   • استخدم التكوين الذي يعمل في الكود الرئيسي")
        print("   • أضف فلترة أفضل للمقالات المتعلقة بالألعاب")
        print("   • استخدم domains محددة لتحسين الجودة")
        
        return True
    else:
        print("❌ NewsData.io API لا يعمل حالياً")
        print("\n💡 الأسباب المحتملة:")
        print("   • المفتاح قد يكون منتهي الصلاحية")
        print("   • قد تكون هناك قيود على الاستخدام")
        print("   • معاملات البحث قد تكون غير صحيحة")
        print("   • قد تكون هناك مشاكل في الخدمة")
        
        print("\n🔧 الحلول المقترحة:")
        print("   • تحقق من صحة المفتاح في لوحة تحكم NewsData.io")
        print("   • جرب معاملات بحث مختلفة")
        print("   • تحقق من حدود الاستخدام")
        print("   • راجع وثائق NewsData.io API")
        
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
