#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مبسط لنظام إدارة الصور الذكي
"""

import asyncio
import sys
import os

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.smart_image_manager import SmartImageManager, ImageGenerationPolicy

async def test_simple_image_generation():
    """اختبار بسيط لإنشاء الصور"""
    print("🧪 اختبار بسيط لنظام إدارة الصور الذكي...")
    print("=" * 50)
    
    # إنشاء سياسة مخصصة للاختبار
    test_policy = ImageGenerationPolicy(
        max_images_per_article=1,
        max_daily_generations=5,  # حد منخفض للاختبار
        min_article_quality_score=3.0,  # حد منخفض للاختبار
        cache_duration_hours=1,
        reuse_similar_images=True
    )
    
    # إنشاء مدير الصور الذكي
    smart_manager = SmartImageManager(test_policy)
    
    print(f"📋 إعدادات الاختبار:")
    print(f"   حد أقصى صور/يوم: {test_policy.max_daily_generations}")
    print(f"   حد أدنى جودة المقال: {test_policy.min_article_quality_score}/10")
    
    # مقال اختبار بسيط
    test_article = {
        'title': 'New Gaming Console Released - PlayStation 6 Announcement',
        'content': 'Sony has announced the PlayStation 6 gaming console with revolutionary features. The new console promises 8K gaming, ray tracing, and ultra-fast loading times. Gaming enthusiasts are excited about the upcoming release.',
        'keywords': ['PlayStation 6', 'gaming', 'console', 'Sony', 'announcement']
    }
    
    print(f"\n📰 اختبار المقال: {test_article['title'][:50]}...")
    
    try:
        # حساب جودة المقال
        quality_score = smart_manager._calculate_article_quality_score(test_article)
        print(f"   نقاط الجودة: {quality_score:.1f}/10")
        
        # فحص ما إذا كان يجب إنشاء صورة
        should_generate, reason = smart_manager._should_generate_image(test_article)
        print(f"   قرار الإنشاء: {'✅ نعم' if should_generate else '❌ لا'}")
        print(f"   السبب: {reason}")
        
        if should_generate:
            # محاولة إنشاء صورة
            print(f"\n🎨 محاولة إنشاء صورة...")
            image_result = await smart_manager.generate_smart_image_for_article(test_article)
            
            if image_result:
                print(f"✅ تم إنشاء صورة بنجاح!")
                print(f"   الرابط: {image_result.get('url', 'N/A')[:80]}...")
                print(f"   المصدر: {image_result.get('source', 'Unknown')}")
                print(f"   طريقة الإنشاء: {image_result.get('generation_method', 'Unknown')}")
                print(f"   API المستخدم: {image_result.get('api_used', 'Unknown')}")
                
                # عرض إحصائيات
                stats = smart_manager.get_daily_stats()
                print(f"\n📊 إحصائيات:")
                print(f"   الصور المُنشأة اليوم: {stats['images_generated']}/{stats['policy']['max_daily_generations']}")
                print(f"   المقالات المُعالجة: {stats['articles_processed']}")
                print(f"   معدل التخزين المؤقت: {stats['cache_hit_rate']:.1f}%")
                
                return True
            else:
                print(f"❌ فشل في إنشاء الصورة")
                return False
        else:
            print(f"⏭️ تم تخطي إنشاء الصورة حسب السياسة")
            return True  # نعتبرها نجاح لأن السياسة تعمل
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

async def test_quality_filtering():
    """اختبار فلترة الجودة"""
    print(f"\n🎯 اختبار فلترة الجودة...")
    
    smart_manager = SmartImageManager()
    
    # مقالات بجودات مختلفة
    test_articles = [
        {
            'title': 'Short',
            'content': 'Very short content.',
            'keywords': [],
            'expected_quality': 'low'
        },
        {
            'title': 'High Quality Gaming Review - Comprehensive Analysis',
            'content': 'This is a detailed gaming review with comprehensive analysis of gameplay mechanics, graphics quality, sound design, and overall user experience. The review covers multiple aspects including performance benchmarks, comparison with similar games, and recommendations for different types of players.',
            'keywords': ['gaming', 'review', 'analysis', 'comprehensive'],
            'expected_quality': 'high'
        }
    ]
    
    for article in test_articles:
        quality_score = smart_manager._calculate_article_quality_score(article)
        should_generate, reason = smart_manager._should_generate_image(article)
        
        print(f"📰 '{article['title'][:30]}...'")
        print(f"   جودة متوقعة: {article['expected_quality']}")
        print(f"   نقاط الجودة: {quality_score:.1f}/10")
        print(f"   قرار الإنشاء: {'✅' if should_generate else '❌'}")
        print(f"   السبب: {reason}")

def test_caching_logic():
    """اختبار منطق التخزين المؤقت"""
    print(f"\n🗄️ اختبار منطق التخزين المؤقت...")
    
    smart_manager = SmartImageManager()
    
    # مقالات مشابهة
    article1 = {
        'title': 'PlayStation 5 Game Announcement',
        'keywords': ['PlayStation 5', 'gaming', 'announcement']
    }
    
    article2 = {
        'title': 'PlayStation 5 Exclusive Game News',
        'keywords': ['PlayStation 5', 'gaming', 'exclusive']
    }
    
    hash1 = smart_manager._generate_content_hash(article1)
    hash2 = smart_manager._generate_content_hash(article2)
    
    print(f"📰 المقال 1: {article1['title']}")
    print(f"   Hash: {hash1[:16]}...")
    
    print(f"📰 المقال 2: {article2['title']}")
    print(f"   Hash: {hash2[:16]}...")
    
    if hash1 == hash2:
        print("✅ المقالات المشابهة لها نفس الـ hash")
    else:
        print("⚠️ المقالات المشابهة لها hash مختلف")

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء الاختبار المبسط لنظام إدارة الصور الذكي...")
    
    try:
        # اختبار إنشاء الصور البسيط
        generation_success = await test_simple_image_generation()
        
        # اختبار فلترة الجودة
        await test_quality_filtering()
        
        # اختبار منطق التخزين المؤقت
        test_caching_logic()
        
        print("\n" + "=" * 50)
        print("📋 ملخص الاختبار المبسط:")
        print("-" * 25)
        
        if generation_success:
            print("✅ نظام إدارة الصور الذكي يعمل بنجاح!")
            print("\n💡 الميزات المحققة:")
            print("   • تحسين استهلاك API (صورة واحدة لكل مقال)")
            print("   • فلترة جودة المقالات")
            print("   • نظام تخزين مؤقت")
            print("   • إدارة حصة يومية")
            print("   • صور احتياطية عالية الجودة")
            
            print("\n🎯 الفوائد المحققة:")
            print("   • تقليل استهلاك API من 6 صور إلى 1 صورة لكل مقال")
            print("   • تحسين جودة الصور المُنشأة")
            print("   • ربط أفضل بموضوع المقال")
            print("   • إعادة استخدام ذكية للصور المشابهة")
            
            return True
        else:
            print("⚠️ نظام إدارة الصور يحتاج مزيد من التحسين")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
