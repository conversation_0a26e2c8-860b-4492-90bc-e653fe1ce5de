#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار APIs الحقيقية المتوفرة
"""

import asyncio
import sys
import os
from datetime import datetime

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.api_integrations import APIIntegrationManager, CoreWebVitalsAnalyzer, KeywordResearchAPI
from modules.microsoft_clarity import MicrosoftClarityAnalyzer
from modules.ubersuggest_api import UbersuggestAPI
from config.api_config import APIConfig

class RealAPITester:
    """فئة اختبار APIs الحقيقية"""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = datetime.now()
        
        # مفاتيح APIs الحقيقية
        self.google_pagespeed_key = 'AIzaSyDb18JC4vCNcsjCbOUPR2iqzOVQ-Oa_OUY'
        self.google_search_console_key = 'AIzaSyCMRxWg6Egl7_nv6sdQvcXJkgHUNoQM_gY'
        self.google_analytics_key = 'AIzaSyBLpimSmuTirR3kLGyaxB1BntcXJQZa12w'
        self.clarity_id = 'sedxi61jhb'
        self.ubersuggest_key = '24d5c97abaee9b5c8230d1bb50f796de8dd3c629'
    
    async def run_real_api_tests(self):
        """تشغيل اختبار APIs الحقيقية"""
        print("🚀 بدء اختبار APIs الحقيقية المتوفرة...\n")
        
        # فحص توفر المفاتيح
        await self.check_api_availability()
        
        tests = [
            ("Google PageSpeed Insights", self.test_google_pagespeed),
            ("Microsoft Clarity Analytics", self.test_microsoft_clarity),
            ("Ubersuggest Keyword Research", self.test_ubersuggest_keywords),
            ("API Configuration", self.test_api_configuration),
            ("Performance Integration", self.test_performance_integration)
        ]
        
        for test_name, test_func in tests:
            try:
                print(f"🔍 اختبار {test_name}...")
                result = await test_func()
                self.test_results[test_name] = result
                
                if result['success']:
                    print(f"✅ {test_name}: نجح")
                    if 'details' in result:
                        for key, value in result['details'].items():
                            print(f"   • {key}: {value}")
                else:
                    print(f"❌ {test_name}: فشل - {result.get('error', 'خطأ غير محدد')}")
                
                print()
                
            except Exception as e:
                print(f"❌ {test_name}: خطأ في الاختبار - {e}\n")
                self.test_results[test_name] = {'success': False, 'error': str(e)}
        
        await self.generate_real_api_report()
    
    async def check_api_availability(self):
        """فحص توفر مفاتيح APIs"""
        print("🔑 فحص توفر مفاتيح APIs...")
        
        apis_status = {
            'Google PageSpeed': bool(self.google_pagespeed_key),
            'Google Search Console': bool(self.google_search_console_key),
            'Google Analytics': bool(self.google_analytics_key),
            'Microsoft Clarity': bool(self.clarity_id),
            'Ubersuggest': bool(self.ubersuggest_key)
        }
        
        for api_name, is_available in apis_status.items():
            status = "✅ متوفر" if is_available else "❌ غير متوفر"
            print(f"  • {api_name}: {status}")
        
        available_count = sum(apis_status.values())
        total_count = len(apis_status)
        
        print(f"\n📊 الإحصائيات: {available_count}/{total_count} APIs متوفرة ({(available_count/total_count)*100:.1f}%)\n")
    
    async def test_google_pagespeed(self):
        """اختبار Google PageSpeed Insights API"""
        try:
            async with APIIntegrationManager() as api_manager:
                # تحديث مفتاح API
                api_manager.api_keys['google_pagespeed_key'] = self.google_pagespeed_key
                
                analyzer = CoreWebVitalsAnalyzer(api_manager)
                
                # اختبار موقع حقيقي
                test_url = "https://www.google.com"
                analysis = await analyzer.analyze_page_performance(test_url)
                
                if analysis and 'core_web_vitals' in analysis:
                    vitals = analysis['core_web_vitals']
                    
                    return {
                        'success': True,
                        'message': 'Google PageSpeed API يعمل بنجاح',
                        'details': {
                            'URL المختبر': test_url,
                            'LCP': f"{vitals.get('lcp', {}).get('value', 0)} ثانية",
                            'FID': f"{vitals.get('fid', {}).get('value', 0)} مللي ثانية",
                            'CLS': f"{vitals.get('cls', {}).get('value', 0)} نقاط",
                            'النقاط الإجمالية': f"{analysis.get('overall_score', 0)}/100",
                            'حالة الأداء': vitals.get('lcp', {}).get('status', 'غير محدد')
                        }
                    }
                else:
                    return {
                        'success': False,
                        'error': 'لم يتم الحصول على بيانات صحيحة من API'
                    }
                    
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_microsoft_clarity(self):
        """اختبار Microsoft Clarity"""
        try:
            clarity = MicrosoftClarityAnalyzer(self.clarity_id)
            
            # توليد كود Clarity
            clarity_script = clarity.generate_clarity_script("https://your-gaming-website.com")
            
            # الحصول على رؤى محاكاة
            insights = await clarity.get_clarity_insights(days=7)
            
            if insights and 'analysis' in insights:
                analysis = insights['analysis']
                engagement = analysis.get('user_engagement', {})
                
                return {
                    'success': True,
                    'message': 'Microsoft Clarity يعمل بنجاح',
                    'details': {
                        'Clarity ID': self.clarity_id,
                        'كود JavaScript': 'تم توليده بنجاح',
                        'نقاط التفاعل': f"{engagement.get('engagement_score', 0)}/100",
                        'مستوى التفاعل': engagement.get('level', 'غير محدد'),
                        'معدل الارتداد': f"{engagement.get('bounce_rate_percentage', 0):.1f}%",
                        'متوسط مدة الجلسة': f"{engagement.get('avg_session_duration', 0)} ثانية",
                        'عدد التوصيات': len(insights.get('recommendations', []))
                    }
                }
            else:
                return {
                    'success': False,
                    'error': 'فشل في الحصول على رؤى Clarity'
                }
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_ubersuggest_keywords(self):
        """اختبار Ubersuggest API"""
        try:
            ubersuggest = UbersuggestAPI(self.ubersuggest_key)
            
            # اختبار بحث الكلمات المفتاحية
            keyword_results = await ubersuggest.get_keyword_suggestions('gaming news', 'ar', 'SA')
            
            if keyword_results and 'suggestions' in keyword_results:
                suggestions = keyword_results['suggestions']
                analysis = keyword_results.get('analysis', {})
                
                # اختبار أفكار المحتوى
                content_ideas = await ubersuggest.get_content_ideas('minecraft', 'article')
                
                return {
                    'success': True,
                    'message': 'Ubersuggest API يعمل بنجاح',
                    'details': {
                        'الكلمة المفتاحية': 'gaming news',
                        'عدد الاقتراحات': len(suggestions),
                        'إجمالي حجم البحث': analysis.get('total_search_volume', 0),
                        'متوسط الصعوبة': f"{analysis.get('average_difficulty', 0):.1f}",
                        'فرص عالية القيمة': analysis.get('high_value_opportunities', 0),
                        'كلمات Long-tail': analysis.get('long_tail_keywords', 0),
                        'أفكار المحتوى': len(content_ideas.get('ideas', [])),
                        'أفضل اقتراح': suggestions[0]['keyword'] if suggestions else 'لا يوجد'
                    }
                }
            else:
                return {
                    'success': False,
                    'error': 'فشل في الحصول على اقتراحات الكلمات المفتاحية'
                }
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_api_configuration(self):
        """اختبار إعدادات APIs"""
        try:
            # فحص الإعدادات
            validation = APIConfig.validate_configuration()
            available_apis = APIConfig.get_available_apis()
            
            # حساب الإحصائيات
            total_apis = len(available_apis)
            available_count = sum(available_apis.values())
            availability_rate = (available_count / total_apis) * 100
            
            return {
                'success': validation['valid'] or available_count > 0,
                'message': 'إعدادات APIs تم فحصها',
                'details': {
                    'APIs متوفرة': f"{available_count}/{total_apis}",
                    'نسبة التوفر': f"{availability_rate:.1f}%",
                    'إعدادات صحيحة': validation['valid'],
                    'عدد الأخطاء': len(validation.get('errors', [])),
                    'عدد التحذيرات': len(validation.get('warnings', [])),
                    'APIs أساسية متوفرة': available_apis.get('google', False),
                    'APIs متقدمة متوفرة': any([
                        available_apis.get('google_pagespeed', False),
                        available_apis.get('semrush', False),
                        available_apis.get('ahrefs', False)
                    ])
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_performance_integration(self):
        """اختبار تكامل مراقبة الأداء"""
        try:
            from modules.performance_monitor import PerformanceMonitor
            
            monitor = PerformanceMonitor()
            
            # اختبار مراقبة Core Web Vitals
            test_url = "https://www.google.com"
            vitals_report = await monitor._monitor_core_web_vitals(test_url)
            
            # اختبار مراقبة SEO
            seo_report = await monitor._monitor_seo_performance(test_url)
            
            # اختبار مراقبة الكلمات المفتاحية
            keyword_report = await monitor._monitor_keyword_rankings()
            
            # حساب النجاح
            successful_reports = sum(1 for report in [vitals_report, seo_report, keyword_report] if report)
            
            return {
                'success': successful_reports > 0,
                'message': 'تكامل مراقبة الأداء يعمل',
                'details': {
                    'تقارير ناجحة': f"{successful_reports}/3",
                    'تقرير Core Web Vitals': bool(vitals_report),
                    'تقرير SEO': bool(seo_report),
                    'تقرير الكلمات المفتاحية': bool(keyword_report),
                    'نقاط الأداء الإجمالية': vitals_report.get('average_scores', {}).get('avg_overall_score', 0) if vitals_report else 0,
                    'حالة المراقبة': 'نشط' if successful_reports == 3 else 'جزئي'
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def generate_real_api_report(self):
        """إنشاء تقرير اختبار APIs الحقيقية"""
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results.values() if result.get('success', False))
        success_rate = (successful_tests / total_tests) * 100 if total_tests > 0 else 0
        
        duration = (datetime.now() - self.start_time).total_seconds()
        
        print("\n" + "="*80)
        print("📊 تقرير اختبار APIs الحقيقية")
        print("="*80)
        
        print(f"🎯 معدل النجاح الإجمالي: {success_rate:.1f}% ({successful_tests}/{total_tests})")
        print(f"⏱️ مدة الاختبار: {duration:.1f} ثانية")
        print(f"📅 تاريخ الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        print("\n🔑 مفاتيح APIs المستخدمة:")
        print(f"  • Google PageSpeed: {self.google_pagespeed_key[:20]}...")
        print(f"  • Google Search Console: {self.google_search_console_key[:20]}...")
        print(f"  • Google Analytics: {self.google_analytics_key[:20]}...")
        print(f"  • Microsoft Clarity: {self.clarity_id}")
        print(f"  • Ubersuggest: {self.ubersuggest_key[:20]}...")
        
        print("\n📋 تفاصيل النتائج:")
        for test_name, result in self.test_results.items():
            status = "✅ نجح" if result.get('success', False) else "❌ فشل"
            print(f"  • {test_name}: {status}")
            
            if result.get('success') and 'details' in result:
                for key, value in result['details'].items():
                    print(f"    - {key}: {value}")
        
        # تقييم عام
        if success_rate >= 90:
            grade = "A+ ممتاز"
            message = "🎉 جميع APIs الحقيقية تعمل بكفاءة استثنائية!"
        elif success_rate >= 80:
            grade = "A جيد جداً"
            message = "👍 معظم APIs الحقيقية تعمل بشكل ممتاز"
        elif success_rate >= 60:
            grade = "B جيد"
            message = "⚠️ APIs تعمل بشكل جيد مع إمكانية تحسينات"
        else:
            grade = "C يحتاج تطوير"
            message = "🔧 يحتاج إلى مراجعة بعض APIs"
        
        print(f"\n🏆 التقييم العام: {grade}")
        print(f"💬 التوصية: {message}")
        
        # توصيات التحسين
        print(f"\n🚀 الخطوات التالية:")
        if success_rate >= 80:
            print("  • APIs جاهزة للاستخدام في الإنتاج")
            print("  • يمكن تفعيل المراقبة المستمرة")
            print("  • إضافة المزيد من APIs المدفوعة للحصول على بيانات أدق")
        else:
            print("  • مراجعة مفاتيح APIs التي فشلت")
            print("  • التأكد من صحة الإعدادات")
            print("  • اختبار الاتصال بالإنترنت")
        
        print("  • دمج APIs في النظام الرئيسي")
        print("  • إعداد تنبيهات للمشاكل")
        print("  • مراقبة استهلاك APIs")

async def main():
    """الدالة الرئيسية"""
    tester = RealAPITester()
    await tester.run_real_api_tests()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⌨️ تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
