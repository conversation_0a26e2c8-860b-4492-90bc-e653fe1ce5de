import asyncio
from modules.youtube_analyzer import YouTubeAnalyzer
from config.settings import BotConfig
from modules.logger import logger

def sync_test():
    """
    يقوم هذا الاختبار باستدعاء وظيفة استخراج النص مباشرة
    للتحقق من أن الاتصال بخدمة Txtify يعمل بشكل صحيح.
    """
    logger.info("🚀 بدء اختبار ميزة استخراج النص من يوتيوب...")

    # التأكد من وجود رابط الخدمة في الإعدادات
    txtify_url = BotConfig.TXTIFY_API_URL
    if not txtify_url:
        logger.error("❌ لم يتم العثور على TXTIFY_API_URL في الإعدادات.")
        return

    logger.info(f"🔗 استخدام خدمة Txtify على الرابط: {txtify_url}")

    # تهيئة محلل يوتيوب
    analyzer = YouTubeAnalyzer()

    # رابط الفيديو للاختبار
    test_video_url = "https://youtu.be/NBc7Sx2VB3M"
    logger.info(f"📹 اختبار الفيديو: {test_video_url}")

    # استدعاء الدالة والحصول على النص
    transcript = analyzer.get_transcript_from_txtify(test_video_url, txtify_url)

    # طباعة النتيجة
    if transcript:
        logger.info("✅ نجح الاختبار! تم استلام النص التالي:")
        print("--- بداية النص ---")
        print(transcript)
        print("--- نهاية النص ---")
    else:
        logger.error("❌ فشل الاختبار. لم يتم استلام أي نص. يرجى مراجعة سجل الأخطاء.")

if __name__ == "__main__":
    sync_test()
