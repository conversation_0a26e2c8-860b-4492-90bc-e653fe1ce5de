#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مواقع الألعاب وتحديث CSS selectors
"""

import asyncio
import aiohttp
import sys
import os
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import json

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger

class GamingSitesDiagnostic:
    """أداة تشخيص مواقع الألعاب"""
    
    def __init__(self):
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        ]
        
        # مواقع الألعاب للفحص
        self.gaming_sites = {
            'ign.com': {
                'test_urls': [
                    'https://www.ign.com/news',
                    'https://www.ign.com/articles'
                ],
                'current_selectors': {
                    'title': 'h1.article-headline, h1.display-title',
                    'content': '.article-content, .article-body',
                    'date': '.publish-date, time',
                    'author': '.author-name, .byline'
                }
            },
            'gamespot.com': {
                'test_urls': [
                    'https://www.gamespot.com/news/',
                    'https://www.gamespot.com/articles/'
                ],
                'current_selectors': {
                    'title': 'h1.js-article-title',
                    'content': '.js-content-entity-body',
                    'date': '.js-publish-date',
                    'author': '.js-author-name'
                }
            },
            'polygon.com': {
                'test_urls': [
                    'https://www.polygon.com/news',
                    'https://www.polygon.com/reviews'
                ],
                'current_selectors': {
                    'title': 'h1.duet--article--dangerously-set-cms-markup',
                    'content': '.duet--article--article-body',
                    'date': 'time',
                    'author': '.author-name'
                }
            },
            'kotaku.com': {
                'test_urls': [
                    'https://kotaku.com/news',
                    'https://kotaku.com/reviews'
                ],
                'current_selectors': {
                    'title': 'h1.headline',
                    'content': '.post-content',
                    'date': '.publish-date',
                    'author': '.author'
                }
            },
            'eurogamer.net': {
                'test_urls': [
                    'https://www.eurogamer.net/news',
                    'https://www.eurogamer.net/reviews'
                ],
                'current_selectors': {
                    'title': 'h1.article_header',
                    'content': '.article_body_content',
                    'date': '.article_date',
                    'author': '.article_author'
                }
            }
        }
    
    async def diagnose_site(self, domain: str, site_config: dict) -> dict:
        """تشخيص موقع واحد"""
        print(f"\n🔍 تشخيص {domain}...")
        
        results = {
            'domain': domain,
            'accessible': False,
            'article_links_found': 0,
            'selector_tests': {},
            'suggested_selectors': {},
            'sample_articles': []
        }
        
        headers = {
            'User-Agent': self.user_agents[0],
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive'
        }
        
        try:
            timeout = aiohttp.ClientTimeout(total=30, connect=15)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                
                # اختبار الوصول للموقع
                for test_url in site_config['test_urls']:
                    try:
                        print(f"   📡 اختبار: {test_url}")
                        
                        async with session.get(test_url, headers=headers) as response:
                            if response.status == 200:
                                results['accessible'] = True
                                html_content = await response.text()
                                soup = BeautifulSoup(html_content, 'html.parser')
                                
                                # البحث عن روابط المقالات
                                article_links = self._find_article_links(soup, domain)
                                results['article_links_found'] = len(article_links)
                                
                                print(f"   📰 وجد {len(article_links)} رابط مقال")
                                
                                # اختبار selectors الحالية
                                selector_results = self._test_selectors(soup, site_config['current_selectors'])
                                results['selector_tests'] = selector_results
                                
                                # اقتراح selectors جديدة
                                suggested = self._suggest_new_selectors(soup)
                                results['suggested_selectors'] = suggested
                                
                                # اختبار مقال واحد إذا وُجدت روابط
                                if article_links:
                                    sample_article = await self._test_article_extraction(
                                        session, article_links[0], site_config['current_selectors'], headers
                                    )
                                    if sample_article:
                                        results['sample_articles'].append(sample_article)
                                
                                break  # نجح الاختبار، لا حاجة لاختبار URLs أخرى
                                
                            else:
                                print(f"   ❌ خطأ {response.status}")
                                
                    except Exception as e:
                        print(f"   ⚠️ خطأ في {test_url}: {e}")
                        continue
                        
        except Exception as e:
            print(f"   ❌ خطأ عام في {domain}: {e}")
        
        return results
    
    def _find_article_links(self, soup: BeautifulSoup, domain: str) -> list:
        """البحث عن روابط المقالات في الصفحة"""
        article_links = []
        
        # أنماط روابط مختلفة للمقالات
        link_patterns = [
            'a[href*="/article"]',
            'a[href*="/news"]',
            'a[href*="/review"]',
            'a[href*="/feature"]',
            'a[href*="/story"]',
            'a.article-link',
            'a.news-link',
            '.article-title a',
            '.news-title a',
            'h2 a', 'h3 a'
        ]
        
        for pattern in link_patterns:
            links = soup.select(pattern)
            for link in links:
                href = link.get('href')
                if href and self._is_valid_article_link(href, domain):
                    full_url = urljoin(f"https://{domain}", href)
                    if full_url not in article_links:
                        article_links.append(full_url)
        
        return article_links[:10]  # أول 10 روابط
    
    def _is_valid_article_link(self, href: str, domain: str) -> bool:
        """فحص صحة رابط المقال"""
        if not href:
            return False
        
        # تجنب الروابط غير المرغوبة
        avoid_patterns = [
            'javascript:', 'mailto:', '#', '/tag/', '/category/',
            '/author/', '/search', '/login', '/register'
        ]
        
        for pattern in avoid_patterns:
            if pattern in href.lower():
                return False
        
        # يجب أن يحتوي على كلمات مفتاحية للمقالات
        article_indicators = [
            'article', 'news', 'review', 'feature', 'story', 'post'
        ]
        
        return any(indicator in href.lower() for indicator in article_indicators)
    
    def _test_selectors(self, soup: BeautifulSoup, selectors: dict) -> dict:
        """اختبار selectors الحالية"""
        results = {}
        
        for element_type, selector_string in selectors.items():
            results[element_type] = {
                'found': False,
                'count': 0,
                'sample_text': ''
            }
            
            # تجربة selectors متعددة مفصولة بفاصلة
            selector_list = [s.strip() for s in selector_string.split(',')]
            
            for selector in selector_list:
                try:
                    elements = soup.select(selector)
                    if elements:
                        results[element_type]['found'] = True
                        results[element_type]['count'] = len(elements)
                        
                        # أخذ عينة من النص
                        sample_element = elements[0]
                        sample_text = sample_element.get_text(strip=True)[:100]
                        results[element_type]['sample_text'] = sample_text
                        break
                        
                except Exception as e:
                    continue
        
        return results
    
    def _suggest_new_selectors(self, soup: BeautifulSoup) -> dict:
        """اقتراح selectors جديدة بناءً على تحليل الصفحة"""
        suggestions = {}
        
        # اقتراحات للعناوين
        title_candidates = [
            'h1', 'h1.title', 'h1.headline', 'h1.article-title',
            '.article-headline', '.post-title', '.entry-title'
        ]
        
        suggestions['title'] = self._find_best_selector(soup, title_candidates, 'title')
        
        # اقتراحات للمحتوى
        content_candidates = [
            '.article-content', '.post-content', '.entry-content',
            '.article-body', '.post-body', '.content', 'article'
        ]
        
        suggestions['content'] = self._find_best_selector(soup, content_candidates, 'content')
        
        # اقتراحات للتاريخ
        date_candidates = [
            'time', '.date', '.publish-date', '.article-date',
            '.post-date', '[datetime]'
        ]
        
        suggestions['date'] = self._find_best_selector(soup, date_candidates, 'date')
        
        # اقتراحات للمؤلف
        author_candidates = [
            '.author', '.author-name', '.byline', '.post-author',
            '.article-author', '[rel="author"]'
        ]
        
        suggestions['author'] = self._find_best_selector(soup, author_candidates, 'author')
        
        return suggestions
    
    def _find_best_selector(self, soup: BeautifulSoup, candidates: list, element_type: str) -> str:
        """العثور على أفضل selector من قائمة المرشحين"""
        for candidate in candidates:
            try:
                elements = soup.select(candidate)
                if elements:
                    # فحص جودة النتيجة
                    sample_text = elements[0].get_text(strip=True)
                    if len(sample_text) > 5:  # يجب أن يحتوي على نص مفيد
                        return candidate
            except Exception:
                continue
        
        return ''
    
    async def _test_article_extraction(self, session, article_url: str, selectors: dict, headers: dict) -> dict:
        """اختبار استخراج مقال واحد"""
        try:
            async with session.get(article_url, headers=headers) as response:
                if response.status == 200:
                    html_content = await response.text()
                    soup = BeautifulSoup(html_content, 'html.parser')
                    
                    extracted_data = {}
                    
                    for element_type, selector_string in selectors.items():
                        extracted_data[element_type] = self._extract_with_selectors(soup, selector_string)
                    
                    return {
                        'url': article_url,
                        'extracted_data': extracted_data,
                        'success': any(extracted_data.values())
                    }
        except Exception as e:
            print(f"   ⚠️ خطأ في استخراج المقال: {e}")
        
        return None
    
    def _extract_with_selectors(self, soup: BeautifulSoup, selectors: str) -> str:
        """استخراج النص باستخدام CSS selectors متعددة"""
        if not selectors:
            return ''

        selector_list = [s.strip() for s in selectors.split(',')]

        for selector in selector_list:
            try:
                element = soup.select_one(selector)
                if element:
                    text = element.get_text(strip=True)
                    if text and len(text) > 5:
                        return text[:200]  # أول 200 حرف
            except Exception:
                continue

        return ''

async def main():
    """الدالة الرئيسية للتشخيص"""
    print("🔍 بدء تشخيص مواقع الألعاب...")
    print("=" * 60)
    
    diagnostic = GamingSitesDiagnostic()
    results = {}
    
    for domain, config in diagnostic.gaming_sites.items():
        result = await diagnostic.diagnose_site(domain, config)
        results[domain] = result
        
        # عرض النتائج
        print(f"\n📊 نتائج {domain}:")
        print(f"   الوصول: {'✅ متاح' if result['accessible'] else '❌ غير متاح'}")
        print(f"   روابط المقالات: {result['article_links_found']}")
        
        if result['selector_tests']:
            print(f"   اختبار Selectors الحالية:")
            for element_type, test_result in result['selector_tests'].items():
                status = '✅' if test_result['found'] else '❌'
                print(f"      {element_type}: {status} ({test_result['count']} عنصر)")
        
        if result['suggested_selectors']:
            print(f"   Selectors المقترحة:")
            for element_type, suggestion in result['suggested_selectors'].items():
                if suggestion:
                    print(f"      {element_type}: {suggestion}")
    
    # حفظ النتائج
    with open('gaming_sites_diagnostic_report.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 تم حفظ التقرير في gaming_sites_diagnostic_report.json")
    
    # إنشاء selectors محدثة
    updated_selectors = {}
    for domain, result in results.items():
        if result['accessible'] and result['suggested_selectors']:
            updated_selectors[domain] = {
                'selectors': result['suggested_selectors'],
                'rate_limit': 2
            }
    
    if updated_selectors:
        with open('updated_gaming_sites_selectors.json', 'w', encoding='utf-8') as f:
            json.dump(updated_selectors, f, indent=2, ensure_ascii=False)
        
        print(f"💾 تم حفظ Selectors المحدثة في updated_gaming_sites_selectors.json")
    
    print("\n" + "=" * 60)
    print("📋 ملخص التشخيص:")
    
    accessible_sites = sum(1 for result in results.values() if result['accessible'])
    total_sites = len(results)
    
    print(f"   المواقع المتاحة: {accessible_sites}/{total_sites}")
    print(f"   إجمالي روابط المقالات: {sum(result['article_links_found'] for result in results.values())}")
    
    if accessible_sites > 0:
        print("\n🎉 تم تشخيص المواقع بنجاح!")
        return True
    else:
        print("\n⚠️ لم يتم الوصول لأي موقع")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التشخيص بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
