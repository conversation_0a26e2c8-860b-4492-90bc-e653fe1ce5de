#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار SerpAPI عبر RapidAPI
"""

import asyncio
import sys
import os

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.serpapi_search import serpapi_search
from modules.logger import logger

async def test_serpapi():
    """اختبار شامل لـ SerpAPI"""
    
    print("🔍 اختبار SerpAPI عبر RapidAPI")
    print("=" * 50)
    
    # 1. اختبار الاتصال
    print("\n1️⃣ اختبار الاتصال...")
    connection_test = await serpapi_search.test_connection()
    print(f"حالة الاتصال: {connection_test['status']}")
    print(f"الرسالة: {connection_test['message']}")
    print(f"صحة المفتاح: {connection_test['api_key_valid']}")
    
    if not connection_test['api_key_valid']:
        print("❌ فشل في الاتصال بـ SerpAPI")
        return
    
    # 2. اختبار البحث الأساسي
    print("\n2️⃣ اختبار البحث الأساسي...")
    basic_results = await serpapi_search.search("gaming news", num_results=5)
    print(f"عدد النتائج: {len(basic_results)}")
    
    if basic_results:
        print("\nأول نتيجة:")
        first_result = basic_results[0]
        print(f"العنوان: {first_result.get('title', '')[:80]}...")
        print(f"الرابط: {first_result.get('link', '')}")
        print(f"المصدر: {first_result.get('source', '')}")
        print(f"درجة الصلة: {first_result.get('relevance_score', 0)}")
    
    # 3. اختبار البحث في الأخبار
    print("\n3️⃣ اختبار البحث في الأخبار...")
    news_results = await serpapi_search.search(
        "video game updates", 
        num_results=3,
        tbm='nws'  # البحث في الأخبار
    )
    print(f"عدد الأخبار: {len(news_results)}")
    
    if news_results:
        print("\nأخبار الألعاب:")
        for i, result in enumerate(news_results[:3], 1):
            print(f"{i}. {result.get('title', '')[:60]}...")
            print(f"   المصدر: {result.get('source', '')}")
            print(f"   التاريخ: {result.get('date', '')}")
    
    # 4. اختبار البحث المتقدم
    print("\n4️⃣ اختبار البحث المتقدم...")
    advanced_results = await serpapi_search.search(
        "new game releases 2025",
        num_results=5,
        tbm='nws',
        tbs='qdr:w',  # الأسبوع الماضي
        hl='en',
        gl='us'
    )
    print(f"عدد النتائج المتقدمة: {len(advanced_results)}")
    
    # 5. عرض الإحصائيات
    print("\n5️⃣ إحصائيات الاستخدام:")
    stats = serpapi_search.get_usage_stats()
    print(f"إجمالي البحثات: {stats['total_searches']}")
    print(f"البحثات الناجحة: {stats['successful_searches']}")
    print(f"البحثات الفاشلة: {stats['failed_searches']}")
    print(f"معدل النجاح: {stats['success_rate']:.1f}%")
    print(f"الاستخدام اليومي: {stats['daily_usage']}/{stats['daily_limit']}")
    print(f"حجم التخزين المؤقت: {stats['cache_size']}")
    
    # 6. اختبار التخزين المؤقت
    print("\n6️⃣ اختبار التخزين المؤقت...")
    print("البحث الأول...")
    cache_test1 = await serpapi_search.search("gaming", num_results=2)
    print("البحث الثاني (نفس الاستعلام)...")
    cache_test2 = await serpapi_search.search("gaming", num_results=2)
    
    if len(cache_test1) == len(cache_test2):
        print("✅ التخزين المؤقت يعمل بشكل صحيح")
    else:
        print("⚠️ قد تكون هناك مشكلة في التخزين المؤقت")
    
    print("\n" + "=" * 50)
    print("✅ اكتمل اختبار SerpAPI بنجاح!")
    
    # عرض ملخص نهائي
    final_stats = serpapi_search.get_usage_stats()
    print(f"\n📊 الملخص النهائي:")
    print(f"   • إجمالي البحثات: {final_stats['total_searches']}")
    print(f"   • معدل النجاح: {final_stats['success_rate']:.1f}%")
    print(f"   • الحالة: {'مفعل' if final_stats['enabled'] else 'معطل'}")

async def test_serpapi_vs_google():
    """مقارنة بين SerpAPI و Google Search التقليدي"""
    
    print("\n🆚 مقارنة SerpAPI مع Google Search التقليدي")
    print("=" * 60)
    
    test_query = "gaming news today"
    
    # اختبار SerpAPI
    print("\n🚀 اختبار SerpAPI...")
    start_time = asyncio.get_event_loop().time()
    serpapi_results = await serpapi_search.search(test_query, num_results=5)
    serpapi_time = asyncio.get_event_loop().time() - start_time
    
    print(f"SerpAPI:")
    print(f"   • النتائج: {len(serpapi_results)}")
    print(f"   • الوقت: {serpapi_time:.2f} ثانية")
    print(f"   • الحالة: {'نجح' if serpapi_results else 'فشل'}")
    
    if serpapi_results:
        print(f"   • أول نتيجة: {serpapi_results[0].get('title', '')[:50]}...")
    
    # محاولة اختبار Google Search التقليدي (إذا كان متاحاً)
    try:
        from modules.web_search import WebSearch
        from config.settings import BotConfig
        
        if BotConfig.GOOGLE_SEARCH_ENGINE_ID:
            print("\n🔍 اختبار Google Search التقليدي...")
            web_search = WebSearch(BotConfig.GOOGLE_SEARCH_ENGINE_ID)
            
            start_time = asyncio.get_event_loop().time()
            google_results = web_search.search(test_query, num_results=5)
            google_time = asyncio.get_event_loop().time() - start_time
            
            print(f"Google Search:")
            print(f"   • النتائج: {len(google_results) if google_results else 0}")
            print(f"   • الوقت: {google_time:.2f} ثانية")
            print(f"   • الحالة: {'نجح' if google_results else 'فشل'}")
            
            if google_results:
                print(f"   • أول نتيجة: {google_results[0].get('title', '')[:50]}...")
        else:
            print("\n⚠️ Google Search غير مكون")
            
    except Exception as e:
        print(f"\n❌ خطأ في اختبار Google Search: {e}")
    
    print("\n💡 التوصية:")
    if serpapi_results:
        print("   ✅ SerpAPI يعمل بشكل ممتاز - يُنصح بالاستخدام")
    else:
        print("   ⚠️ SerpAPI لا يعمل - تحقق من المفتاح والإعدادات")

if __name__ == "__main__":
    print("🧪 بدء اختبار SerpAPI...")
    
    try:
        # تشغيل الاختبارات
        asyncio.run(test_serpapi())
        asyncio.run(test_serpapi_vs_google())
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        logger.error(f"خطأ في اختبار SerpAPI: {e}")
