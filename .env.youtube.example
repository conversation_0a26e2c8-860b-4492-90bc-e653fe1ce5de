# إعدادات نظام YouTube المتقدم مع الأولوية والموافقة
# انسخ هذا الملف إلى .env وأضف المفاتيح الصحيحة

# ===== المفاتيح الأساسية =====
GEMINI_API_KEY=your_gemini_api_key_here
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHANNEL_ID=@your_channel_or_chat_id_here

# ===== مفاتيح YouTube والنظام الجديد =====
# مفتاح YouTube Data API (مطلوب للنظام الجديد)
YOUTUBE_API_KEY=your_youtube_api_key_here

# إعدادات Whisper API على Hugging Face
WHISPER_API_URL=https://nanami34-ai55.hf.space/api/transcribe
WHISPER_API_KEY=whisper-hf-spaces-2025
HF_TOKEN=*************************************

# ===== إعدادات فلترة الفيديوهات =====
# الحد الأقصى لمدة الفيديو (بالدقائق) - لا يتجاوز 30 دقيقة
MAX_VIDEO_DURATION_MINUTES=30

# الحد الأقصى لعمر الفيديو (بالأيام)
MAX_VIDEO_AGE_DAYS=60

# ===== إعدادات نظام الموافقة =====
# مهلة انتظار الموافقة (بالدقائق)
APPROVAL_TIMEOUT_MINUTES=5

# الموافقة التلقائية عند انتهاء المهلة (true/false)
AUTO_APPROVE_ON_TIMEOUT=true

# ===== مفاتيح Blogger =====
BLOGGER_CLIENT_ID=your_blogger_client_id_here
BLOGGER_CLIENT_SECRET=your_blogger_client_secret_here
BLOGGER_BLOG_ID=your_blog_id_here

# ===== مفاتيح Google Search =====
GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id_here
GOOGLE_SEARCH_KEY=your_google_search_key_here

# قائمة مفاتيح Google API متعددة (مفصولة بفواصل)
GOOGLE_API_KEYS_LIST=key1,key2,key3,key4,key5

# ===== مفاتيح APIs الأخبار المتقدمة =====
NEWSAPI_KEY=your_newsapi_key_here
NEWSDATA_KEY=pub_6a04788f4edc429a8fb798dc3af6a6fb
THENEWSAPI_KEY=your_thenewsapi_key_here
GNEWS_KEY=your_gnews_key_here

# ===== مفاتيح البحث المتقدم =====
BRAVE_SEARCH_KEY=your_brave_search_key_here
SERPAPI_KEY=8b221d23f3aa037d438db307927f904933ae3037
RAPIDAPI_KEY=**************************************************

# ===== مفاتيح Tavily للبحث العميق =====
TAVILY_API_KEY_1=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
TAVILY_API_KEY_2=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

# ===== مفاتيح إنشاء الصور بالذكاء الاصطناعي =====
FREEPIK_API_KEY=FPSX1ee910637a8ec349e6d8c7f17a57740b
FLUXAI_API_KEY=b6863038ac459a1f8cd9e30d82cdd989
LEONARDO_AI_API_KEY=your_leonardo_key_here
MIDJOURNEY_API_KEY=your_midjourney_key_here

# ===== مفاتيح الصور التقليدية =====
PEXELS_API_KEY=your_pexels_key_here
PIXABAY_API_KEY=your_pixabay_key_here
UNSPLASH_ACCESS_KEY=your_unsplash_key_here

# ===== إعدادات التشغيل =====
SEARCH_INTERVAL_HOURS=3
LOG_LEVEL=INFO

# ===== ملاحظات مهمة =====
# 1. تأكد من الحصول على YouTube API Key من Google Cloud Console
# 2. فعّل YouTube Data API v3 في مشروعك
# 3. تأكد من أن Telegram Bot يمكنه إرسال رسائل للقناة/المجموعة المحددة
# 4. خدمة Whisper على Hugging Face مجانية ولكن قد تكون بطيئة
# 5. يمكنك تخصيص القنوات المراقبة في modules/advanced_youtube_analyzer.py

# ===== القنوات المراقبة حالياً =====
# 1. Saudi Gamer (عربي) - https://youtube.com/@saudigamer
# 2. LevelCap Gaming (إنجليزي) - https://youtube.com/@levelcapgaming
# 3. JorRaptor (إنجليزي) - https://youtube.com/@jorraptor
# 4. gameranx (إنجليزي) - https://youtube.com/@gameranxtv
# 5. GameSpot (إنجليزي) - https://youtube.com/@gamespot
# 6. IGN (إنجليزي) - https://youtube.com/@ign

# ===== كيفية الحصول على YouTube API Key =====
# 1. اذهب إلى Google Cloud Console (console.cloud.google.com)
# 2. أنشئ مشروع جديد أو اختر مشروع موجود
# 3. فعّل YouTube Data API v3
# 4. أنشئ credentials (API Key)
# 5. قيّد المفتاح لـ YouTube Data API فقط (للأمان)

# ===== كيفية إعداد Telegram Bot =====
# 1. تحدث مع @BotFather على Telegram
# 2. أنشئ بوت جديد بالأمر /newbot
# 3. احصل على Bot Token
# 4. أضف البوت للقناة/المجموعة كمدير
# 5. احصل على معرف القناة/المجموعة

# ===== اختبار الإعدادات =====
# بعد إضافة المفاتيح، شغّل:
# python quick_test_youtube_system.py
