#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار القنوات الجديدة المحدثة
"""

import asyncio
import sys
import os

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.advanced_youtube_analyzer import AdvancedYouTubeAnalyzer
from modules.database import db

async def test_new_channels():
    """اختبار القنوات الجديدة"""
    print("🎥 اختبار القنوات الجديدة المحدثة")
    print("=" * 60)
    
    try:
        # إنشاء محلل YouTube
        analyzer = AdvancedYouTubeAnalyzer()
        
        print(f"📺 عدد القنوات الجديدة: {len(analyzer.priority_channels)}")
        print()
        
        # عرض القنوات الجديدة
        print("📋 القنوات المحدثة:")
        print("-" * 40)
        
        for i, channel in enumerate(analyzer.priority_channels, 1):
            print(f"{i}. 📺 {channel['name']}")
            print(f"   🔗 {channel['url']}")
            print(f"   🌐 اللغة: {channel['language']}")
            print(f"   ⭐ الأولوية: {channel['priority']}")
            print(f"   🆔 المعرف: {channel.get('id', 'سيتم استخراجه')}")
            print()
        
        # اختبار استخراج معرفات القنوات
        print("🔍 اختبار استخراج معرفات القنوات...")
        print("-" * 45)
        
        successful_extractions = 0
        total_channels = len(analyzer.priority_channels)
        
        for i, channel in enumerate(analyzer.priority_channels, 1):
            print(f"\n{i}. اختبار قناة: {channel['name']}")
            
            if channel.get('id'):
                print(f"   ✅ المعرف موجود مسبقاً: {channel['id']}")
                successful_extractions += 1
                continue
            
            if channel['url'].startswith('https://youtube.com/@'):
                try:
                    print(f"   🔍 محاولة استخراج المعرف من: {channel['url']}")
                    
                    channel_id = analyzer._extract_channel_id_from_handle(channel['url'])
                    
                    if channel_id:
                        print(f"   ✅ تم استخراج المعرف بنجاح: {channel_id}")
                        channel['id'] = channel_id
                        successful_extractions += 1
                    else:
                        print(f"   ❌ فشل في استخراج المعرف")
                        
                except Exception as e:
                    print(f"   ⚠️ خطأ في الاستخراج: {e}")
            else:
                print(f"   ⚠️ تنسيق رابط غير مدعوم: {channel['url']}")
        
        # عرض النتائج
        print(f"\n📊 نتائج استخراج المعرفات:")
        print(f"   ✅ نجح: {successful_extractions}/{total_channels}")
        print(f"   📈 معدل النجاح: {(successful_extractions/total_channels)*100:.1f}%")
        
        # اختبار البحث عن فيديوهات (للقنوات التي تم استخراج معرفاتها)
        print(f"\n🎬 اختبار البحث عن فيديوهات...")
        print("-" * 35)
        
        videos_found = 0
        channels_tested = 0
        
        for channel in analyzer.priority_channels:
            if channel.get('id'):
                channels_tested += 1
                print(f"\n🔍 البحث في قناة: {channel['name']}")
                
                try:
                    videos = await analyzer._get_channel_latest_videos(channel['id'], channel['language'])
                    
                    if videos:
                        print(f"   ✅ تم العثور على {len(videos)} فيديو")
                        videos_found += len(videos)
                        
                        # عرض أول فيديو كمثال
                        if videos:
                            video = videos[0]
                            print(f"   📹 مثال: {video.get('title', 'بدون عنوان')[:50]}...")
                            print(f"   📅 تاريخ النشر: {video.get('published_at', 'غير محدد')}")
                    else:
                        print(f"   📭 لم يتم العثور على فيديوهات")
                        
                except Exception as e:
                    print(f"   ❌ خطأ في البحث: {e}")
                    
                # انتظار قصير بين الطلبات
                await asyncio.sleep(1)
        
        print(f"\n📊 نتائج البحث عن الفيديوهات:")
        print(f"   📺 قنوات مختبرة: {channels_tested}")
        print(f"   🎬 فيديوهات موجودة: {videos_found}")
        
        # اختبار البحث عن فيديو مناسب
        print(f"\n🎯 اختبار البحث عن فيديو مناسب...")
        print("-" * 40)
        
        try:
            suitable_video = await analyzer.find_latest_gaming_video()
            
            if suitable_video:
                print("✅ تم العثور على فيديو مناسب:")
                print(f"   📺 العنوان: {suitable_video.get('title', 'غير محدد')}")
                print(f"   🏷️ القناة: {suitable_video.get('channel_info', {}).get('name', 'غير محدد')}")
                print(f"   🔗 الرابط: https://youtube.com/watch?v={suitable_video.get('id', '')}")
                print(f"   📅 تاريخ النشر: {suitable_video.get('published_at', 'غير محدد')}")
                
                # فحص ما إذا كان الفيديو تم معالجته من قبل
                is_processed = db.is_video_processed(suitable_video['id'])
                print(f"   🔍 حالة المعالجة: {'معالج مسبقاً' if is_processed else 'جديد'}")
                
            else:
                print("❌ لم يتم العثور على فيديو مناسب")
                print("   💡 الأسباب المحتملة:")
                print("   - جميع الفيديوهات الحديثة تم معالجتها")
                print("   - لا توجد فيديوهات تستوفي المعايير (مدة، تاريخ، محتوى)")
                print("   - مشاكل في الوصول للقنوات")
                
        except Exception as e:
            print(f"❌ خطأ في البحث عن فيديو مناسب: {e}")
        
        # عرض إحصائيات قاعدة البيانات
        print(f"\n💾 إحصائيات قاعدة البيانات:")
        print("-" * 30)
        
        try:
            stats = db.get_video_processing_stats(7)
            if stats:
                print(f"   📹 فيديوهات معالجة (7 أيام): {stats['total_videos_processed']}")
                print(f"   📰 أخبار مستخرجة: {stats['total_news_extracted']}")
                print(f"   📺 قنوات فريدة: {stats['unique_channels']}")
                print(f"   ✅ معدل الموافقة: {stats['approval_rate']:.1f}%")
            else:
                print("   📊 لا توجد إحصائيات متاحة")
        except Exception as e:
            print(f"   ❌ خطأ في الإحصائيات: {e}")
        
        print("\n" + "=" * 60)
        print("✅ اكتمل اختبار القنوات الجديدة")
        
        # تقييم عام
        if successful_extractions >= total_channels * 0.5:  # 50% على الأقل
            print("🎉 النتيجة: النظام يعمل بشكل جيد مع القنوات الجديدة")
        else:
            print("⚠️ النتيجة: يحتاج تحسين - معدل نجاح منخفض في استخراج المعرفات")
        
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_specific_channel(channel_url: str, channel_name: str):
    """اختبار قناة محددة"""
    print(f"\n🔍 اختبار قناة محددة: {channel_name}")
    print(f"🔗 الرابط: {channel_url}")
    
    analyzer = AdvancedYouTubeAnalyzer()
    
    try:
        if channel_url.startswith('https://youtube.com/@'):
            channel_id = analyzer._extract_channel_id_from_handle(channel_url)
            if channel_id:
                print(f"✅ معرف القناة: {channel_id}")
                
                videos = await analyzer._get_channel_latest_videos(channel_id, 'en')
                print(f"📹 عدد الفيديوهات: {len(videos)}")
                
                if videos:
                    print("🎬 أحدث فيديو:")
                    video = videos[0]
                    print(f"   العنوان: {video.get('title', 'غير محدد')}")
                    print(f"   التاريخ: {video.get('published_at', 'غير محدد')}")
            else:
                print("❌ فشل في استخراج معرف القناة")
        else:
            print("⚠️ تنسيق رابط غير مدعوم")
            
    except Exception as e:
        print(f"❌ خطأ: {e}")

async def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار القنوات الجديدة")
    print("=" * 70)
    
    # اختبار شامل للقنوات الجديدة
    success = await test_new_channels()
    
    if success:
        print("\n🎊 تم الاختبار بنجاح!")
        print("💡 يمكنك الآن تشغيل البوت الرئيسي: python main.py")
    else:
        print("\n⚠️ هناك مشاكل تحتاج لحل")

if __name__ == "__main__":
    asyncio.run(main())
