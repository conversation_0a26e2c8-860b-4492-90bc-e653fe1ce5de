import sqlite3
import os

# Define the path to the database and the article file
db_path = os.path.join('data', 'articles.db')
article_path = 'revised_article.md'
article_id_to_update = 1 # Assuming we are updating the first article

# Read the content of the revised article
try:
    with open(article_path, 'r', encoding='utf-8') as f:
        new_content = f.read()
except FileNotFoundError:
    print(f"Error: The file {article_path} was not found.")
    exit()

# Connect to the SQLite database
try:
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # Check if the article with the given ID exists
    cursor.execute("SELECT id FROM articles WHERE id = ?", (article_id_to_update,))
    article = cursor.fetchone()

    if article:
        # Update the article content
        cursor.execute("""
            UPDATE articles
            SET content = ?, status = 'revised'
            WHERE id = ?
        """, (new_content, article_id_to_update))
        print(f"Article with ID {article_id_to_update} has been successfully updated.")
    else:
        # If the article does not exist, insert it as a new one
        cursor.execute("""
            INSERT INTO articles (id, title, content, status)
            VALUES (?, ?, ?, ?)
        """, (article_id_to_update, "مراجعة ألعاب: من عوالم Death Stranding 2 الغامضة إلى قتال Grime الصعب", new_content, 'revised'))
        print(f"Article with ID {article_id_to_update} was not found. A new article has been inserted.")

    # Commit the changes and close the connection
    conn.commit()
    conn.close()

except sqlite3.Error as e:
    print(f"Database error: {e}")
except Exception as e:
    print(f"An error occurred: {e}")
