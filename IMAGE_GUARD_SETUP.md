# دليل إعداد ImageGuard Pro - نظام الصور الآمنة 🖼️

## نظرة عامة
ImageGuard Pro هو نظام متقدم للبحث عن الصور الآمنة والقانونية لوكيل أخبار الألعاب "أليكس". يضمن النظام الامتثال الكامل لسياسات AdSense وحقوق الطبع والنشر.

## ✨ الميزات الرئيسية

### 🛡️ الأمان والقانونية
- **رخص آمنة 100%**: Pexels, Pixabay, Unsplash
- **متوافق مع AdSense**: فلترة تلقائية للمحتوى غير المناسب
- **استخدام تجاري مجاني**: جميع الصور مسموحة للاستخدام التجاري
- **نسب تلقائية**: إسناد صحيح للمصورين

### 🔍 البحث الذكي
- **3 مصادر موثوقة**: Pexels (أولوية عالية) → Pixabay → Unsplash
- **كلمات آمنة**: فلترة تلقائية للكلمات المحظورة
- **صور احتياطية**: نظام fallback للحالات الطارئة
- **جودة مضمونة**: حد أدنى للأبعاد والجودة

## 🔧 متطلبات الإعداد

### 1. الحصول على مفاتيح APIs (مجانية)

#### 🟢 Pexels API (الأولوية العالية)
```bash
# 1. اذهب إلى: https://www.pexels.com/api/
# 2. أنشئ حساب مجاني
# 3. احصل على API Key
# 4. الحدود: 200 طلب/ساعة (مجاني)
```

#### 🟡 Pixabay API (الأولوية المتوسطة)
```bash
# 1. اذهب إلى: https://pixabay.com/api/docs/
# 2. أنشئ حساب مجاني
# 3. احصل على API Key
# 4. الحدود: 5000 طلب/شهر (مجاني)
```

#### 🟠 Unsplash API (احتياطي)
```bash
# 1. اذهب إلى: https://unsplash.com/developers
# 2. أنشئ تطبيق جديد
# 3. احصل على Access Key
# 4. الحدود: 50 طلب/ساعة (مجاني)
```

### 2. إضافة المفاتيح لملف البيئة

أضف المفاتيح التالية لملف `.env`:

```env
# ImageGuard Pro APIs
PEXELS_API_KEY=your_pexels_api_key_here
PIXABAY_API_KEY=your_pixabay_api_key_here
UNSPLASH_ACCESS_KEY=your_unsplash_access_key_here
```

## 🚀 كيفية الاستخدام

### الاستخدام الأساسي
```python
from modules.image_guard import image_guard

# البحث عن صورة آمنة
result = await image_guard.search_safe_image(
    prompt="صورة جهاز تحكم ألعاب",
    article_topic="مراجعة PlayStation 5"
)

if result:
    print(f"URL: {result['url']}")
    print(f"المصدر: {result['source']}")
    print(f"الرخصة: {result['license']}")
    print(f"النسب: {result['attribution']}")
```

### الاستخدام المتقدم
```python
# الحصول على بيانات وصفية كاملة
image_data = await image_guard.search_safe_image(prompt, article_topic)

if image_data:
    # معلومات الصورة
    url = image_data['url']
    description = image_data['description']
    
    # معلومات الأمان
    safe_for_adsense = image_data['safe_for_adsense']
    commercial_use = image_data['commercial_use']
    
    # معلومات المصدر
    source = image_data['source']
    license_type = image_data['license']
    attribution = image_data['attribution']
```

## 🧪 اختبار النظام

### تشغيل الاختبارات
```bash
# اختبار شامل للنظام
python test_image_guard.py
```

### اختبارات يدوية
```python
# اختبار البحث الأساسي
result = await image_guard.search_safe_image("gaming controller", "")

# اختبار مرشحات الأمان
result = await image_guard.search_safe_image("violent game", "")  # يجب أن يرجع صورة آمنة

# اختبار النظام الاحتياطي
result = await image_guard.search_safe_image("كلمات غير مفيدة", "")  # يجب أن يستخدم fallback
```

## 📊 مراقبة الاستخدام

### الحصول على الإحصائيات
```python
stats = image_guard.get_usage_stats()
print(f"استدعاءات Pexels: {stats['pexels_calls']}")
print(f"استدعاءات Pixabay: {stats['pixabay_calls']}")
print(f"استدعاءات Unsplash: {stats['unsplash_calls']}")
print(f"استخدام الصور الاحتياطية: {stats['fallback_used']}")
```

### إعادة تعيين الإحصائيات
```python
image_guard.reset_usage_stats()
```

## ⚠️ إرشادات الأمان

### ✅ ما يجب فعله
- استخدم الكلمات الآمنة فقط (gaming, controller, technology)
- تحقق من `safe_for_adsense` قبل الاستخدام
- أضف النسب (`attribution`) دائماً
- راقب حدود APIs

### ❌ ما يجب تجنبه
- لا تستخدم كلمات عنيفة أو غير مناسبة
- لا تتجاهل رسائل التحذير
- لا تتجاوز حدود APIs
- لا تستخدم صور بدون رخص واضحة

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. "لم يتم العثور على صورة"
```python
# الحل: تحقق من مفاتيح APIs
if not image_guard.pexels_api_key:
    print("❌ مفتاح Pexels مفقود")
```

#### 2. "تجاوز حدود API"
```python
# الحل: راقب الاستخدام
stats = image_guard.get_usage_stats()
if stats['pexels_calls'] > 180:  # قريب من الحد 200
    print("⚠️ اقتراب من حد Pexels")
```

#### 3. "صور غير مناسبة"
```python
# الحل: فحص إضافي
if not result.get('safe_for_adsense'):
    print("⚠️ الصورة قد لا تكون آمنة لـ AdSense")
```

## 📈 تحسين الأداء

### نصائح للحصول على أفضل النتائج
1. **استخدم كلمات واضحة**: "gaming controller" أفضل من "شيء للألعاب"
2. **أضف سياق**: اذكر اسم اللعبة أو النوع
3. **راقب الحدود**: وزع الطلبات على APIs مختلفة
4. **استخدم الكاش**: احفظ النتائج الجيدة لإعادة الاستخدام

## 🆘 الدعم والمساعدة

### في حالة المشاكل
1. تحقق من ملف اللوج: `logs/bot.log`
2. شغل الاختبارات: `python test_image_guard.py`
3. تحقق من الإعدادات في `config/settings.py`
4. راجع إحصائيات الاستخدام

### معلومات إضافية
- جميع الصور المستخدمة آمنة قانونياً
- النظام يدعم العربية والإنجليزية
- التحديثات التلقائية للمرشحات
- دعم كامل لـ AdSense

---

**✅ النظام جاهز للاستخدام! استمتع بصور آمنة وقانونية لمقالاتك 🎮**
