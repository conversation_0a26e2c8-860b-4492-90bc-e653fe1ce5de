# 🎥 نظام YouTube المتقدم مع الأولوية والموافقة

## 📋 نظرة عامة

تم تطوير نظام متقدم جديد يجعل الوكيل يبحث **أولاً** في قنوات YouTube المحددة قبل المصادر الأخرى، مع استخدام تقنية Whisper لاستخراج النصوص ونظام موافقة تفاعلي عبر Telegram.

## 🎯 الميزات الجديدة

### 🔥 الأولوية لـ YouTube
- **البحث الأول** في القنوات المحددة قبل أي مصدر آخر
- **5 قنوات محددة** بترتيب أولوية واضح
- **فلترة ذكية** للفيديوهات حسب المدة والتاريخ

### 🤖 استخراج النص بالذكاء الاصطناعي
- **Whisper API** على Hugging Face Spaces
- **استخراج دقيق** للنصوص من الفيديوهات
- **تحليل ذكي** للنصوص لاستخراج الأخبار

### 📱 نظام الموافقة التفاعلي
- **رسائل Telegram** مع أزرار تفاعلية
- **معاينة الفيديو** قبل المعالجة
- **خيارات متعددة**: موافقة، رفض، اختيار آخر

### 📊 تتبع شامل
- **قاعدة بيانات محسنة** لتتبع الفيديوهات
- **إحصائيات مفصلة** لأداء القنوات
- **تقارير دورية** للنتائج

## 🏗️ البنية التقنية

### الملفات الجديدة
```
modules/
├── advanced_youtube_analyzer.py    # محلل YouTube المتقدم
├── video_approval_system.py        # نظام الموافقة
└── database.py                     # محسن بجداول جديدة

test_youtube_priority_system.py     # اختبار النظام
setup_youtube_priority.py           # إعداد النظام
```

### القنوات المحددة (بالأولوية)

| الأولوية | القناة | اللغة | الوصف |
|---------|--------|-------|--------|
| 1 | Abu Reviews | عربي | مراجعات الألعاب العربية |
| 2 | Faisella | عربي | أخبار وتحليلات الألعاب |
| 3 | Nasser Gamer Zone | عربي | منطقة الألعاب مع ناصر |
| 4 | Gaming Channel | عربي | قناة ألعاب متنوعة |
| 5 | JorRaptor | إنجليزي | أخبار الألعاب الأجنبية |

## 🔄 سير العمل الجديد

```mermaid
graph TD
    A[بدء دورة البحث] --> B[البحث في قنوات YouTube]
    B --> C{فيديو مناسب؟}
    C -->|نعم| D[طلب الموافقة عبر Telegram]
    C -->|لا| E[البحث في المصادر التقليدية]
    
    D --> F{موافقة؟}
    F -->|نعم| G[استخراج النص بـ Whisper]
    F -->|لا| H[اختيار فيديو آخر أو مصادر أخرى]
    
    G --> I[تحليل النص للأخبار]
    I --> J[توليد المقالات]
    J --> K[النشر على Blogger & Telegram]
    K --> L[حفظ البيانات والإحصائيات]
    
    E --> M[معالجة المصادر التقليدية]
    H --> B
```

## ⚙️ الإعداد والتكوين

### 1. المتطلبات الأساسية

```bash
# مفاتيح API مطلوبة
YOUTUBE_API_KEY=your_youtube_api_key
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHANNEL_ID=@your_channel_or_chat_id

# إعدادات Whisper API
WHISPER_API_URL=https://nanami34-ai55.hf.space/api/transcribe
WHISPER_API_KEY=whisper-hf-spaces-2025
HF_TOKEN=*************************************
```

### 2. تشغيل الإعداد

```bash
# إعداد النظام الجديد
python setup_youtube_priority.py

# اختبار النظام
python test_youtube_priority_system.py

# تشغيل البوت مع النظام الجديد
python main.py
```

## 🎮 كيفية الاستخدام

### 1. التشغيل التلقائي
- البوت يبحث تلقائياً كل 3 ساعات
- يبدأ بقنوات YouTube المحددة
- ينتقل للمصادر الأخرى إذا لم يجد محتوى مناسب

### 2. نظام الموافقة
عند العثور على فيديو مناسب، ستصلك رسالة على Telegram:

```
🎥 فيديو مقترح للمعالجة

📺 القناة: Abu Reviews
🏷️ العنوان: أحدث أخبار الألعاب لهذا الأسبوع

⏱️ المدة: 15:30
📅 تاريخ النشر: 2025-01-20 14:30
🌐 اللغة: عربي

📝 الوصف: في هذا الفيديو نتحدث عن...

🔗 الرابط: https://youtube.com/watch?v=...

❓ هل تريد معالجة هذا الفيديو لاستخراج الأخبار منه؟

[✅ موافق] [❌ رفض] [🔄 اختر فيديو آخر]
```

### 3. خيارات الرد
- **✅ موافق**: يتم استخراج النص ومعالجة الفيديو
- **❌ رفض**: يتم البحث عن فيديو آخر أو الانتقال للمصادر الأخرى
- **🔄 اختر فيديو آخر**: يتم البحث عن فيديو آخر من نفس القناة أو القناة التالية

## 📊 الإحصائيات والمراقبة

### إحصائيات الفيديوهات
```python
# الحصول على إحصائيات آخر 7 أيام
stats = db.get_video_processing_stats(7)

print(f"الفيديوهات المعالجة: {stats['total_videos_processed']}")
print(f"الأخبار المستخرجة: {stats['total_news_extracted']}")
print(f"معدل الموافقة: {stats['approval_rate']:.1f}%")
```

### أداء القنوات
```python
# الحصول على أداء القنوات
channels = db.get_channel_performance(30)

for channel in channels:
    print(f"{channel['channel_name']}: {channel['efficiency']:.1f}% كفاءة")
```

## 🔧 التخصيص والتطوير

### إضافة قنوات جديدة
```python
# في modules/advanced_youtube_analyzer.py
self.priority_channels.append({
    'url': 'https://youtube.com/@new_channel',
    'name': 'New Gaming Channel',
    'language': 'ar',
    'priority': 6,
    'id': None
})
```

### تخصيص معايير الفلترة
```python
# تغيير الحد الأقصى لمدة الفيديو (بالثواني)
self.max_video_duration = 30 * 60  # 30 دقيقة

# تغيير الحد الأقصى لعمر الفيديو (بالأيام)
self.max_video_age_days = 90  # 3 أشهر
```

## 🐛 استكشاف الأخطاء

### مشاكل شائعة

#### 1. فشل في استخراج معرفات القنوات
```bash
⚠️ فشل في استخراج معرف القناة
```
**الحل**: تأكد من صحة مفتاح YouTube API وأن القناة موجودة

#### 2. فشل في Whisper API
```bash
❌ فشل في استخراج النص من الفيديو
```
**الحل**: تأكد من:
- صحة رابط Whisper API
- صحة HuggingFace Token
- أن الخدمة متاحة

#### 3. عدم وصول رسائل الموافقة
```bash
⚠️ نظام الموافقة غير متاح
```
**الحل**: تأكد من:
- صحة Telegram Bot Token
- صحة معرف القناة/المجموعة
- أن البوت يمكنه إرسال رسائل

### أوامر التشخيص
```bash
# اختبار النظام الكامل
python test_youtube_priority_system.py

# اختبار قاعدة البيانات
python -c "from modules.database import db; print(db.get_video_processing_stats(7))"

# اختبار محلل YouTube
python -c "from modules.advanced_youtube_analyzer import AdvancedYouTubeAnalyzer; a = AdvancedYouTubeAnalyzer(); print('OK')"
```

## 📈 التحسينات المستقبلية

### المخطط لها
- [ ] دعم تحميل الصوت المباشر من YouTube
- [ ] تحسين دقة تحليل النصوص
- [ ] إضافة المزيد من القنوات العربية
- [ ] نظام تقييم جودة الفيديوهات
- [ ] تكامل مع المزيد من خدمات الترجمة

### اقتراحات للتطوير
- إضافة نظام تصويت للفيديوهات المقترحة
- تحليل تعليقات الفيديوهات للحصول على رؤى إضافية
- نظام تنبيهات للقنوات النشطة
- تكامل مع منصات أخرى (TikTok, Instagram)

## 📞 الدعم والمساعدة

### الإبلاغ عن المشاكل
عند الإبلاغ عن مشكلة، يرجى تضمين:
- رسائل الخطأ من السجلات
- خطوات إعادة تكرار المشكلة
- إعدادات النظام ذات الصلة

### طلب ميزات جديدة
نرحب بالاقتراحات لتحسين النظام:
- قنوات جديدة للمراقبة
- تحسينات في دقة التحليل
- ميزات إضافية للموافقة

---

🎉 **النظام الجديد جاهز للاستخدام!** 
🚀 **ابدأ بتشغيل**: `python main.py`
