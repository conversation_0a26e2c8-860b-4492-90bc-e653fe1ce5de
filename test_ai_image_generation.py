# اختبار نظام إنشاء الصور بالذكاء الاصطناعي
import asyncio
import sys
import os
import json
from datetime import datetime

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.image_guard import ai_image_generator
from modules.content_generator import ContentGenerator

async def test_ai_image_generation():
    """اختبار شامل لنظام إنشاء الصور بالذكاء الاصطناعي"""
    
    print("🎨 بدء اختبار نظام إنشاء الصور بالذكاء الاصطناعي")
    print("=" * 60)
    
    try:
        # 1. اختبار APIs
        print("\n🔍 اختبار توفر APIs...")
        api_test_results = await ai_image_generator.test_image_generation_apis()
        
        print(f"📊 نتائج اختبار APIs:")
        print(f"   • Freepik: {'✅ متوفر' if api_test_results['freepik']['available'] else '❌ غير متوفر'}")
        if api_test_results['freepik']['error']:
            print(f"     خطأ: {api_test_results['freepik']['error']}")
            
        print(f"   • FluxAI: {'✅ متوفر' if api_test_results['fluxai']['available'] else '❌ غير متوفر'}")
        if api_test_results['fluxai']['error']:
            print(f"     خطأ: {api_test_results['fluxai']['error']}")
            
        print(f"   • الحالة العامة: {'✅ جاهز' if api_test_results['overall_status'] else '❌ غير جاهز'}")
        
        # 2. اختبار إنشاء صور لمقال تجريبي
        print("\n🎮 اختبار إنشاء صور لمقال عن الألعاب...")
        
        test_article = {
            'title': 'أفضل ألعاب 2025 المنتظرة - دليل شامل للاعبين',
            'content': '''
            يشهد عام 2025 إطلاق العديد من الألعاب المثيرة التي ينتظرها اللاعبون حول العالم.
            من أبرز هذه الألعاب Grand Theft Auto VI و The Elder Scrolls VI و Fable الجديدة.
            
            تتميز هذه الألعاب بجرافيك متطور وقصص مشوقة وعوالم مفتوحة واسعة.
            كما تدعم تقنيات الذكاء الاصطناعي الحديثة لتحسين تجربة اللعب.
            ''',
            'keywords': ['gaming', 'video games', 'GTA 6', 'Elder Scrolls', 'Fable', '2025 games'],
            'category': 'gaming_news'
        }
        
        # إنشاء صور للمقال
        generated_images = await ai_image_generator.generate_article_images(test_article, 3)
        
        if generated_images:
            print(f"✅ تم إنشاء {len(generated_images)} صورة بنجاح!")
            
            for i, image in enumerate(generated_images, 1):
                print(f"\n📸 الصورة {i}:")
                print(f"   • الرابط: {image.get('url', 'غير متوفر')}")
                print(f"   • الوصف: {image.get('description', 'غير متوفر')[:80]}...")
                print(f"   • المصدر: {image.get('source', 'غير متوفر')}")
                print(f"   • الرخصة: {image.get('license', 'غير متوفر')}")
                print(f"   • طريقة الإنشاء: {image.get('generation_method', 'غير متوفر')}")
                print(f"   • آمن لـ AdSense: {'✅' if image.get('safe_for_adsense', False) else '❌'}")
        else:
            print("❌ فشل في إنشاء الصور")
        
        # 3. اختبار النظام الشامل للصور
        print("\n🎨 اختبار النظام الشامل للصور الاحترافية...")
        
        content_generator = ContentGenerator()
        professional_visuals = await content_generator.create_professional_article_visuals(test_article)
        
        print(f"📊 نتائج النظام الشامل:")
        print(f"   • الصور الرئيسية: {len(professional_visuals['main_images'])}")
        print(f"   • الصورة المصغرة: {'✅' if professional_visuals['thumbnail'] else '❌'}")
        print(f"   • صور وسائل التواصل: {len(professional_visuals['social_media_images'])}")
        
        # 4. عرض الإحصائيات
        print("\n📈 إحصائيات الإنشاء:")
        stats = ai_image_generator.get_generation_stats()
        
        print(f"   • إجمالي الصور المنشأة: {stats['total_images_created']}")
        print(f"   • نجح: {stats['successful_generations']}")
        print(f"   • فشل: {stats['failed_generations']}")
        print(f"   • معدل النجاح: {stats['success_rate']:.1f}%")
        print(f"   • Freepik: {stats['freepik_generations']} صورة")
        print(f"   • FluxAI: {stats['fluxai_generations']} صورة")
        
        # 5. حفظ تقرير الاختبار
        test_report = {
            'timestamp': datetime.now().isoformat(),
            'api_test_results': api_test_results,
            'generated_images_count': len(generated_images),
            'professional_visuals_summary': {
                'main_images': len(professional_visuals['main_images']),
                'has_thumbnail': bool(professional_visuals['thumbnail']),
                'social_media_images': len(professional_visuals['social_media_images'])
            },
            'generation_stats': stats,
            'test_status': 'success' if generated_images else 'failed'
        }
        
        with open('test_ai_image_generation_report.json', 'w', encoding='utf-8') as f:
            json.dump(test_report, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 تم حفظ تقرير الاختبار في: test_ai_image_generation_report.json")
        
        # 6. النتيجة النهائية
        if api_test_results['overall_status'] and generated_images:
            print("\n🎉 نجح الاختبار! نظام إنشاء الصور بالذكاء الاصطناعي يعمل بشكل صحيح")
            return True
        else:
            print("\n⚠️ الاختبار مكتمل مع بعض المشاكل - راجع التفاصيل أعلاه")
            return False
            
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار نظام إنشاء الصور: {e}")
        print(f"\n❌ فشل الاختبار: {e}")
        return False

async def test_specific_game_images():
    """اختبار إنشاء صور لألعاب محددة"""
    
    print("\n🎮 اختبار إنشاء صور لألعاب محددة...")
    
    game_articles = [
        {
            'title': 'مراجعة Cyberpunk 2077 - عودة قوية للمستقبل',
            'content': 'لعبة Cyberpunk 2077 تعود بتحديثات جديدة ومحسنة...',
            'keywords': ['cyberpunk', 'review', 'futuristic', 'RPG'],
            'category': 'game_review'
        },
        {
            'title': 'نصائح للفوز في Fortnite - دليل المبتدئين',
            'content': 'تعلم أفضل الاستراتيجيات للفوز في Fortnite...',
            'keywords': ['fortnite', 'tips', 'battle royale', 'guide'],
            'category': 'gaming_tips'
        },
        {
            'title': 'بطولة League of Legends العالمية 2025',
            'content': 'تنطلق بطولة League of Legends العالمية...',
            'keywords': ['league of legends', 'esports', 'tournament', 'championship'],
            'category': 'esports'
        }
    ]
    
    for i, article in enumerate(game_articles, 1):
        print(f"\n🎯 اختبار {i}: {article['title'][:50]}...")
        
        images = await ai_image_generator.generate_article_images(article, 2)
        
        if images:
            print(f"   ✅ تم إنشاء {len(images)} صورة")
            for j, img in enumerate(images, 1):
                print(f"      {j}. {img.get('source', 'Unknown')}: {img.get('description', '')[:60]}...")
        else:
            print(f"   ❌ فشل في إنشاء الصور")

if __name__ == "__main__":
    print("🚀 بدء اختبار نظام إنشاء الصور بالذكاء الاصطناعي للألعاب")
    
    # تشغيل الاختبار الأساسي
    success = asyncio.run(test_ai_image_generation())
    
    if success:
        # تشغيل اختبارات إضافية
        asyncio.run(test_specific_game_images())
        
        print("\n🎉 جميع الاختبارات مكتملة!")
        print("💡 يمكنك الآن استخدام النظام في main.py")
    else:
        print("\n⚠️ يرجى مراجعة الأخطاء وإعادة المحاولة")
