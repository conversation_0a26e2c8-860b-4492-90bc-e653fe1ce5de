<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دمج Microsoft Clarity - موقع أخبار الألعاب</title>
    
    <!-- Microsoft Clarity Analytics -->
    <script type="text/javascript">
        (function(c,l,a,r,i,t,y){
            c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
            t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
            y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
        })(window, document, "clarity", "script", "sedxi61jhb");
    </script>

    <!-- تتبع أحداث مخصصة للألعاب -->
    <script type="text/javascript">
        // تتبع قراءة المقالات
        function trackArticleRead(articleTitle, gameCategory) {
            if (typeof clarity !== 'undefined') {
                clarity('set', 'article_read', articleTitle);
                clarity('set', 'game_category', gameCategory);
                clarity('event', 'article_completed');
                console.log('📊 Clarity: تم تتبع قراءة المقال -', articleTitle);
            }
        }
        
        // تتبع البحث عن الألعاب
        function trackGameSearch(searchTerm) {
            if (typeof clarity !== 'undefined') {
                clarity('set', 'search_term', searchTerm);
                clarity('event', 'game_search');
                console.log('🔍 Clarity: تم تتبع البحث -', searchTerm);
            }
        }
        
        // تتبع مشاهدة الفيديوهات
        function trackVideoView(videoTitle, gameName) {
            if (typeof clarity !== 'undefined') {
                clarity('set', 'video_title', videoTitle);
                clarity('set', 'game_name', gameName);
                clarity('event', 'video_viewed');
                console.log('🎥 Clarity: تم تتبع مشاهدة الفيديو -', videoTitle);
            }
        }
        
        // تتبع التفاعل مع المراجعات
        function trackReviewInteraction(reviewType, rating) {
            if (typeof clarity !== 'undefined') {
                clarity('set', 'review_type', reviewType);
                clarity('set', 'user_rating', rating);
                clarity('event', 'review_interaction');
                console.log('⭐ Clarity: تم تتبع تفاعل المراجعة -', reviewType, rating);
            }
        }
        
        // تتبع مشاركة المحتوى
        function trackContentShare(platform, contentType, contentTitle) {
            if (typeof clarity !== 'undefined') {
                clarity('set', 'share_platform', platform);
                clarity('set', 'content_type', contentType);
                clarity('set', 'content_title', contentTitle);
                clarity('event', 'content_shared');
                console.log('📤 Clarity: تم تتبع المشاركة -', platform, contentTitle);
            }
        }
        
        // تتبع تحميل الألعاب
        function trackGameDownload(gameName, downloadSource) {
            if (typeof clarity !== 'undefined') {
                clarity('set', 'game_name', gameName);
                clarity('set', 'download_source', downloadSource);
                clarity('event', 'game_download');
                console.log('⬇️ Clarity: تم تتبع تحميل اللعبة -', gameName);
            }
        }
        
        // تتبع تلقائي لسلوك المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Microsoft Clarity تم تحميله بنجاح');
            
            // تتبع الوقت المقضي في الصفحة
            let startTime = Date.now();
            let pageTitle = document.title;
            let pageUrl = window.location.href;
            
            // تسجيل بداية الجلسة
            if (typeof clarity !== 'undefined') {
                clarity('set', 'page_title', pageTitle);
                clarity('set', 'page_url', pageUrl);
                clarity('set', 'session_start', new Date().toISOString());
                clarity('event', 'page_loaded');
            }
            
            // تتبع الخروج من الصفحة
            window.addEventListener('beforeunload', function() {
                let timeSpent = Math.round((Date.now() - startTime) / 1000);
                if (typeof clarity !== 'undefined') {
                    clarity('set', 'time_spent_seconds', timeSpent);
                    clarity('set', 'session_end', new Date().toISOString());
                    clarity('event', 'page_exit');
                }
                console.log('⏱️ Clarity: وقت الجلسة -', timeSpent, 'ثانية');
            });
            
            // تتبع التمرير المتقدم
            let maxScroll = 0;
            let scrollMilestones = [25, 50, 75, 100];
            let reachedMilestones = [];
            
            window.addEventListener('scroll', function() {
                let scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
                
                if (scrollPercent > maxScroll) {
                    maxScroll = scrollPercent;
                    
                    // تتبع معالم التمرير
                    scrollMilestones.forEach(milestone => {
                        if (scrollPercent >= milestone && !reachedMilestones.includes(milestone)) {
                            reachedMilestones.push(milestone);
                            if (typeof clarity !== 'undefined') {
                                clarity('set', 'scroll_milestone', milestone);
                                clarity('event', 'scroll_milestone_reached');
                            }
                            console.log('📜 Clarity: وصل للتمرير', milestone + '%');
                        }
                    });
                    
                    if (typeof clarity !== 'undefined') {
                        clarity('set', 'max_scroll_percent', maxScroll);
                    }
                }
            });
            
            // تتبع النقرات على الروابط الخارجية
            document.querySelectorAll('a[href^="http"]').forEach(function(link) {
                link.addEventListener('click', function() {
                    let linkUrl = this.href;
                    let linkText = this.textContent.trim();
                    
                    if (typeof clarity !== 'undefined') {
                        clarity('set', 'external_link_url', linkUrl);
                        clarity('set', 'external_link_text', linkText);
                        clarity('event', 'external_link_click');
                    }
                    console.log('🔗 Clarity: نقر على رابط خارجي -', linkUrl);
                });
            });
            
            // تتبع النقرات على الصور
            document.querySelectorAll('img').forEach(function(img) {
                img.addEventListener('click', function() {
                    let imgSrc = this.src;
                    let imgAlt = this.alt || 'بدون وصف';
                    
                    if (typeof clarity !== 'undefined') {
                        clarity('set', 'image_src', imgSrc);
                        clarity('set', 'image_alt', imgAlt);
                        clarity('event', 'image_click');
                    }
                    console.log('🖼️ Clarity: نقر على صورة -', imgAlt);
                });
            });
            
            // تتبع استخدام البحث الداخلي
            document.querySelectorAll('input[type="search"], input[name*="search"]').forEach(function(searchInput) {
                searchInput.addEventListener('keyup', function(e) {
                    if (e.key === 'Enter' && this.value.trim()) {
                        trackGameSearch(this.value.trim());
                    }
                });
            });
            
            // تتبع النقرات على أزرار المشاركة
            document.querySelectorAll('[data-share], .share-button, .social-share').forEach(function(shareBtn) {
                shareBtn.addEventListener('click', function() {
                    let platform = this.dataset.platform || this.className.match(/\b(facebook|twitter|telegram|whatsapp)\b/)?.[0] || 'unknown';
                    let contentTitle = document.title;
                    
                    trackContentShare(platform, 'article', contentTitle);
                });
            });
            
            // تتبع تفاعل المستخدم مع الفيديوهات
            document.querySelectorAll('video, iframe[src*="youtube"], iframe[src*="vimeo"]').forEach(function(video) {
                video.addEventListener('play', function() {
                    let videoTitle = this.title || this.dataset.title || 'فيديو بدون عنوان';
                    trackVideoView(videoTitle, 'gaming');
                });
            });
            
            // تتبع الأخطاء JavaScript
            window.addEventListener('error', function(e) {
                if (typeof clarity !== 'undefined') {
                    clarity('set', 'js_error_message', e.message);
                    clarity('set', 'js_error_file', e.filename);
                    clarity('set', 'js_error_line', e.lineno);
                    clarity('event', 'javascript_error');
                }
                console.log('❌ Clarity: خطأ JavaScript -', e.message);
            });
            
            // تتبع أداء الصفحة
            window.addEventListener('load', function() {
                setTimeout(function() {
                    if (window.performance && window.performance.timing) {
                        let timing = window.performance.timing;
                        let loadTime = timing.loadEventEnd - timing.navigationStart;
                        let domReady = timing.domContentLoadedEventEnd - timing.navigationStart;
                        
                        if (typeof clarity !== 'undefined') {
                            clarity('set', 'page_load_time', loadTime);
                            clarity('set', 'dom_ready_time', domReady);
                            clarity('event', 'performance_measured');
                        }
                        console.log('⚡ Clarity: أداء الصفحة - تحميل:', loadTime + 'ms', 'DOM:', domReady + 'ms');
                    }
                }, 1000);
            });
        });
        
        // دالة لتتبع الأحداث المخصصة
        function trackCustomEvent(eventName, eventData) {
            if (typeof clarity !== 'undefined') {
                Object.keys(eventData).forEach(key => {
                    clarity('set', key, eventData[key]);
                });
                clarity('event', eventName);
                console.log('🎯 Clarity: حدث مخصص -', eventName, eventData);
            }
        }
        
        // دالة لتتبع تفاعل المستخدم مع المحتوى
        function trackContentEngagement(contentType, action, details) {
            trackCustomEvent('content_engagement', {
                content_type: contentType,
                engagement_action: action,
                engagement_details: details,
                timestamp: new Date().toISOString()
            });
        }
    </script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
        
        .clarity-info {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .test-btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            border: none;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        
        .demo-content {
            margin: 30px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        
        .search-demo {
            margin: 20px 0;
        }
        
        .search-demo input {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            margin-bottom: 10px;
        }
        
        .video-demo {
            text-align: center;
            margin: 20px 0;
        }
        
        .share-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }
        
        .share-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .share-btn.facebook { background: #3b5998; }
        .share-btn.twitter { background: #1da1f2; }
        .share-btn.telegram { background: #0088cc; }
        .share-btn.whatsapp { background: #25d366; }
        
        .status-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #27ae60;
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 14px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body>
    <div class="status-indicator">
        🟢 Microsoft Clarity نشط
    </div>
    
    <div class="container">
        <h1>🎮 دمج Microsoft Clarity - موقع أخبار الألعاب</h1>
        
        <div class="clarity-info">
            <h2>📊 معلومات Microsoft Clarity</h2>
            <p><strong>Clarity ID:</strong> sedxi61jhb</p>
            <p><strong>الحالة:</strong> نشط ويتتبع جميع التفاعلات</p>
            <p><strong>الميزات المفعلة:</strong></p>
            <ul>
                <li>تتبع قراءة المقالات</li>
                <li>تتبع البحث عن الألعاب</li>
                <li>تتبع مشاهدة الفيديوهات</li>
                <li>تتبع التفاعل مع المراجعات</li>
                <li>تتبع مشاركة المحتوى</li>
                <li>تتبع تحميل الألعاب</li>
                <li>تتبع أداء الصفحة</li>
            </ul>
        </div>
        
        <div class="demo-content">
            <h2>🧪 اختبار تتبع الأحداث</h2>
            <p>استخدم الأزرار التالية لاختبار تتبع الأحداث المختلفة:</p>
            
            <div class="test-buttons">
                <button class="test-btn" onclick="trackArticleRead('أحدث أخبار Minecraft 2025', 'minecraft')">
                    📰 تتبع قراءة مقال
                </button>
                
                <button class="test-btn" onclick="trackVideoView('شرح أسرار Fortnite', 'fortnite')">
                    🎥 تتبع مشاهدة فيديو
                </button>
                
                <button class="test-btn" onclick="trackReviewInteraction('game_review', 5)">
                    ⭐ تتبع تقييم لعبة
                </button>
                
                <button class="test-btn" onclick="trackGameDownload('Minecraft', 'official_website')">
                    ⬇️ تتبع تحميل لعبة
                </button>
                
                <button class="test-btn" onclick="trackContentEngagement('article', 'like', 'gaming_news')">
                    👍 تتبع إعجاب
                </button>
                
                <button class="test-btn" onclick="trackCustomEvent('user_achievement', {achievement: 'first_visit', level: 'beginner'})">
                    🏆 تتبع إنجاز مخصص
                </button>
            </div>
        </div>
        
        <div class="search-demo">
            <h3>🔍 اختبار البحث</h3>
            <input type="search" placeholder="ابحث عن لعبة... (اضغط Enter)" name="game_search">
        </div>
        
        <div class="video-demo">
            <h3>🎬 فيديو تجريبي</h3>
            <video controls width="400" title="فيديو أخبار الألعاب التجريبي">
                <source src="demo-video.mp4" type="video/mp4">
                متصفحك لا يدعم تشغيل الفيديو.
            </video>
        </div>
        
        <div class="share-buttons">
            <h3>📤 اختبار المشاركة:</h3>
            <button class="share-btn facebook" data-platform="facebook" onclick="trackContentShare('facebook', 'article', document.title)">
                Facebook
            </button>
            <button class="share-btn twitter" data-platform="twitter" onclick="trackContentShare('twitter', 'article', document.title)">
                Twitter
            </button>
            <button class="share-btn telegram" data-platform="telegram" onclick="trackContentShare('telegram', 'article', document.title)">
                Telegram
            </button>
            <button class="share-btn whatsapp" data-platform="whatsapp" onclick="trackContentShare('whatsapp', 'article', document.title)">
                WhatsApp
            </button>
        </div>
        
        <div class="clarity-info">
            <h2>📈 كيفية مراقبة البيانات</h2>
            <p>1. اذهب إلى <a href="https://clarity.microsoft.com" target="_blank" style="color: #ffd700;">Microsoft Clarity Dashboard</a></p>
            <p>2. سجل الدخول بحسابك</p>
            <p>3. ابحث عن مشروع بـ ID: <strong>sedxi61jhb</strong></p>
            <p>4. ستجد جميع البيانات والتحليلات هناك</p>
            
            <h3>📊 البيانات المتوفرة:</h3>
            <ul>
                <li>خرائط حرارية للنقرات</li>
                <li>تسجيلات جلسات المستخدمين</li>
                <li>تحليل سلوك التمرير</li>
                <li>إحصائيات الأداء</li>
                <li>تتبع الأخطاء</li>
                <li>تحليل التحويلات</li>
            </ul>
        </div>
    </div>
    
    <script>
        // إضافة مؤشر بصري للتتبع
        console.log('🎮 صفحة اختبار Microsoft Clarity جاهزة!');
        console.log('📊 جميع الأحداث سيتم تتبعها وإرسالها إلى Clarity');
        console.log('🔍 افتح Developer Tools لمراقبة الأحداث المرسلة');
    </script>
</body>
</html>
